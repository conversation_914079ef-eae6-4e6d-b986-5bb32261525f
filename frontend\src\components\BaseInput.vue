<template>
  <div class="form-field">
    <label v-if="label" :for="inputId" :class="labelClasses">
      {{ label }}
      <span v-if="required" class="text-red-500 ml-1">*</span>
    </label>
    
    <div class="relative">
      <div v-if="prefixIcon" class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
        <i :class="`fa-solid ${prefixIcon} text-gray-400`"></i>
      </div>
      
      <input
        :id="inputId"
        :type="type"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :class="inputClasses"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
      />
      
      <div v-if="suffixIcon || clearable" class="absolute inset-y-0 right-0 flex items-center pr-3">
        <button
          v-if="clearable && modelValue"
          type="button"
          class="text-gray-400 hover:text-gray-600 focus:outline-none"
          @click="handleClear"
        >
          <i class="fa-solid fa-times"></i>
        </button>
        <i v-else-if="suffixIcon" :class="`fa-solid ${suffixIcon} text-gray-400`"></i>
      </div>
    </div>
    
    <div v-if="error || hint" class="mt-1">
      <p v-if="error" class="text-sm text-red-600">
        <i class="fa-solid fa-exclamation-circle mr-1"></i>
        {{ error }}
      </p>
      <p v-else-if="hint" class="text-sm text-gray-500">
        {{ hint }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { cn, conditionalClass } from '@/utils/styleUtils';
import { componentPresets } from '@/styles/shared-config';

interface Props {
  modelValue?: string | number;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';
  label?: string;
  placeholder?: string;
  hint?: string;
  error?: string;
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  readonly?: boolean;
  required?: boolean;
  clearable?: boolean;
  prefixIcon?: string;
  suffixIcon?: string;
  id?: string;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  size: 'md',
  disabled: false,
  readonly: false,
  required: false,
  clearable: false
});

const emit = defineEmits<{
  'update:modelValue': [value: string | number];
  blur: [event: FocusEvent];
  focus: [event: FocusEvent];
  clear: [];
}>();

// 生成唯一ID
const inputId = computed(() => props.id || `input-${Math.random().toString(36).substr(2, 9)}`);

// 计算标签样式类
const labelClasses = computed(() => {
  return cn(
    'block text-sm font-medium mb-1',
    props.error ? 'text-red-700' : 'text-gray-700'
  );
});

// 计算输入框样式类
const inputClasses = computed(() => {
  const baseClass = componentPresets.input.base;
  const variantClass = props.error 
    ? componentPresets.input.variants.error 
    : componentPresets.input.variants.default;
  const sizeClass = componentPresets.input.sizes[props.size];
  
  const paddingClass = cn(
    props.prefixIcon ? 'pl-10' : '',
    props.suffixIcon || props.clearable ? 'pr-10' : ''
  );
  
  const stateClass = cn(
    props.disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white',
    props.readonly ? 'bg-gray-50' : ''
  );

  return cn(
    baseClass,
    variantClass,
    sizeClass,
    paddingClass,
    stateClass
  );
});

// 处理输入事件
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const value = props.type === 'number' ? Number(target.value) : target.value;
  emit('update:modelValue', value);
};

// 处理失焦事件
const handleBlur = (event: FocusEvent) => {
  emit('blur', event);
};

// 处理聚焦事件
const handleFocus = (event: FocusEvent) => {
  emit('focus', event);
};

// 处理清空事件
const handleClear = () => {
  emit('update:modelValue', '');
  emit('clear');
};
</script>

<style scoped>
.form-field {
  @apply w-full;
}

/* 数字输入框隐藏箭头 */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

/* 搜索输入框样式 */
input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration {
  -webkit-appearance: none;
}
</style>