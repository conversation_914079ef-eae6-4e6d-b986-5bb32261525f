<template>
  <div ref="chartContainer" class="w-full h-full">
    <div v-if="loading" class="w-full h-full flex items-center justify-center">
      <div class="text-gray-400">图表加载中...</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  option: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  theme: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['chart-ready', 'chart-error']);

const chartContainer = ref<HTMLElement | null>(null);
let chart: echarts.ECharts | null = null;

const initChart = () => {
  if (!chartContainer.value || props.loading) return;
  
  try {
    console.log('📊 ChartBase: 开始初始化图表...');
    console.log('📊 ChartBase: 图表配置:', props.option);
    
    // 销毁旧的图表实例
    if (chart) {
      chart.dispose();
    }
    
    // 创建新的图表实例
    chart = echarts.init(chartContainer.value, props.theme);
    chart.setOption(props.option);
    
    console.log('✅ ChartBase: 图表初始化成功');
    emit('chart-ready', chart);
  } catch (error) {
    console.error('❌ ChartBase: 初始化图表失败:', error);
    emit('chart-error', error);
  }
};

// 监听窗口大小变化，调整图表大小
const handleResize = () => {
  if (chart) {
    try {
      chart.resize();
    } catch (error) {
      console.error('调整图表大小失败:', error);
    }
  }
};

// 监听option变化，更新图表
watch(() => props.option, (newOption) => {
  if (chart && !props.loading) {
    try {
      console.log('📊 ChartBase: 更新图表配置...');
      chart.setOption(newOption);
      console.log('✅ ChartBase: 图表配置更新成功');
    } catch (error) {
      console.error('❌ ChartBase: 更新图表失败:', error);
      emit('chart-error', error);
    }
  }
}, { deep: true });

// 监听loading状态变化
watch(() => props.loading, (isLoading) => {
  if (!isLoading) {
    // 当加载完成时，初始化或更新图表
    setTimeout(() => {
      initChart();
    }, 0);
  }
});

onMounted(() => {
  if (!props.loading) {
    // 使用setTimeout确保DOM已完全渲染
    setTimeout(() => {
      initChart();
    }, 0);
  }
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  try {
    if (chart) {
      chart.dispose();
      chart = null;
    }
  } catch (error) {
    console.error('销毁图表失败:', error);
  }
  window.removeEventListener('resize', handleResize);
});
</script>