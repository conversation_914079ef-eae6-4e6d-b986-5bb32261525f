# Map Studio 前后端联调测试说明文档

## 📋 目录

- [概述](#概述)
- [测试环境配置](#测试环境配置)
- [API测试](#API测试)
- [前端单元测试](#前端单元测试)
- [集成测试](#集成测试)
- [E2E端到端测试](#E2E端到端测试)
- [性能测试](#性能测试)
- [测试数据管理](#测试数据管理)
- [故障排除](#故障排除)

## 🎯 概述

Map Studio 采用前后端分离架构，前端使用 Vue 3 + TypeScript，后端基于 .NET 9 Web API。本文档详细说明前后端联调测试的完整流程，确保系统功能的正确性和稳定性。

### 测试架构图

```mermaid
graph TB
    subgraph "测试层级"
        A[单元测试] --> B[集成测试]
        B --> C[E2E测试]
        C --> D[性能测试]
    end
    
    subgraph "前端测试"
        E[Vitest 单元测试]
        F[API Mock 测试]
        G[Playwright E2E]
    end
    
    subgraph "后端测试"
        H[xUnit 单元测试]
        I[集成测试]
        J[API 测试]
    end
    
    subgraph "测试环境"
        K[开发环境]
        L[测试环境]
        M[模拟环境]
    end
    
    A --> E
    A --> H
    B --> F
    B --> I
    C --> G
    C --> J
```

## ⚙️ 测试环境配置

### 环境要求

- **Node.js**: 18.x 或更高版本
- **.NET SDK**: 9.0 或更高版本
- **MySQL**: 8.0 或更高版本
- **Docker**: 20.x 或更高版本 (可选)

### 快速启动

#### 方式一：使用 Docker (推荐)

```bash
# 启动完整测试环境
./deploy.sh test --build

# 或者在 Windows 上
./deploy.bat test --build
```

#### 方式二：手动启动

1. **启动数据库**
```bash
# 使用 Docker 启动 MySQL
docker run --name mapstudio-mysql \
  -e MYSQL_ROOT_PASSWORD=test_password \
  -e MYSQL_DATABASE=mapstudio_test \
  -p 3306:3306 \
  -d mysql:8.0
```

2. **启动后端服务**
```bash
cd backend
./start-test-server.sh Testing 5000
# 或在 Windows 上
./start-test-server.bat Testing 5000
```

3. **启动前端服务**
```bash
cd frontend
npm install
npm run dev
```

### 环境验证

访问以下地址验证环境：

- 前端应用: http://localhost:3001
- 后端API: http://localhost:5000
- API文档: http://localhost:5000/swagger
- 健康检查: http://localhost:5000/api/system/health

## 🔌 API测试

### API配置切换

系统支持Mock数据与真实API的一键切换：

```typescript
// 在浏览器控制台中切换
import { toggleMockMode } from './config/api.config';

// 切换到Mock模式
toggleMockMode(true);

// 切换到真实API
toggleMockMode(false);
```

### 手动API测试

#### 1. 用户认证测试

```bash
# 登录测试
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }'

# 响应示例
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user-001",
    "name": "系统管理员",
    "email": "<EMAIL>",
    "role": "admin"
  }
}
```

#### 2. 租户申请测试

```bash
# 提交租户申请
curl -X POST http://localhost:5000/api/tenant/auth-applications \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "tenantName": "测试科技有限公司",
    "tenantId": "TEST_TECH_CORP",
    "contactPerson": "张测试",
    "contactEmail": "<EMAIL>",
    "contactPhone": "13800138000",
    "serviceType": "enterprise",
    "authPeriod": "1year",
    "permissionScope": "admin",
    "description": "API测试申请"
  }'
```

#### 3. 统计数据测试

```bash
# 获取日度统计
curl -X GET "http://localhost:5000/api/operation/daily-stats?viewMode=bar" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 获取操作日志
curl -X GET "http://localhost:5000/api/operation/logs?page=1&pageSize=20" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### API测试脚本

创建 `scripts/api-test.sh` 进行自动化API测试：

```bash
#!/bin/bash

# API 测试脚本
BASE_URL="http://localhost:5000/api"
TOKEN=""

# 登录获取token
login() {
    response=$(curl -s -X POST "$BASE_URL/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"email":"<EMAIL>","password":"admin123"}')
    
    TOKEN=$(echo $response | jq -r '.token')
    echo "✅ 登录成功，Token: ${TOKEN:0:20}..."
}

# 测试租户申请
test_tenant_application() {
    response=$(curl -s -X POST "$BASE_URL/tenant/auth-applications" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -d '{
            "tenantName": "API测试公司",
            "tenantId": "API_TEST_CORP",
            "contactPerson": "API测试员",
            "contactEmail": "<EMAIL>",
            "contactPhone": "13800138000",
            "serviceType": "enterprise",
            "authPeriod": "1year",
            "permissionScope": "admin"
        }')
    
    application_id=$(echo $response | jq -r '.id')
    echo "✅ 申请提交成功，ID: $application_id"
}

# 执行测试
login
test_tenant_application
```

## 🧪 前端单元测试

### 运行测试

```bash
cd frontend

# 运行所有单元测试
npm run test

# 运行特定测试文件
npm run test auth.service.test.ts

# 监听模式运行测试
npm run test:watch

# 生成覆盖率报告
npm run test:coverage
```

### 测试结构

```
frontend/src/tests/
├── test-utils.ts          # 测试工具和Mock数据
├── auth.service.test.ts   # 认证服务测试
├── tenant.service.test.ts # 租户服务测试
└── integration.test.ts    # 集成测试
```

### 编写新的测试

```typescript
// 示例：新增服务测试
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { YourService } from '../services/your.service';
import { testHooks, TestAssert } from './test-utils';

describe('YourService 测试', () => {
  beforeEach(() => {
    testHooks.beforeEach();
  });
  
  afterEach(() => {
    testHooks.afterEach();
  });
  
  it('应该成功执行某个功能', async () => {
    // Arrange
    const testData = { /* 测试数据 */ };
    
    // Act
    const result = await YourService.someMethod(testData);
    
    // Assert
    expect(result).toBeDefined();
    TestAssert.assertApiResponse(result);
  });
});
```

## 🔗 集成测试

### 运行集成测试

```bash
# 启动测试环境
./deploy.sh test

# 运行前端集成测试
cd frontend
npm run test:integration

# 运行特定集成测试
npm run test integration.test.ts
```

### 集成测试场景

#### 1. 完整的租户申请流程
```typescript
test('完整的租户申请流程', async () => {
  // 1. 用户登录
  const loginResult = await AuthService.login({
    email: '<EMAIL>',
    password: 'admin123'
  });
  
  // 2. 提交申请
  const applicationData = TestDataGenerator.generateApplicationData();
  const submitResult = await TenantService.submitAuthApplication(applicationData);
  
  // 3. 查看申请详情
  const detailResult = await TenantService.getAuthApplicationDetail(submitResult.id);
  
  // 4. 审批申请
  const approvalResult = await TenantService.approveAuthApplication(
    submitResult.id,
    { action: 'approve', comments: '测试审批通过' }
  );
  
  // 验证结果
  expect(approvalResult.status).toBe('Approved');
});
```

#### 2. API错误处理测试
```typescript
test('API错误处理', async () => {
  // 测试网络错误
  await expect(
    TenantService.getAuthApplicationDetail('INVALID_ID')
  ).rejects.toThrow();
  
  // 测试认证失败
  localStorage.removeItem('auth_token');
  await expect(
    TenantService.getAuthApplications({ page: 1, pageSize: 10 })
  ).rejects.toThrow();
});
```

## 🎭 E2E端到端测试

### 配置和运行

```bash
# 安装 Playwright 浏览器
npx playwright install

# 运行 E2E 测试
npm run test:e2e

# 运行特定测试文件
npx playwright test tenant-workflow.spec.ts

# 调试模式运行
npx playwright test --debug

# 生成测试报告
npx playwright show-report
```

### E2E测试配置

在 `playwright-e2e.config.ts` 中配置：

```typescript
export default defineConfig({
  testDir: './src/tests/e2e',
  use: {
    baseURL: 'http://localhost:3001',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
  },
  webServer: [
    {
      command: 'npm run dev',
      port: 3001,
    },
    {
      command: 'cd ../backend && dotnet run --environment Testing',
      port: 5000,
    }
  ]
});
```

### E2E测试场景

#### 1. 租户申请完整流程
```typescript
test('租户申请完整流程', async ({ page }) => {
  // 登录
  await page.goto('/login');
  await page.fill('[data-testid="email"]', '<EMAIL>');
  await page.fill('[data-testid="password"]', 'admin123');
  await page.click('[data-testid="login-btn"]');
  
  // 提交申请
  await page.goto('/tenant/auth-apply');
  await page.fill('[data-testid="tenant-name"]', 'E2E测试公司');
  // ... 填写其他字段
  await page.click('[data-testid="submit-btn"]');
  
  // 验证成功
  await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
});
```

#### 2. 响应式设计测试
```typescript
test('响应式设计测试', async ({ page }) => {
  // 桌面端
  await page.setViewportSize({ width: 1920, height: 1080 });
  await page.goto('/dashboard');
  await expect(page.locator('[data-testid="desktop-nav"]')).toBeVisible();
  
  // 移动端
  await page.setViewportSize({ width: 375, height: 667 });
  await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();
});
```

## ⚡ 性能测试

### 页面加载性能

```typescript
test('页面加载性能', async ({ page }) => {
  const start = Date.now();
  await page.goto('/dashboard');
  await page.waitForLoadState('networkidle');
  const loadTime = Date.now() - start;
  
  expect(loadTime).toBeLessThan(3000); // 3秒内加载
});
```

### API响应时间测试

```bash
# 使用 ab 工具进行压力测试
ab -n 1000 -c 10 http://localhost:5000/api/operation/daily-stats

# 使用 curl 测试单个API响应时间
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:5000/api/auth/login
```

创建 `curl-format.txt`：
```
     time_namelookup:  %{time_namelookup}\n
        time_connect:  %{time_connect}\n
     time_appconnect:  %{time_appconnect}\n
    time_pretransfer:  %{time_pretransfer}\n
       time_redirect:  %{time_redirect}\n
  time_starttransfer:  %{time_starttransfer}\n
                     ----------\n
          time_total:  %{time_total}\n
```

## 📊 测试数据管理

### 测试数据初始化

```bash
# 初始化测试数据
curl -X POST http://localhost:5000/api/test/initialize \
  -H "Content-Type: application/json" \
  -d '{"mode": "integration-test"}'

# 清理测试数据
curl -X POST http://localhost:5000/api/test/cleanup \
  -H "Content-Type: application/json" \
  -d '{"mode": "integration-test"}'

# 重置测试数据
curl -X POST http://localhost:5000/api/test/reset
```

### 生成测试数据

```typescript
// 使用数据生成器
import { TestDataGenerator } from './test-utils';

// 生成租户申请数据
const applicationData = TestDataGenerator.generateApplicationData();

// 生成随机用户数据
const email = TestDataGenerator.randomEmail();
const phone = TestDataGenerator.randomPhone();
```

### 测试数据隔离

每个测试使用独立的数据：

```typescript
describe('数据隔离测试', () => {
  let testDataId: string;
  
  beforeEach(async () => {
    // 为每个测试创建独立数据
    testDataId = await createTestData();
  });
  
  afterEach(async () => {
    // 清理测试数据
    await cleanupTestData(testDataId);
  });
});
```

## 🔍 测试报告

### 生成测试报告

```bash
# 前端测试报告
npm run test:coverage
open coverage/index.html

# E2E测试报告
npx playwright show-report

# 合并测试报告
npm run test:report
```

### 持续集成

在 `.github/workflows/test.yml` 中配置 CI：

```yaml
name: 测试流水线

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: test_password
          MYSQL_DATABASE: mapstudio_test
        ports:
          - 3306:3306
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '9.0.x'
    
    - name: Install dependencies
      run: |
        cd frontend && npm ci
        cd ../backend && dotnet restore
    
    - name: Run backend tests
      run: |
        cd backend && dotnet test
    
    - name: Run frontend tests
      run: |
        cd frontend && npm run test:coverage
    
    - name: Run E2E tests
      run: |
        cd frontend && npm run test:e2e
```

## 🚨 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查MySQL服务状态
docker ps | grep mysql

# 查看数据库日志
docker logs mapstudio-mysql

# 重启数据库
docker restart mapstudio-mysql
```

#### 2. API调用超时
```typescript
// 增加超时时间
const apiConfig = {
  timeout: 30000, // 30秒
  retries: 3
};
```

#### 3. 前端测试失败
```bash
# 清理 node_modules
rm -rf node_modules package-lock.json
npm install

# 更新测试快照
npm run test -- --update-snapshots
```

#### 4. E2E测试环境问题
```bash
# 重新安装浏览器
npx playwright install --force

# 检查端口占用
netstat -an | grep 3001
netstat -an | grep 5000
```

### 调试技巧

#### 1. API调试
```typescript
// 启用详细日志
import { httpClient } from './utils/http.client';
httpClient.interceptors.request.use(config => {
  console.log('Request:', config);
  return config;
});
```

#### 2. E2E调试
```bash
# 无头模式关闭，查看浏览器操作
npx playwright test --headed

# 启用调试器
npx playwright test --debug
```

#### 3. 网络问题调试
```bash
# 检查网络连通性
curl -v http://localhost:5000/api/system/health

# 查看网络请求
# 在浏览器开发者工具 Network 面板查看
```

## 📝 测试最佳实践

### 1. 测试命名规范
- 描述性名称：`应该成功提交租户申请`
- 遵循 AAA 模式：Arrange, Act, Assert
- 一个测试只验证一个功能点

### 2. 测试数据管理
- 使用工厂方法生成测试数据
- 每个测试使用独立的数据
- 测试结束后清理数据

### 3. 异步测试
```typescript
// 正确的异步测试
test('异步操作测试', async () => {
  const result = await someAsyncFunction();
  expect(result).toBeDefined();
});

// 错误的异步测试
test('错误的异步测试', () => {
  someAsyncFunction().then(result => {
    expect(result).toBeDefined(); // 可能不会执行
  });
});
```

### 4. Mock使用原则
- 只Mock外部依赖
- 保持Mock简单
- 验证Mock调用

```typescript
// 正确的Mock使用
const mockApiCall = vi.fn().mockResolvedValue({ success: true });
// 验证Mock被调用
expect(mockApiCall).toHaveBeenCalledWith(expectedParams);
```

## 📈 测试指标

### 覆盖率目标
- 单元测试覆盖率：≥ 80%
- 集成测试覆盖率：≥ 70%
- E2E测试覆盖率：≥ 60%

### 性能指标
- API响应时间：< 500ms
- 页面加载时间：< 3s
- 测试执行时间：< 10min

### 质量指标
- 测试通过率：≥ 95%
- 测试稳定性：≥ 98%
- 缺陷发现率：≥ 90%

---

## 📞 支持和反馈

如有测试相关问题，请通过以下方式联系：

- 📧 邮箱：<EMAIL>
- 🐛 问题反馈：在项目 Issue 中提交
- 📖 更多文档：查看项目 Wiki

---

*最后更新：2025年8月25日*