using MapStudio.Api.Models.DTOs;

namespace MapStudio.Api.Services.Interfaces
{
    /// <summary>
    /// 短信模板服务接口
    /// </summary>
    public interface ISmsTemplateService
    {
        /// <summary>
        /// 获取短信模板
        /// </summary>
        /// <returns>短信模板设置</returns>
        Task<SmsTemplateSettingDto> GetSmsTemplateAsync();

        /// <summary>
        /// 保存短信模板（新增或修改）
        /// </summary>
        /// <param name="request">保存短信模板请求</param>
        /// <returns>保存后的短信模板设置</returns>
        Task<SmsTemplateSettingDto> SaveSmsTemplateAsync(SaveSmsTemplateRequestDto request);
    }
}