using MapStudio.Api.Models.DTOs;
using MapStudio.Api.Models.Entities;
using MapStudio.Api.Services.Interfaces;
using SqlSugar;
using System.Collections.Generic;
using static System.Net.Mime.MediaTypeNames;
using FileEntity = MapStudio.Api.Models.Entities.File;

namespace MapStudio.Api.Services.Implementations;

public class TenantService : ITenantService
{
    private readonly ISqlSugarClient _db;
    private readonly ILogger<TenantService> _logger;

    public TenantService(ISqlSugarClient db, ILogger<TenantService> logger)
    {
        _db = db;
        _logger = logger;
    }

    public async Task<AuthApplicationResponse> SubmitAuthApplicationAsync(SubmitAuthApplicationRequest request)
    {
        try
        {
            // 生成申请ID
            var applicationId = $"TA-{DateTime.Now:yyyyMMdd}-{Guid.NewGuid().ToString("N")[..6].ToUpper()}";

            var application = new AuthApplication
            {
                Id = applicationId,
                TenantName = request.TenantName,
                TenantId = request.TenantId,
                ContactPerson = request.ContactPerson,
                ContactEmail = request.ContactEmail,
                ContactPhone = request.ContactPhone,
                ServiceType = request.ServiceType,
                AuthPeriod = request.AuthPeriod,
                PermissionScope = request.PermissionScope,
                Description = request.Description,
                BusinessLicenseFileId = request.BusinessLicense,
                OrganizationCodeFileId = request.OrganizationCode,
                Status = "Pending",
                AppliedAt = DateTime.Now
            };

            await _db.Insertable(application).ExecuteCommandAsync();

            _logger.LogInformation("租户授权申请提交成功: {ApplicationId}", applicationId);

            return new AuthApplicationResponse
            {
                Id = applicationId,
                Status = "Pending",
                AppliedAt = DateTime.Now,
                Message = "租户授权申请提交成功！您的申请已提交至管理员审批，请耐心等待。"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "提交租户授权申请失败");
            throw;
        }
    }

    public async Task<AuthApplicationListResponse> GetAuthApplicationsAsync(int page, int pageSize, string? searchTerm, string? serviceType, string? authPeriod, string? status = null)
    {
        try
        {
            var query = _db.Queryable<AuthApplication>();

            // 搜索条件
            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(a => a.TenantName.Contains(searchTerm) ||
                                        a.Id.Contains(searchTerm) ||
                                        a.ContactPerson.Contains(searchTerm));
            }

            if (!string.IsNullOrEmpty(serviceType))
            {
                query = query.Where(a => a.ServiceType == serviceType);
            }

            if (!string.IsNullOrEmpty(authPeriod))
            {
                query = query.Where(a => a.AuthPeriod == authPeriod);
            }

            // 状态筛选
            if (!string.IsNullOrEmpty(status))
            {
                query = query.Where(a => a.Status.ToLower() == status.ToLower());
            }

            var totalCount = await query.CountAsync();
            var items = await query
                .OrderBy(a => a.AppliedAt, OrderByType.Desc)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var responseItems = items.Select(item => new AuthApplicationDto
            {
                Id = item.Id,
                TenantId = item.TenantId,
                TenantName = item.TenantName,
                ApplyTime = item.AppliedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                ServiceType = item.ServiceType,
                ServiceTypeName = GetServiceTypeName(item.ServiceType),
                AuthPeriod = item.AuthPeriod,
                AuthPeriodName = GetAuthPeriodName(item.AuthPeriod),
                ContactPerson = item.ContactPerson,
                ContactPhone = item.ContactPhone,
                ContactEmail = item.ContactEmail,
                Status = item.Status.ToLower()
            }).ToList();

            return new AuthApplicationListResponse
            {
                Items = responseItems,
                TotalCount = totalCount,
                CurrentPage = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取授权申请列表失败");
            throw;
        }
    }

    public async Task<AuthApplicationDetailResponse> GetAuthApplicationDetailAsync(string id)
    {
        try
        {
            var application = await _db.Queryable<AuthApplication>()
                .Where(a => a.Id == id)
                .FirstAsync();

            if (application == null)
            {
                throw new ArgumentException("授权申请不存在");
            }

            // 获取相关文件
            var documents = new List<DocumentDto>();
            if (!string.IsNullOrEmpty(application.BusinessLicenseFileId))
            {
                var file = await _db.Queryable<FileEntity>()
                    .Where(f => f.Id == application.BusinessLicenseFileId)
                    .FirstAsync();
                if (file != null)
                {
                    documents.Add(new DocumentDto
                    {
                        Id = file.Id,
                        Name = file.OriginalFileName,
                        TypeName = "企业营业执照",
                        Size = FormatFileSize(file.FileSize),
                        UploadTime = file.UploadTime.ToString("yyyy-MM-dd HH:mm:ss"),
                        Url = $"/api/files/download/{file.Id}"
                    });
                }
            }

            return new AuthApplicationDetailResponse
            {
                ApplicationId = application.Id,
                Status = application.Status.ToLower(),
                ApplyTime = application.AppliedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                ServiceInfo = new ServiceInfoDto
                {
                    TypeName = GetServiceTypeName(application.ServiceType),
                    AuthPeriodName = GetAuthPeriodName(application.AuthPeriod),
                    PermissionScopeName = GetPermissionScopeName(application.PermissionScope),
                    Description = application.Description
                },
                TenantInfo = new TenantInfoDto
                {
                    Name = application.TenantName,
                    Id = application.TenantId,
                    ContactPerson = application.ContactPerson,
                    ContactPhone = application.ContactPhone,
                    ContactEmail = application.ContactEmail
                },
                Documents = documents,
                ApprovalHistory = new List<ApprovalHistoryDto>
                {
                    new ApprovalHistoryDto
                    {
                        Id = Guid.NewGuid().ToString(),
                        Action = "申请提交",
                        Operator = application.ContactPerson,
                        OperatorRole = "申请人",
                        Time = application.AppliedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                        Comment = "提交租户授权申请",
                        Status = "submitted"
                    }
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取授权申请详情失败: {ApplicationId}", id);
            throw;
        }
    }

    public async Task<ApprovalResponse> ApproveAuthApplicationAsync(string id, ApprovalRequest request)
    {
        try
        {
            var application = await _db.Queryable<AuthApplication>()
                .Where(a => a.Id == id)
                .FirstAsync();

            if (application == null)
            {
                throw new ArgumentException("授权申请不存在");
            }

            // 更新申请状态
            application.Status = request.Action == "approve" ? "Approved" : "Rejected";
            application.ApprovedAt = DateTime.Now;
            application.ApprovalComments = request.Comments;

            await _db.Updateable(application).ExecuteCommandAsync();

            _logger.LogInformation("授权申请审批完成: {ApplicationId}, 结果: {Action}", id, request.Action);

            return new ApprovalResponse
            {
                Id = id,
                Status = application.Status,
                ApprovedAt = application.ApprovedAt,
                Message = $"申请单 {id} 已成功{(request.Action == "approve" ? "审批通过" : "驳回")}。"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "审批授权申请失败: {ApplicationId}", id);
            throw;
        }
    }

    public async Task DeleteAuthApplicationAsync(string id)
    {
        try
        {
            await _db.Deleteable<AuthApplication>()
                .Where(a => a.Id == id)
                .ExecuteCommandAsync();

            _logger.LogInformation("删除授权申请: {ApplicationId}", id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除授权申请失败: {ApplicationId}", id);
            throw;
        }
    }

    public async Task<TenantServiceStatusResponse> GetServiceStatusAsync(string? tenantId)
    {
        try
        {
            var query = _db.Queryable<AuthApplication>()
                .Where(a => a.Status == "Approved");

            if (!string.IsNullOrEmpty(tenantId))
            {
                query = query.Where(a => a.TenantId == tenantId);
            }

            var applications = await query.ToListAsync();

            var services = applications.Select(app => new TenantServiceStatusDto
            {
                TenantId = app.TenantId,
                TenantName = app.TenantName,
                ServiceType = app.ServiceType,
                ServiceName = GetServiceTypeName(app.ServiceType),
                Status = "active",
                StatusName = "正常运行",
                ExpiryDate = CalculateExpiryDate(app.ApprovedAt ?? app.AppliedAt, app.AuthPeriod),
                RemainingDays = CalculateRemainingDays(app.ApprovedAt ?? app.AppliedAt, app.AuthPeriod)
            }).ToList();

            return new TenantServiceStatusResponse
            {
                Services = services
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取服务状态失败");
            throw;
        }
    }

    private string GetServiceTypeName(string serviceType)
    {
        return serviceType switch
        {
            "basic" => "基础地图服务",
            "advanced" => "高级地图服务",
            "enterprise" => "企业级地图服务",
            "custom" => "定制化地图服务",
            _ => serviceType
        };
    }

    private string GetAuthPeriodName(string authPeriod)
    {
        return authPeriod switch
        {
            "3months" => "3个月",
            "6months" => "6个月",
            "1year" => "1年",
            "2years" => "2年",
            "3years" => "3年",
            _ => authPeriod
        };
    }

    private string GetPermissionScopeName(string permissionScope)
    {
        return permissionScope switch
        {
            "read" => "只读权限",
            "write" => "读写权限",
            "admin" => "管理员权限",
            _ => permissionScope
        };
    }

    private string FormatFileSize(long bytes)
    {
        string[] suffixes = { "B", "KB", "MB", "GB" };
        int counter = 0;
        decimal number = bytes;
        while (Math.Round(number / 1024) >= 1)
        {
            number /= 1024;
            counter++;
        }
        return string.Format("{0:n1} {1}", number, suffixes[counter]);
    }

    private DateTime? CalculateExpiryDate(DateTime startDate, string authPeriod)
    {
        return authPeriod switch
        {
            "3months" => startDate.AddMonths(3),
            "6months" => startDate.AddMonths(6),
            "1year" => startDate.AddYears(1),
            "2years" => startDate.AddYears(2),
            "3years" => startDate.AddYears(3),
            _ => null
        };
    }

    private int CalculateRemainingDays(DateTime startDate, string authPeriod)
    {
        var expiryDate = CalculateExpiryDate(startDate, authPeriod);
        if (expiryDate == null) return -1;

        var remaining = (expiryDate.Value - DateTime.Now).Days;
        return Math.Max(0, remaining);
    }

    public async Task<TenantServiceProgressResponse> GetAuthApplicationProgressAsync(int page, int pageSize, string? searchTerm, string? serviceType, string? authPeriod, string? status = null)
    {
        try
        {
            var query = _db.Queryable<AuthApplication>();

            // 搜索条件
            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(a => a.TenantName.Contains(searchTerm) ||
                                        a.Id.Contains(searchTerm) ||
                                        a.ContactPerson.Contains(searchTerm));
            }

            if (!string.IsNullOrEmpty(serviceType))
            {
                query = query.Where(a => a.ServiceType == serviceType);
            }

            if (!string.IsNullOrEmpty(authPeriod))
            {
                query = query.Where(a => a.AuthPeriod == authPeriod);
            }

            // 状态筛选
            if (!string.IsNullOrEmpty(status))
            {
                query = query.Where(a => a.Status.ToLower() == status.ToLower());
            }

            var totalCount = await query.CountAsync();
            var items = await query
                .OrderBy(a => a.AppliedAt, OrderByType.Desc)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var responseItems = items.Select(item => new TenantServiceProgressDto
            {
                Id = item.Id,
                TenantId = item.TenantId,
                TenantName = item.TenantName,
                ApplyTime = item.AppliedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                ServiceType = item.ServiceType,
                ServiceTypeName = GetServiceTypeName(item.ServiceType),
                AuthPeriod = item.AuthPeriod,
                AuthPeriodName = GetAuthPeriodName(item.AuthPeriod),
                ContactEmail = item.ContactEmail,
                ContactPerson = item.ContactPerson,
                ContactPhone = item.ContactPhone,
                Status = item.Status.ToLower(),
                StatusName = getStepStatusName(item.Status.ToLower()),
                currentStep = item.Status.ToLower(),
                Progress = GetProgressNmuber(item.Status.ToLower()),
                steps = GetStepList(item),

            }).ToList();

            return new TenantServiceProgressResponse
            {
                Items = responseItems,
                TotalCount = totalCount,
                CurrentPage = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取授权申请列表失败");
            throw;
        }
    }

    public async Task<TenantServiceProgressDto> GetAuthApplicationProgressDetailAsync(string id)
    {
        try
        {
            var application = await _db.Queryable<AuthApplication>()
                .Where(a => a.Id == id)
                .FirstAsync();

            if (application == null)
            {
                throw new ArgumentException("授权申请不存在");
            }

            //     steps: [
            //       { id: 'submit', name: '提交申请', time: '2025-08-01 09:23:45', status: 'completed' },
            //       { id: 'review', name: '审核中', time: '2025-08-01 10:15:30', status: 'completed' },
            //       { id: 'approve', name: '审批通过', time: '2025-08-02 14:30:00', status: 'completed' },
            //       { id: 'activate', name: '服务激活', time: '2025-08-02 15:00:00', status: 'completed' },
            //     ],

            return new TenantServiceProgressDto
            {
                Id = application.Id,
                TenantId = application.TenantId,
                TenantName = application.TenantName,
                ApplyTime = application.AppliedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                ServiceType = application.ServiceType,
                ServiceTypeName = GetServiceTypeName(application.ServiceType),
                AuthPeriod = application.AuthPeriod,
                AuthPeriodName = GetAuthPeriodName(application.AuthPeriod),
                ContactEmail = application.ContactEmail,
                ContactPerson = application.ContactPerson,
                ContactPhone = application.ContactPhone,
                Status = application.Status.ToLower(),
                StatusName = getStepStatusName(application.Status.ToLower()),
                Progress = GetProgressNmuber(application.Status.ToLower()),
                currentStep = application.Status.ToLower(),
                steps = GetStepList(application),
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取授权申请列表失败");
            throw;
        }
    }

    private List<ProgressStep> GetStepList(AuthApplication application)
    {
        List<ProgressStep> steps = new List<ProgressStep>();

        Dictionary<string, string> stepMap = new Dictionary<string, string>
        {
            { "submit" , "提交申请" },
            { "review" , "审核中"},
            { "Approved", "审批通过"},
            { "rejected", "审批驳回"},
            //{ "activate" , "服务激活"},
        };

        foreach (KeyValuePair<string, string> kv in stepMap)
        {
            if (application.ApprovedAt != null && application.Status.ToLower() == kv.Key.ToLower())
            {
                steps.Add(new ProgressStep(kv.Key, kv.Value, application.ApprovedAt.Value.ToString("yyyy-MM-dd HH:mm:ss"), "completed"));
            }
            else if (kv.Key.ToLower() == "review" && application.ApprovedAt == null)
            {
                steps.Add(new ProgressStep(kv.Key, kv.Value, "", "current"));
            }
            else if (kv.Key.ToLower() == "submit")
            {
                steps.Add(new ProgressStep(kv.Key, kv.Value, application.AppliedAt.ToString("yyyy-MM-dd HH:mm:ss"), "completed"));
            }
        }
        return steps;
    }

    private string getStepStatusName(string step)
    {
        return step switch
        {
            "pending" => "待审批",
            "approved" => "已通过",
            "rejected" => "已驳回",
            "processing" => "审批中",
            _ => step
        };
    }

    private int GetProgressNmuber(string step)
    {
        int number = 0;
        return step switch
        {
            "pending" => 50,
            "approved" => 100,
            "rejected" => 0,
            "reviewing" => 60,
            "review" => 60,
            _ => number
        };
    }

    public async Task<AuthDestoryListResponse> GetAuthDestoryAsync(int page, int pageSize, string? searchTerm, string? serviceType, string? authPeriod, string? status = null)
    {
        try
        {
            var query = _db.Queryable<AuthApplication>().Where(item => item.ApprovedAt != null);

            // 搜索条件
            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(a => a.TenantName.Contains(searchTerm) ||
                                        a.Id.Contains(searchTerm) ||
                                        a.ContactPerson.Contains(searchTerm));
            }

            if (!string.IsNullOrEmpty(serviceType))
            {
                query = query.Where(a => a.ServiceType == serviceType);
            }

            if (!string.IsNullOrEmpty(authPeriod))
            {
                query = query.Where(a => a.AuthPeriod == authPeriod);
            }

            // 状态筛选
            if (!string.IsNullOrEmpty(status))
            {
                query = query.Where(a => a.Status.ToLower() == status.ToLower());
            }

            var totalCount = await query.CountAsync();
            var items = await query
                .OrderBy(a => a.AppliedAt, OrderByType.Desc)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            
            var responseItems = items.Select(item => new TenantServiceDestoryDto
            {
                Id = item.Id,
                TenantId = item.TenantId,
                TenantName = item.TenantName,
                ApplyTime = item.AppliedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                ServiceType = item.ServiceType,
                ServiceTypeName = GetServiceTypeName(item.ServiceType),
                ContactPerson = item.ContactPerson,
                ContactPhone = item.ContactPhone,
                ContactEmail = item.ContactEmail,
                Status = getDestoryStatus(item),
                StatusName = getDestoryStatusName(item),
                AuthStartTime = item.AppliedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                AuthEndTime = item.AppliedAt.AddMonths(getMonthNumberByName(item.AuthPeriod)).ToString("yyyy-MM-dd HH:mm:ss"),

            }).ToList();

            return new AuthDestoryListResponse
            {
                Items = responseItems,
                TotalCount = totalCount,
                CurrentPage = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取授权申请列表失败");
            throw;
        }
    }

    private int getMonthNumberByName(string name)
    {
        int number = 3;
        switch (name)
        {
            case "3months":
                number = 3;
                break;
            case "6months":
                number = 6;
                break;
            case "1year":
                number = 12;
                break;
            case "2years":
                number = 24;
                break;
            case "3years":
                number = 36;
                break;
        }
        return number;
    }

    private string getDestoryStatus(AuthApplication application)
    {
        string status = "active";
        DateTime endtime = application.ApprovedAt.Value.AddMonths(getMonthNumberByName(application.AuthPeriod));
        if (endtime > DateTime.Now)
        {
            status = "expired";
        }
        else if (endtime <= DateTime.Now
            && endtime > DateTime.Now.AddMonths(1))
        {
            status = "expiring";
        }

        return status;
    }

    private string getDestoryStatusName(AuthApplication application)
    {
        string status = "服务中";
        DateTime endtime = application.ApprovedAt.Value.AddMonths(getMonthNumberByName(application.AuthPeriod));
        if (DateTime.Now > endtime)
        {
            status = "已过期";
        }
        else if (DateTime.Now < endtime
            && DateTime.Now.AddMonths(1) >= endtime)
        {
            status = "即将过期";
        }

        return status;
    }

    public async Task<AuthApplication> IsValidateAsync(TenantValidateDto dto)
    {
        if(dto == null 
            || (string.IsNullOrEmpty(dto.TenantId) && string.IsNullOrEmpty(dto.TenantName)))
        {
            return null;
        }
        var application = await _db.Queryable<AuthApplication>().Where(item => item.Status != "rejected" 
            && ((item.TenantId == dto.TenantId) || item.TenantName == dto.TenantName))
            .FirstAsync();

        if (application == null)
        {
            return null;
        }

        return application;
    }
}