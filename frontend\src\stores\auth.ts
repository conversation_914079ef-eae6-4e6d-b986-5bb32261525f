import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface User {
  id: string
  name: string
  email: string
  avatar?: string
  role: string
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const isAuthenticated = ref(false)
  const loading = ref(true)
  const user = ref<User | null>(null)

  // 计算属性
  const isAdmin = computed(() => user.value?.role === 'admin')
  const userName = computed(() => user.value?.name || '未知用户')

  // 方法
  const login = async (credentials?: { email: string; password: string }) => {
    try {
      loading.value = true
      
      // 模拟登录API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟用户数据
      user.value = {
        id: '1',
        name: '管理员',
        email: credentials?.email || '<EMAIL>',
        avatar: '',
        role: 'admin'
      }
      
      isAuthenticated.value = true
      
      // 保存到本地存储
      localStorage.setItem('auth_token', 'mock_token_' + Date.now())
      localStorage.setItem('user_info', JSON.stringify(user.value))
      
      return { success: true }
    } catch (error) {
      console.error('登录失败:', error)
      return { success: false, error: '登录失败' }
    } finally {
      loading.value = false
    }
  }

  const logout = () => {
    isAuthenticated.value = false
    user.value = null
    
    // 清除本地存储
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_info')
  }

  const checkAuth = async () => {
    try {
      loading.value = true
      
      // 检查本地存储
      const token = localStorage.getItem('auth_token')
      const userInfo = localStorage.getItem('user_info')
      
      if (token && userInfo) {
        user.value = JSON.parse(userInfo)
        isAuthenticated.value = true
      } else {
        // 模拟自动登录
        await new Promise(resolve => setTimeout(resolve, 1000))
        await login()
      }
    } catch (error) {
      console.error('认证检查失败:', error)
      logout()
    } finally {
      loading.value = false
    }
  }

  const updateUser = (userData: Partial<User>) => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
      localStorage.setItem('user_info', JSON.stringify(user.value))
    }
  }

  return {
    // 状态
    isAuthenticated,
    loading,
    user,
    
    // 计算属性
    isAdmin,
    userName,
    
    // 方法
    login,
    logout,
    checkAuth,
    updateUser
  }
})