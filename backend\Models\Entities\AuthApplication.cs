using SqlSugar;

namespace MapStudio.Api.Models.Entities
{
    /// <summary>
    /// 授权申请实体，对应数据库表 ms_auth_applications
    /// </summary>
    [SugarTable("ms_auth_applications")]
    public class AuthApplication
    {
        /// <summary>
        /// 申请ID (主键)
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, Length = 36)]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 租户ID (外键)
        /// </summary>
        [SugarColumn(Length = 36, IsNullable = false)]
        public string TenantId { get; set; } = string.Empty;

        /// <summary>
        /// 租户名称
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = false)]
        public string TenantName { get; set; } = string.Empty;

        /// <summary>
        /// 联系人姓名
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        public string ContactPerson { get; set; } = string.Empty;

        /// <summary>
        /// 联系人邮箱
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = false)]
        public string ContactEmail { get; set; } = string.Empty;

        /// <summary>
        /// 联系人电话
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false)]
        public string ContactPhone { get; set; } = string.Empty;

        /// <summary>
        /// 服务类型 (basic, advanced, enterprise, custom)
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        public string ServiceType { get; set; } = string.Empty;

        /// <summary>
        /// 授权期限 (3months, 6months, 1year, 2years, 3years)
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false)]
        public string AuthPeriod { get; set; } = string.Empty;

        /// <summary>
        /// 权限范围 (read, write, admin)
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        public string PermissionScope { get; set; } = string.Empty;

        /// <summary>
        /// 申请描述
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        public string? Description { get; set; }

        /// <summary>
        /// 申请状态 (Pending, Approved, Rejected, Expired)
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false)]
        public string Status { get; set; } = "Pending";

        /// <summary>
        /// 审批用户ID
        /// </summary>
        [SugarColumn(Length = 36, IsNullable = true)]
        public string? ApprovalUserId { get; set; }

        /// <summary>
        /// 营业执照文件ID
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        public string? BusinessLicenseFileId { get; set; }

        /// <summary>
        /// 组织机构代码文件ID
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        public string? OrganizationCodeFileId { get; set; }

        /// <summary>
        /// 申请时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime AppliedAt { get; set; }

        /// <summary>
        /// 审批时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? ApprovedAt { get; set; }

        /// <summary>
        /// 审批意见
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string? ApprovalComments { get; set; }

        // 导航属性
        [SugarColumn(IsIgnore = true)]
        public Tenant? Tenant { get; set; }

        [SugarColumn(IsIgnore = true)]
        public User? ApprovalUser { get; set; }
    }
}