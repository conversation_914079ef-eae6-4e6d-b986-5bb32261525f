<template>
  <div class="p-6 space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">授权进度查询</h1>
      <p class="mt-1 text-gray-500">查询租户授权申请的处理进度</p>
    </div>

    <!-- 搜索和筛选栏 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <i class="fa-solid fa-search text-gray-400"></i>
          </div>
          <input
            type="text"
            placeholder="搜索申请单号或租户名称..."
            v-model="searchTerm"
            @input="handleSearchChange"
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
        
        <div>
          <select
            name="status"
            v-model="filters.status"
            @change="handleFilterChange"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option v-for="option in statusOptions" :key="option.id" :value="option.id">
              {{ option.name }}
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                申请单号
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                租户名称
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                申请时间
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                权限类型
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                过期时间
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                当前状态
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                进度
              </th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-if="paginatedApplications.length > 0" v-for="application in paginatedApplications" :key="application.id" class="hover:bg-gray-50 transition-colors">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-blue-600">
                  {{ application.id }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ application.tenantName }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500">{{ application.applyTime }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                  {{ application.serviceTypeName }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ application.authPeriodName }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span 
                  class="px-2 py-1 text-xs font-medium rounded-full"
                  :class="getStatusBadgeClass(application.status)"
                >
                  {{ application.statusName }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      class="h-2 rounded-full"
                      :class="application.status === 'rejected' ? 'bg-red-500' : 'bg-blue-500'"
                      :style="{ width: application.progress + '%' }"
                    ></div>
                  </div>
                  <span class="ml-2 text-sm text-gray-500">{{ application.progress }}%</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <router-link
                  :to="`/tenant/auth-progress-detail/${application.id}`"
                  class="text-blue-600 hover:text-blue-900"
                >
                  查看详情
                </router-link>
              </td>
            </tr>
            <tr v-else>
              <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                暂无数据
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- 分页 -->
      <div v-if="paginatedApplications.length > 0" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
          <button
            @click="prevPage"
            :disabled="pagination.page === 1"
            class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            上一页
          </button>
          <button
            @click="nextPage"
            :disabled="pagination.page === Math.ceil(filteredApplications.length / pagination.pageSize)"
            class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            下一页
          </button>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              显示第 <span class="font-medium">{{ (pagination.page - 1) * pagination.pageSize + 1 }}</span> 到 <span class="font-medium">{{ Math.min(pagination.page * pagination.pageSize, filteredApplications.length) }}</span> 条结果，共 <span class="font-medium">{{ filteredApplications.length }}</span> 条
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                @click="prevPage"
                :disabled="pagination.page === 1"
                class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
              >
                <span class="sr-only">上一页</span>
                <i class="fa-solid fa-chevron-left"></i>
              </button>
              <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                第 {{ pagination.page }} 页，共 {{ Math.ceil(filteredApplications.length / pagination.pageSize) }} 页
              </span>
              <button
                @click="nextPage"
                :disabled="pagination.page === Math.ceil(filteredApplications.length / pagination.pageSize)"
                class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
              >
                <span class="sr-only">下一页</span>
                <i class="fa-solid fa-chevron-right"></i>
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { cn } from '@/lib/utils'
import { TenantService } from '@/services';

// 模拟授权进度数据
const progressData = ref([] as any[]);

const loadProgressData = async () => {
    try {
          const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      searchTerm: searchTerm.value || undefined,

      status: null // 默认所有
    };

    console.log('📋 加载授权申请列表...', params);

    const result = await TenantService.getAuthProgress(params);
    progressData.value = result.items; // 正确：使用.value来修改ref的值
    console.log('📋 加载授权申请列表成功:', result.items);
  } catch (error) {
    console.error('获取申请详情失败:', error);
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadProgressData();
});
// const progressData = [
//   {
//     id: 'TA-20250801',
//     tenantName: '智慧城市科技有限公司',
//     applyTime: '2025-08-01 09:23:45',
//     serviceType: 'enterprise',
//     serviceTypeName: '企业级地图服务',
//     authPeriod: '1year',
//     authPeriodName: '1年',
//     status: 'approved',
//     statusName: '已通过',
//     progress: 100,
//     currentStep: 'completed',
//     steps: [
//       { id: 'submit', name: '提交申请', time: '2025-08-01 09:23:45', status: 'completed' },
//       { id: 'review', name: '审核中', time: '2025-08-01 10:15:30', status: 'completed' },
//       { id: 'approve', name: '审批通过', time: '2025-08-02 14:30:00', status: 'completed' },
//       { id: 'activate', name: '服务激活', time: '2025-08-02 15:00:00', status: 'completed' },
//     ],
//   },
//   {
//     id: 'TA-20250805',
//     tenantName: '云端数据有限公司',
//     applyTime: '2025-08-05 11:45:22',
//     serviceType: 'advanced',
//     serviceTypeName: '高级地图服务',
//     authPeriod: '2years',
//     authPeriodName: '2年',
//     status: 'reviewing',
//     statusName: '审核中',
//     progress: 60,
//     currentStep: 'review',
//     steps: [
//       { id: 'submit', name: '提交申请', time: '2025-08-05 11:45:22', status: 'completed' },
//       { id: 'review', name: '审核中', time: '2025-08-05 14:20:15', status: 'current' },
//       { id: 'approve', name: '审批通过', time: '', status: 'pending' },
//       { id: 'activate', name: '服务激活', time: '', status: 'pending' },
//     ],
//   },
//   {
//     id: 'TA-20250803',
//     tenantName: '绿色能源集团',
//     applyTime: '2025-08-03 10:15:33',
//     serviceType: 'basic',
//     serviceTypeName: '基础地图服务',
//     authPeriod: '3months',
//     authPeriodName: '3个月',
//     status: 'pending',
//     statusName: '待审批',
//     progress: 25,
//     currentStep: 'review',
//     steps: [
//       { id: 'submit', name: '提交申请', time: '2025-08-03 10:15:33', status: 'completed' },
//       { id: 'review', name: '审核中', time: '', status: 'current' },
//       { id: 'approve', name: '审批通过', time: '', status: 'pending' },
//       { id: 'activate', name: '服务激活', time: '', status: 'pending' },
//     ],
//   },
//   {
//     id: 'TA-20250728',
//     tenantName: '智能物流股份有限公司',
//     applyTime: '2025-07-28 16:30:45',
//     serviceType: 'enterprise',
//     serviceTypeName: '企业级地图服务',
//     authPeriod: '1year',
//     authPeriodName: '1年',
//     status: 'rejected',
//     statusName: '已驳回',
//     progress: 50,
//     currentStep: 'review',
//     steps: [
//       { id: 'submit', name: '提交申请', time: '2025-07-28 16:30:45', status: 'completed' },
//       { id: 'review', name: '审核中', time: '2025-07-29 09:15:20', status: 'completed' },
//       { id: 'approve', name: '审批驳回', time: '2025-07-30 11:45:33', status: 'rejected' },
//       { id: 'activate', name: '服务激活', time: '', status: 'pending' },
//     ],
//     rejectReason: '企业资质文件不完整，请补充营业执照副本。',
//   },
// ]

// 状态选项
const statusOptions = [
  { id: '', name: '全部状态' },
  { id: 'pending', name: '待审批' },
  { id: 'reviewing', name: '审核中' },
  { id: 'approved', name: '已通过' },
  { id: 'rejected', name: '已驳回' },
]

// 响应式数据
const searchTerm = ref('')
const filters = reactive({
  status: '',
})

const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0, //progressData.value.length,
})

// 处理搜索变化
const handleSearchChange = () => {
  pagination.page = 1 // 重置页码
}

// 处理筛选变化
const handleFilterChange = () => {
  pagination.page = 1 // 重置页码
}

// 过滤数据
const filteredApplications = computed(() => {
  return progressData.value.filter(app => {
    // 搜索过滤
    const matchesSearch = searchTerm.value === '' || 
      app.tenantName.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
      app.id.toLowerCase().includes(searchTerm.value.toLowerCase())
    
    // 状态过滤
    const matchesStatus = filters.status === '' || app.status === filters.status
    
    return matchesSearch && matchesStatus
  })
})

// 计算分页数据
const paginatedApplications = computed(() => {
  const start = (pagination.page - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return filteredApplications.value.slice(start, end)
})

// 获取状态样式
const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'reviewing':
      return 'bg-blue-100 text-blue-800'
    case 'reviewing':
      return 'bg-blue-100 text-blue-800'
    case 'approved':
      return 'bg-green-100 text-green-800'
    case 'rejected':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 获取步骤状态样式
const getStepStatusClass = (stepStatus: string) => {
  switch (stepStatus) {
    case 'completed':
      return 'bg-green-100 text-green-800'
    case 'current':
      return 'bg-blue-100 text-blue-800'
    case 'rejected':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 获取步骤图标
const getStepIcon = (stepStatus: string, stepId: string) => {
  switch (stepStatus) {
    case 'completed':
      return 'fa-solid fa-check'
    case 'current':
      return 'fa-solid fa-hourglass-half'
    case 'rejected':
      return 'fa-solid fa-times'
    default:
      return 'fa-solid fa-circle'
  }
}

// 分页控制
const prevPage = () => {
  if (pagination.page > 1) {
    pagination.page--
  }
}

const nextPage = () => {
  if (pagination.page < Math.ceil(filteredApplications.value.length / pagination.pageSize)) {
    pagination.page++
  }
}

const router = useRouter()
</script>