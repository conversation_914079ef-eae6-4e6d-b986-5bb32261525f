@echo off
REM Map Studio 自动化部署脚本 (Windows版本)
REM 支持开发、测试和生产环境的一键部署

setlocal enabledelayedexpansion

echo ========================================
echo   Map Studio 自动化部署脚本 (Windows)
echo ========================================
echo.

REM 默认值
set ENVIRONMENT=dev
set BUILD_FLAG=false
set CLEAN_FLAG=false

REM 解析参数
:parse_args
if "%~1"=="" goto :args_done
if "%~1"=="dev" (
    set ENVIRONMENT=dev
    shift
    goto :parse_args
)
if "%~1"=="test" (
    set ENVIRONMENT=test
    shift
    goto :parse_args
)
if "%~1"=="prod" (
    set ENVIRONMENT=prod
    shift
    goto :parse_args
)
if "%~1"=="--build" (
    set BUILD_FLAG=true
    shift
    goto :parse_args
)
if "%~1"=="--clean" (
    set CLEAN_FLAG=true
    shift
    goto :parse_args
)
if "%~1"=="--logs" (
    echo ℹ️  显示服务日志...
    docker-compose logs -f --tail=100
    exit /b 0
)
if "%~1"=="--health" (
    call :check_health
    exit /b 0
)
if "%~1"=="--stop" (
    call :stop_services
    exit /b 0
)
if "%~1"=="--restart" (
    call :restart_services
    exit /b 0
)
if "%~1"=="--help" (
    call :show_usage
    exit /b 0
)
echo ❌ 未知参数: %~1
call :show_usage
exit /b 1

:args_done

echo ℹ️  部署环境: %ENVIRONMENT%

REM 执行部署流程
call :check_dependencies
if errorlevel 1 exit /b 1

call :setup_directories
call :generate_configs

if "%CLEAN_FLAG%"=="true" (
    call :clean_resources
)

if "%BUILD_FLAG%"=="true" (
    call :build_images
) else (
    REM 检查镜像是否存在
    docker images | findstr mapstudio-backend >nul
    if errorlevel 1 (
        call :build_images
    )
)

call :start_services
call :check_health
call :show_services_info

echo ✅ 🎉 Map Studio 部署完成!
goto :eof

REM 函数定义

:show_usage
echo 使用方法: %~nx0 [环境] [选项]
echo.
echo 环境:
echo   dev        开发环境 (默认)
echo   test       测试环境
echo   prod       生产环境
echo.
echo 选项:
echo   --build    强制重新构建镜像
echo   --clean    清理旧数据和镜像
echo   --logs     显示服务日志
echo   --health   检查服务健康状态
echo   --stop     停止所有服务
echo   --restart  重启所有服务
echo   --help     显示此帮助信息
echo.
echo 示例:
echo   %~nx0 dev --build     # 构建并启动开发环境
echo   %~nx0 test --clean    # 清理并启动测试环境
echo   %~nx0 prod            # 启动生产环境
echo   %~nx0 --stop          # 停止所有服务
goto :eof

:check_dependencies
echo 🔄 检查系统依赖...

REM 检查 Docker
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker 未安装或未在PATH中
    echo ℹ️  请访问 https://docs.docker.com/get-docker/ 安装 Docker
    exit /b 1
)

REM 检查 Docker Compose
docker-compose --version >nul 2>&1
if errorlevel 1 (
    docker compose version >nul 2>&1
    if errorlevel 1 (
        echo ❌ Docker Compose 未安装或未在PATH中
        echo ℹ️  请访问 https://docs.docker.com/compose/install/ 安装 Docker Compose
        exit /b 1
    )
)

REM 检查 Docker 服务状态
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker 服务未运行
    echo ℹ️  请启动 Docker 服务
    exit /b 1
)

echo ✅ 系统依赖检查完成
goto :eof

:setup_directories
echo 🔄 创建必要的目录结构...

if not exist data mkdir data
if not exist data\mysql mkdir data\mysql
if not exist data\uploads mkdir data\uploads
if not exist data\logs mkdir data\logs
if not exist monitoring mkdir monitoring
if not exist monitoring\prometheus mkdir monitoring\prometheus
if not exist monitoring\grafana mkdir monitoring\grafana
if not exist monitoring\grafana\dashboards mkdir monitoring\grafana\dashboards
if not exist monitoring\grafana\datasources mkdir monitoring\grafana\datasources
if not exist nginx mkdir nginx
if not exist nginx\conf.d mkdir nginx\conf.d
if not exist nginx\ssl mkdir nginx\ssl
if not exist scripts mkdir scripts

echo ✅ 目录结构创建完成
goto :eof

:generate_configs
echo 🔄 生成配置文件...

REM 生成 .env 文件
if not exist .env (
    echo # Map Studio 环境配置 > .env
    echo ENVIRONMENT=%ENVIRONMENT% >> .env
    echo MYSQL_ROOT_PASSWORD=mapstudio_password >> .env
    echo MYSQL_PASSWORD=mapstudio_user_password >> .env
    echo JWT_SECRET_KEY=your-super-secret-jwt-key-here >> .env
    echo API_BASE_URL=http://localhost:5000/api >> .env
    echo FRONTEND_URL=http://localhost:3001 >> .env
    echo ✅ 已生成 .env 配置文件
)

REM 生成数据库初始化脚本
if not exist scripts\init-db.sql (
    echo -- Map Studio 数据库初始化脚本 > scripts\init-db.sql
    echo CREATE DATABASE IF NOT EXISTS mapstudio_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; >> scripts\init-db.sql
    echo CREATE DATABASE IF NOT EXISTS mapstudio_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; >> scripts\init-db.sql
    echo CREATE DATABASE IF NOT EXISTS mapstudio_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; >> scripts\init-db.sql
    echo. >> scripts\init-db.sql
    echo -- 创建用户并授权 >> scripts\init-db.sql
    echo CREATE USER IF NOT EXISTS 'mapstudio'@'%%' IDENTIFIED BY 'mapstudio_user_password'; >> scripts\init-db.sql
    echo GRANT ALL PRIVILEGES ON mapstudio_dev.* TO 'mapstudio'@'%%'; >> scripts\init-db.sql
    echo GRANT ALL PRIVILEGES ON mapstudio_test.* TO 'mapstudio'@'%%'; >> scripts\init-db.sql
    echo GRANT ALL PRIVILEGES ON mapstudio_prod.* TO 'mapstudio'@'%%'; >> scripts\init-db.sql
    echo FLUSH PRIVILEGES; >> scripts\init-db.sql
    echo ✅ 已生成数据库初始化脚本
)

goto :eof

:build_images
echo 🔄 构建 Docker 镜像...

REM 选择构建目标
if "%ENVIRONMENT%"=="dev" set TARGET=development
if "%ENVIRONMENT%"=="test" set TARGET=testing
if "%ENVIRONMENT%"=="prod" set TARGET=production

REM 构建后端镜像
echo ℹ️  构建后端镜像...
docker build -t mapstudio-backend:%ENVIRONMENT% --target %TARGET% .\backend\

REM 构建前端镜像
echo ℹ️  构建前端镜像...
docker build -t mapstudio-frontend:%ENVIRONMENT% --target %TARGET% .\frontend\

echo ✅ 镜像构建完成
goto :eof

:start_services
echo 🔄 启动服务...

if "%ENVIRONMENT%"=="dev" (
    docker-compose up -d mysql redis backend frontend
) else if "%ENVIRONMENT%"=="test" (
    docker-compose --profile testing up -d mysql-test backend-test
) else if "%ENVIRONMENT%"=="prod" (
    docker-compose --profile production up -d mysql redis backend frontend nginx
)

echo ✅ 服务启动完成
goto :eof

:check_health
echo 🔄 检查服务健康状态...

REM 等待服务启动
timeout /t 10 /nobreak >nul

REM 检查数据库
docker-compose ps mysql | findstr "Up" >nul
if not errorlevel 1 (
    echo ✅ MySQL 数据库: 运行正常
) else (
    echo ❌ MySQL 数据库: 未运行
)

REM 检查后端
curl -f http://localhost:5000/api/system/health >nul 2>&1
if not errorlevel 1 (
    echo ✅ 后端 API: 运行正常
) else (
    echo ⚠️  后端 API: 启动中或异常
)

REM 检查前端
curl -f http://localhost:3001 >nul 2>&1
if not errorlevel 1 (
    echo ✅ 前端应用: 运行正常
) else (
    echo ⚠️  前端应用: 启动中或异常
)

goto :eof

:show_services_info
echo 🔄 服务访问信息:
echo.

if "%ENVIRONMENT%"=="dev" (
    echo   🌐 前端应用:    http://localhost:3001
    echo   🔧 后端 API:    http://localhost:5000
    echo   📚 API 文档:    http://localhost:5000/swagger
    echo   🗄️  数据库:      localhost:3306
    echo   🔴 Redis:       localhost:6379
) else if "%ENVIRONMENT%"=="test" (
    echo   🔧 后端 API:    http://localhost:5001
    echo   📚 API 文档:    http://localhost:5001/swagger
    echo   🗄️  测试数据库:  localhost:3307
) else if "%ENVIRONMENT%"=="prod" (
    echo   🌐 主站地址:    http://localhost
    echo   🔧 API 地址:    http://localhost/api
    echo   📊 监控面板:    http://localhost:3000 (如启用)
)

echo.
echo ℹ️  使用 '%~nx0 --logs' 查看服务日志
echo ℹ️  使用 '%~nx0 --health' 检查服务状态
echo ℹ️  使用 '%~nx0 --stop' 停止所有服务
goto :eof

:stop_services
echo 🔄 停止所有服务...
docker-compose down
echo ✅ 服务已停止
goto :eof

:restart_services
echo 🔄 重启服务...
call :stop_services
call :start_services
call :check_health
echo ✅ 服务重启完成
goto :eof

:clean_resources
echo 🔄 清理旧资源...

REM 停止并删除容器
docker-compose down -v --remove-orphans

REM 删除旧镜像
for /f "tokens=3" %%i in ('docker images ^| findstr mapstudio') do docker rmi -f %%i 2>nul

REM 清理未使用的资源
docker system prune -f

echo ✅ 资源清理完成
goto :eof