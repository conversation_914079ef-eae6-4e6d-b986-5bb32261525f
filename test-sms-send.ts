import { OperationService } from './frontend/src/services/operation.service';

async function testSmsSend() {
  console.log('开始测试短信发送功能...');
  
  try {
    // 准备测试数据
    const testData = {
      phoneNumber: '13800138000',  // 测试手机号
      message: '这是一条测试短信，用于验证短信发送功能是否正常工作。',
      level: 'test'
    };
    
    console.log('发送测试短信数据:', testData);
    
    // 调用发送短信接口
    const result = await OperationService.sendSms(testData);
    
    console.log('短信发送成功:', result);
  } catch (error) {
    console.error('短信发送失败:', error);
  }
}

// 执行测试
testSmsSend();