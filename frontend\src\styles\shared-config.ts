/**
 * 跨框架样式共享配置
 * 用于Vue和React之间的样式统一管理
 */

// 设计系统配置
export const designSystem = {
  // 颜色系统
  colors: {
    primary: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
    },
    gray: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
    },
    success: {
      50: '#ecfdf5',
      100: '#d1fae5',
      500: '#10b981',
      600: '#059669',
      700: '#047857',
    },
    warning: {
      50: '#fffbeb',
      100: '#fef3c7',
      500: '#f59e0b',
      600: '#d97706',
      700: '#b45309',
    },
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c',
    },
    info: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      500: '#06b6d4',
      600: '#0891b2',
      700: '#0e7490',
    }
  },

  // 字体系统
  typography: {
    fontFamily: {
      sans: [
        '-apple-system',
        'BlinkMacSystemFont',
        '"Segoe UI"',
        'Roboto',
        '"Helvetica Neue"',
        'Arial',
        '"Noto Sans"',
        'sans-serif'
      ],
      mono: [
        '"SF Mono"',
        'Monaco',
        'Inconsolata',
        '"Roboto Mono"',
        '"Source Code Pro"',
        'monospace'
      ]
    },
    fontSize: {
      xs: ['0.75rem', { lineHeight: '1rem' }],
      sm: ['0.875rem', { lineHeight: '1.25rem' }],
      base: ['1rem', { lineHeight: '1.5rem' }],
      lg: ['1.125rem', { lineHeight: '1.75rem' }],
      xl: ['1.25rem', { lineHeight: '1.75rem' }],
      '2xl': ['1.5rem', { lineHeight: '2rem' }],
      '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
      '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    }
  },

  // 间距系统
  spacing: {
    px: '1px',
    0: '0',
    0.5: '0.125rem',
    1: '0.25rem',
    1.5: '0.375rem',
    2: '0.5rem',
    2.5: '0.625rem',
    3: '0.75rem',
    3.5: '0.875rem',
    4: '1rem',
    5: '1.25rem',
    6: '1.5rem',
    7: '1.75rem',
    8: '2rem',
    9: '2.25rem',
    10: '2.5rem',
    11: '2.75rem',
    12: '3rem',
    14: '3.5rem',
    16: '4rem',
    20: '5rem',
    24: '6rem',
    28: '7rem',
    32: '8rem',
    36: '9rem',
    40: '10rem',
    44: '11rem',
    48: '12rem',
    52: '13rem',
    56: '14rem',
    60: '15rem',
    64: '16rem',
    72: '18rem',
    80: '20rem',
    96: '24rem',
  },

  // 圆角系统
  borderRadius: {
    none: '0',
    sm: '0.125rem',
    DEFAULT: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    '2xl': '1rem',
    '3xl': '1.5rem',
    full: '9999px',
  },

  // 阴影系统
  boxShadow: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    DEFAULT: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
    none: 'none',
  },

  // 断点系统
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },

  // 动画系统
  animation: {
    duration: {
      75: '75ms',
      100: '100ms',
      150: '150ms',
      200: '200ms',
      300: '300ms',
      500: '500ms',
      700: '700ms',
      1000: '1000ms',
    },
    easing: {
      linear: 'linear',
      in: 'cubic-bezier(0.4, 0, 1, 1)',
      out: 'cubic-bezier(0, 0, 0.2, 1)',
      inOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    }
  },

  // Z-index系统
  zIndex: {
    auto: 'auto',
    0: '0',
    10: '10',
    20: '20',
    30: '30',
    40: '40',
    50: '50',
    dropdown: '1000',
    sticky: '1020',
    fixed: '1030',
    modalBackdrop: '1040',
    modal: '1050',
    popover: '1060',
    tooltip: '1070',
  }
};

// 组件样式预设
export const componentPresets = {
  // 按钮预设
  button: {
    base: 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed',
    variants: {
      primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
      secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',
      outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500',
      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',
      danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
    },
    sizes: {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-sm',
      lg: 'px-6 py-3 text-base',
    }
  },

  // 输入框预设
  input: {
    base: 'block w-full border rounded-md shadow-sm transition-colors duration-200 focus:outline-none focus:ring-1 placeholder-gray-400',
    variants: {
      default: 'border-gray-300 focus:ring-blue-500 focus:border-blue-500',
      error: 'border-red-300 focus:ring-red-500 focus:border-red-500',
      success: 'border-green-300 focus:ring-green-500 focus:border-green-500',
    },
    sizes: {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-3 py-2 text-sm',
      lg: 'px-4 py-3 text-base',
    }
  },

  // 卡片预设
  card: {
    base: 'bg-white rounded-lg shadow-sm border border-gray-200',
    variants: {
      default: '',
      elevated: 'shadow-md',
      outlined: 'border-2',
    },
    sections: {
      header: 'px-6 py-4 border-b border-gray-200',
      body: 'px-6 py-4',
      footer: 'px-6 py-4 border-t border-gray-200 bg-gray-50',
    }
  },

  // 标签预设
  badge: {
    base: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
    variants: {
      primary: 'bg-blue-100 text-blue-800',
      secondary: 'bg-gray-100 text-gray-800',
      success: 'bg-green-100 text-green-800',
      warning: 'bg-yellow-100 text-yellow-800',
      danger: 'bg-red-100 text-red-800',
      info: 'bg-cyan-100 text-cyan-800',
    }
  },

  // 表格预设
  table: {
    wrapper: 'overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg',
    table: 'min-w-full divide-y divide-gray-300',
    header: 'bg-gray-50',
    headerCell: 'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider',
    body: 'bg-white divide-y divide-gray-200',
    row: 'hover:bg-gray-50',
    cell: 'px-6 py-4 whitespace-nowrap text-sm text-gray-900',
  },

  // 导航预设
  navigation: {
    sidebar: {
      container: 'bg-white border-r border-gray-200',
      header: 'flex items-center justify-between h-16 px-4 border-b border-gray-200',
      nav: 'p-4 space-y-1',
      item: 'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200',
      itemActive: 'text-blue-600 bg-blue-50',
      itemInactive: 'text-gray-600 hover:text-blue-600 hover:bg-blue-50',
    },
    topbar: {
      container: 'bg-white border-b border-gray-200 shadow-sm',
      content: 'flex items-center justify-between h-16 px-6',
    }
  },

  // 模态框预设
  modal: {
    overlay: 'fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity z-40',
    container: 'fixed inset-0 z-50 overflow-y-auto',
    content: 'flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0',
    panel: 'relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg',
    header: 'px-6 py-4 border-b border-gray-200',
    body: 'px-6 py-4',
    footer: 'px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-end space-x-3',
  },

  // 下拉菜单预设
  dropdown: {
    container: 'relative inline-block text-left',
    menu: 'origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 py-1 z-10',
    item: 'block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-150',
    divider: 'border-t border-gray-100 my-1',
  }
};

// 响应式工具
export const responsive = {
  // 生成响应式类名
  generateResponsiveClass: (
    base: string,
    breakpoints: Partial<Record<keyof typeof designSystem.breakpoints, string>>
  ): string => {
    const classes = [base];
    Object.entries(breakpoints).forEach(([breakpoint, value]) => {
      if (value) {
        classes.push(`${breakpoint}:${value}`);
      }
    });
    return classes.join(' ');
  },

  // 常用响应式模式
  patterns: {
    // 网格响应式
    grid: {
      responsive1to4: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6',
      responsive1to3: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6',
      responsive1to2: 'grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6',
    },
    // 弹性布局响应式
    flex: {
      stackToRow: 'flex flex-col sm:flex-row items-start sm:items-center gap-4',
      centerToStart: 'flex flex-col sm:flex-row justify-center sm:justify-start items-center gap-4',
    },
    // 文本响应式
    text: {
      responsive: 'text-sm sm:text-base lg:text-lg',
      heading: 'text-lg sm:text-xl lg:text-2xl xl:text-3xl',
    },
    // 间距响应式
    spacing: {
      padding: 'p-4 sm:p-6 lg:p-8',
      margin: 'm-4 sm:m-6 lg:m-8',
    }
  }
};

// 主题配置
export const themes = {
  light: {
    background: {
      primary: 'bg-white',
      secondary: 'bg-gray-50',
      tertiary: 'bg-gray-100',
    },
    text: {
      primary: 'text-gray-900',
      secondary: 'text-gray-600',
      tertiary: 'text-gray-500',
    },
    border: {
      primary: 'border-gray-200',
      secondary: 'border-gray-300',
    }
  },
  dark: {
    background: {
      primary: 'bg-gray-900',
      secondary: 'bg-gray-800',
      tertiary: 'bg-gray-700',
    },
    text: {
      primary: 'text-white',
      secondary: 'text-gray-300',
      tertiary: 'text-gray-400',
    },
    border: {
      primary: 'border-gray-700',
      secondary: 'border-gray-600',
    }
  }
};

// 导出配置
export default {
  designSystem,
  componentPresets,
  responsive,
  themes
};