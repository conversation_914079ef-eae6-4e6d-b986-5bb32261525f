/**
 * 运营统计服务
 * 处理各种运营数据统计、分析和报表功能
 */

import { httpClient } from '../utils/http.client';
import { API_ENDPOINTS } from '../config/endpoints';
import type {
  DailyStatsParams,
  DailyStatsResponse,
  DailyStatsSummary,
  MonthlyStatsParams,
  MonthlyStatsResponse,
  OperationLogParams,
  OperationLogItem,
  ActionTypeRankParams,
  UserUsageParams,
  PaginationResponse,
  AlarmSettingsResponse,
  AlarmTemplateUpdateRequest,
  AlarmEmailRequest,
  AlarmEmailUpdateRequest,
  AlarmEmailItem,
  SmsLevelOption,
  SmsTemplateVariable,
  SmsPhoneItem,
  AlarmSmsResponse,
  SmsTemplateUpdateRequest,
  SmsPhoneRequest,
  SmsPhoneUpdateRequest,
  ApiResponse,
  SmsTemplateSettingDto,
  SaveSmsTemplateRequestDto,
  SmsTemplateSettingResponse,
  SmsTemplateSettingListResponse,
  SixMonthUsageStatsResponse
} from './types';
import type { ServiceStatusStats, ServiceStatusStatsResponse, TenantInfo } from './types';

// 后端SmsPhoneSettingDto的前端类型定义
interface SmsPhoneSettingDto {
  id: number;
  phone: string;
  levels: string[];
  status: boolean;
  createDate: string;
}

export class OperationService {
  /**
   * 获取日度统计数据
   * @param params 查询参数
   */
  static async getDailyStats(params: DailyStatsParams = {}): Promise<DailyStatsResponse> {
    try {
      console.log('📊 获取日度统计数据...');

      const queryParams = {
        ...(params.selectedTenant && { selectedTenant: params.selectedTenant }),
        ...(params.selectedServiceType && { selectedServiceType: params.selectedServiceType }),
        ...(params.selectedSize && { selectedSize: params.selectedSize }),
        ...(params.viewMode && { viewMode: params.viewMode })
      };

      const response = await httpClient.get<DailyStatsResponse>(
        API_ENDPOINTS.OPERATION.DAILY_STATS,
        { params: queryParams }
      );

      console.log(`✅ 获取日度统计成功: ${response.dailyStatsData.length} 条数据`);
      return response;
    } catch (error: any) {
      console.error('❌ 获取日度统计失败:', error.message);
      throw new Error(error.message || '获取日度统计数据失败');
    }
  }

  /**
   * 获取日统计汇总数据
   * 用于daily-stats页面的统计功能，获取四个关键统计数据：
   * 1. 租户总数：统计ms_auth_applications表的数据量
   * 2. 平均日操作次数：按天汇聚统计操作数，然后求平均值
   * 3. 最大日操作数：按天汇聚统计，找出最大值
   * 4. 操作总量：统计ms_operation_logs表当天的总数据量
   * @param tenantName 租户名称，可选参数，为空时统计所有租户
   */
  static async getDailyStatsSummary(tenantName?: string): Promise<DailyStatsSummary> {
    try {
      console.log('📊 获取日统计汇总数据...');

      // 构建查询参数
      const queryParams = {
        ...(tenantName && { tenantName })
      };

      const response = await httpClient.get<DailyStatsSummary>(
        API_ENDPOINTS.OPERATION.DAILY_STATS_SUMMARY,
        { params: queryParams }
      );

      console.log('✅ 获取日统计汇总数据成功:', response);
      return response;
    } catch (error: any) {
      console.error('❌ 获取日统计汇总数据失败:', error.message);
      throw new Error(error.message || '获取日统计汇总数据失败');
    }
  }

  /**
   * 获取月度统计数据
   * @param params 查询参数
   */
  static async getMonthlyStats(params: MonthlyStatsParams = {}): Promise<MonthlyStatsResponse> {
    try {
      console.log('📈 获取月度统计数据...');

      const queryParams = {
        ...(params.selectedTenant && { selectedTenant: params.selectedTenant }),
        ...(params.comparisonType && { comparisonType: params.comparisonType })
      };

      const response = await httpClient.get<MonthlyStatsResponse>(
        API_ENDPOINTS.OPERATION.MONTHLY_STATS,
        { params: queryParams }
      );

      console.log(`✅ 获取月度统计成功: ${response.monthlyData.length} 月数据`);
      return response;
    } catch (error: any) {
      console.error('❌ 获取月度统计失败:', error.message);
      throw new Error(error.message || '获取月度统计数据失败');
    }
  }

  /**
   * 获取访问统计数据
   * @param timeRange 时间范围
   * @param chartType 图表类型
   */
  static async getAccessStats(
    timeRange?: string,
    chartType?: string
  ): Promise<any> {
    try {
      console.log('🔍 获取访问统计数据...');

      const queryParams = {
        ...(timeRange && { timeRange }),
        ...(chartType && { chartType })
      };

      const response = await httpClient.get(
        API_ENDPOINTS.OPERATION.ACCESS_STATS,
        { params: queryParams }
      );

      console.log('✅ 获取访问统计成功');
      return response;
    } catch (error: any) {
      console.error('❌ 获取访问统计失败:', error.message);
      throw new Error(error.message || '获取访问统计数据失败');
    }
  }

  /**
   * 获取操作类型排行
   * @param params 查询参数
   */
  static async getActionTypeRank(params: ActionTypeRankParams): Promise<any> {
    try {
      console.log('🏆 获取操作类型排行...');

      const queryParams = {
        selectedOperationType: params.selectedOperationType,
        ...(params.tenantSize && { tenantSize: params.tenantSize }),
        ...(params.industry && { industry: params.industry }),
        ...(params.timeRange && { timeRange: params.timeRange })
      };

      const response = await httpClient.get(
        API_ENDPOINTS.OPERATION.ACTION_TYPE_RANK,
        { params: queryParams }
      );

      console.log('✅ 获取操作类型排行成功');
      return response;
    } catch (error: any) {
      console.error('❌ 获取操作类型排行失败:', error.message);
      throw new Error(error.message || '获取操作类型排行失败');
    }
  }

  /**
   * 获取租户操作排行
   */
  static async getTenantActionRank(): Promise<any> {
    try {
      console.log('🏅 获取租户操作排行...');

      const response = await httpClient.get(
        API_ENDPOINTS.OPERATION.TENANT_ACTION_RANK
      );

      console.log('✅ 获取租户操作排行成功');
      return response;
    } catch (error: any) {
      console.error('❌ 获取租户操作排行失败:', error.message);
      throw new Error(error.message || '获取租户操作排行失败');
    }
  }

  /**
   * 获取用户使用情况
   * @param params 查询参数
   */
  static async getUserUsage(params: UserUsageParams = {}): Promise<any> {
    try {
      console.log('👥 获取用户使用情况...');

      const queryParams = {
        ...(params.selectedTenant && { selectedTenant: params.selectedTenant }),
        ...(params.timeRange && { timeRange: params.timeRange }),
        ...(params.timeGranularity && { timeGranularity: params.timeGranularity })
      };

      const response = await httpClient.get(
        API_ENDPOINTS.OPERATION.USER_USAGE,
        { params: queryParams }
      );

      console.log('✅ 获取用户使用情况成功');
      return response;
    } catch (error: any) {
      console.error('❌ 获取用户使用情况失败:', error.message);
      throw new Error(error.message || '获取用户使用情况失败');
    }
  }

  /**
   * 获取操作日志列表
   * @param params 查询参数
   */
  static async getOperationLogs(
    params: OperationLogParams
  ): Promise<PaginationResponse<OperationLogItem>> {
    try {
      console.log('📋 获取操作日志列表...');

      const queryParams = {
        page: params.page || 1,
        pageSize: params.pageSize || 20,
        ...(params.tenantName && { tenantName: params.tenantName }),
        ...(params.actionType && { actionType: params.actionType }),
        ...(params.userName && { userName: params.userName }),
        ...(params.operationDate && { operationDate: params.operationDate })
      };

      const response = await httpClient.get<PaginationResponse<OperationLogItem>>(
        API_ENDPOINTS.OPERATION.LOGS,
        { params: queryParams }
      );

      console.log(`✅ 获取操作日志成功: ${response.totalCount} 条记录`);
      return response;
    } catch (error: any) {
      console.error('❌ 获取操作日志失败:', error.message);
      throw new Error(error.message || '获取操作日志失败');
    }
  }

  /**
   * 获取操作日志详情
   * @param logId 日志ID
   */
  static async getOperationLogDetail(logId: string): Promise<any> {
    try {
      console.log('🔍 获取操作日志详情:', logId);

      if (!logId) {
        throw new Error('日志ID不能为空');
      }

      const response = await httpClient.get(
        API_ENDPOINTS.OPERATION.LOG_DETAIL(logId)
      );

      console.log('✅ 获取日志详情成功');
      return response;
    } catch (error: any) {
      console.error('❌ 获取日志详情失败:', error.message);
      throw new Error(error.message || '获取日志详情失败');
    }
  }

  /**
   * 获取告警设置
   */
  static async getAlarmSettings(): Promise<AlarmSettingsResponse> {
    try {
      console.log('⚠️ 获取告警设置...');

      const response = await httpClient.get<AlarmSettingsResponse>(
        API_ENDPOINTS.OPERATION.ALARM_SETTINGS
      );

      console.log('✅ 获取告警设置成功');
      return response;
    } catch (error: any) {
      console.error('❌ 获取告警设置失败:', error.message);
      throw new Error(error.message || '获取告警设置失败');
    }
  }

  /**
   * 更新告警邮件模板
   * @param emailTemplate 邮件模板
   */
  static async updateAlarmTemplate(emailTemplate: string): Promise<any> {
    try {
      console.log('📧 更新告警邮件模板...');

      if (!emailTemplate?.trim()) {
        throw new Error('邮件模板不能为空');
      }

      const request: AlarmTemplateUpdateRequest = { emailTemplate };

      const response = await httpClient.put(
        API_ENDPOINTS.OPERATION.ALARM_TEMPLATE,
        request
      );

      console.log('✅ 更新邮件模板成功');
      return response;
    } catch (error: any) {
      console.error('❌ 更新邮件模板失败:', error.message);
      throw new Error(error.message || '更新邮件模板失败');
    }
  }

  /**
   * 添加告警邮箱
   * @param email 邮箱地址
   * @param levels 告警级别
   */
  static async addAlarmEmail(email: string, levels: string[]): Promise<any> {
    try {
      console.log('📬 添加告警邮箱:', email);

      if (!email?.trim()) {
        throw new Error('邮箱地址不能为空');
      }

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new Error('邮箱地址格式不正确');
      }

      if (!levels || levels.length === 0) {
        throw new Error('请至少选择一个告警级别');
      }

      const request: AlarmEmailRequest = { email, levels };

      const response = await httpClient.post(
        API_ENDPOINTS.OPERATION.ALARM_EMAILS,
        request
      );

      console.log('✅ 添加告警邮箱成功');
      return response;
    } catch (error: any) {
      console.error('❌ 添加告警邮箱失败:', error.message);
      throw new Error(error.message || '添加告警邮箱失败');
    }
  }

  /**
   * 更新告警邮箱
   * @param emailId 邮箱ID
   * @param enabled 是否启用
   * @param levels 告警级别
   */
  static async updateAlarmEmail(
    emailId: string,
    enabled: boolean,
    levels: string[]
  ): Promise<any> {
    try {
      console.log('📝 更新告警邮箱:', emailId);

      if (!emailId) {
        throw new Error('邮箱ID不能为空');
      }

      const request: AlarmEmailUpdateRequest = { enabled, levels };

      const response = await httpClient.put(
        API_ENDPOINTS.OPERATION.ALARM_EMAIL(emailId),
        request
      );

      console.log('✅ 更新告警邮箱成功');
      return response;
    } catch (error: any) {
      console.error('❌ 更新告警邮箱失败:', error.message);
      throw new Error(error.message || '更新告警邮箱失败');
    }
  }

  /**
   * 删除告警邮箱
   * @param emailId 邮箱ID
   */
  static async deleteAlarmEmail(emailId: string): Promise<void> {
    try {
      console.log('🗑️ 删除告警邮箱:', emailId);

      if (!emailId) {
        throw new Error('邮箱ID不能为空');
      }

      await httpClient.delete(
        API_ENDPOINTS.OPERATION.ALARM_EMAIL(emailId)
      );

      console.log('✅ 删除告警邮箱成功');
    } catch (error: any) {
      console.error('❌ 删除告警邮箱失败:', error.message);
      throw new Error(error.message || '删除告警邮箱失败');
    }
  }

  // =============================================================================
  // 短信告警相关方法
  // =============================================================================

  /**
   * 获取短信告警级别选项
   */
  static getSmsLevelOptions(): SmsLevelOption[] {
    return [
      { id: 'system_failure', name: '系统故障', description: '系统严重故障，无法正常使用' },
      { id: 'service_exception', name: '服务异常', description: '服务功能异常，但仍可部分使用' },
      { id: 'performance_warning', name: '性能警告', description: '服务性能下降，需要关注' },
      { id: 'maintenance_notice', name: '维护通知', description: '系统维护通知，可能影响服务' }
    ];
  }

  /**
   * 获取短信模板变量
   */
  static getSmsTemplateVariables(): SmsTemplateVariable[] {
    return [
      { key: '{{type}}', description: '类型' },
      { key: '{{modular}}', description: '模组' },
      { key: '{{function}}', description: '功能' },
      { key: '{{receiveMans}}', description: '接收人' },
      { key: '{{receivePns}}', description: '接收人号码' },
      { key: '{{ccMans}}', description: '抄送人' },
      { key: '{{ccPns}}', description: '抄送人号码' },
      { key: '{{sendMan}}', description: '发送人' },
      { key: '{{message}}', description: '发送内容' }
    ];
  }

  /**
   * 获取短信告警设置
   */
  static async getAlarmSmsSettings(): Promise<SmsPhoneSettingDto[]> {
    try {
      console.log('📱 获取短信告警设置...');

      const response = await httpClient.get<any>(
        API_ENDPOINTS.SEND_MSG.PHONES
      );

      console.log('✅ 获取短信告警设置成功:', response);
      
      // 检查响应数据结构
      if (response && response.data) {
        return response.data;
      } else if (response && Array.isArray(response)) {
        return response;
      } else {
        console.warn('响应数据格式不符合预期:', response);
        return [];
      }
    } catch (error: any) {
      console.error('❌ 获取短信告警设置失败:', error.message);
      throw new Error(error.message || '获取短信告警设置失败');
    }
  }

  /**
   * 获取短信接收手机号列表
   */
  static async getSmsPhones(): Promise<SmsPhoneSettingDto[]> {
    try {
      console.log('📱 获取短信接收手机号列表...');

      const response = await httpClient.get<any>(
        API_ENDPOINTS.SEND_MSG.PHONES
      );

      console.log('✅ 获取短信接收手机号列表成功:', response);
      
      // 检查响应数据结构
      if (response && response.data) {
        return response.data;
      } else if (response && Array.isArray(response)) {
        return response;
      } else {
        console.warn('响应数据格式不符合预期:', response);
        return [];
      }
    } catch (error: any) {
      console.error('❌ 获取短信接收手机号列表失败:', error.message);
      throw new Error(error.message || '获取短信接收手机号列表失败');
    }
  }

  /**
   * 保存短信接收手机号列表
   * @param phones 手机号列表
   */
  static async saveSmsPhones(phones: SmsPhoneItem[]): Promise<any> {
    try {
      console.log('💾 保存短信接收手机号列表...');

      const response = await httpClient.post(
        API_ENDPOINTS.SEND_MSG.PHONES,
        phones
      );

      console.log('✅ 保存短信接收手机号列表成功');
      return response;
    } catch (error: any) {
      console.error('❌ 保存短信接收手机号列表失败:', error.message);
      throw new Error(error.message || '保存短信接收手机号列表失败');
    }
  }

  /**
   * 更新短信模板
   * @param smsTemplate 短信模板
   */
  static async updateSmsTemplate(smsTemplate: string): Promise<any> {
    try {
      console.log('📝 更新短信模板...');

      if (!smsTemplate?.trim()) {
        throw new Error('短信模板不能为空');
      }

      const request: SmsTemplateUpdateRequest = { smsTemplate };

      const response = await httpClient.put(
        API_ENDPOINTS.OPERATION.ALARM_SMS_TEMPLATE,
        request
      );

      console.log('✅ 更新短信模板成功');
      return response;
    } catch (error: any) {
      console.error('❌ 更新短信模板失败:', error.message);
      throw new Error(error.message || '更新短信模板失败');
    }
  }

  /**
   * 添加短信接收手机号
   * @param phone 手机号码
   * @param levels 告警级别
   */
  static async addSmsPhone(phone: string, levels: string[]): Promise<SmsPhoneSettingDto> {
    try {
      console.log('📱 添加短信接收手机号:', phone);

      if (!phone?.trim()) {
        throw new Error('手机号码不能为空');
      }

      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(phone)) {
        throw new Error('手机号码格式不正确');
      }

      if (!levels || levels.length === 0) {
        throw new Error('请至少选择一个告警级别');
      }

      const request: SmsPhoneRequest = { phone, levels };

      const response = await httpClient.post<ApiResponse<SmsPhoneSettingDto>>(
        API_ENDPOINTS.SEND_MSG.PHONES,
        request
      );

      console.log('✅ 添加短信接收手机号成功');
      return response.data;
    } catch (error: any) {
      console.error('❌ 添加短信接收手机号失败:', error.message);
      throw new Error(error.message || '添加短信接收手机号失败');
    }
  }

  /**
   * 更新短信接收手机号
   * @param phoneId 手机号ID
   * @param status 是否启用
   * @param levels 告警级别
   */
  static async updateSmsPhone(
    phoneId: string,
    status: boolean,
    levels: string[]
  ): Promise<SmsPhoneSettingDto> {
    try {
      console.log('📝 更新短信接收手机号:', phoneId);

      if (!phoneId) {
        throw new Error('手机号ID不能为空');
      }

      const request: SmsPhoneUpdateRequest = { status, levels };

      const response = await httpClient.put<ApiResponse<SmsPhoneSettingDto>>(
        `${API_ENDPOINTS.SEND_MSG.PHONES}/${phoneId}`,
        request
      );

      console.log('✅ 更新短信接收手机号成功');
      return response.data;
    } catch (error: any) {
      console.error('❌ 更新短信接收手机号失败:', error.message);
      throw new Error(error.message || '更新短信接收手机号失败');
    }
  }

  /**
   * 删除短信接收手机号
   * @param phoneId 手机号ID
   */
  static async deleteSmsPhone(phoneId: string): Promise<boolean> {
    try {
      console.log('🗑️ 删除短信接收手机号:', phoneId);

      if (!phoneId) {
        throw new Error('手机号ID不能为空');
      }

      const response = await httpClient.delete<ApiResponse<boolean>>(
        `${API_ENDPOINTS.SEND_MSG.PHONES}/${phoneId}`
      );

      console.log('✅ 删除短信接收手机号成功');
      return response.data;
    } catch (error: any) {
      console.error('❌ 删除短信接收手机号失败:', error.message);
      throw new Error(error.message || '删除短信接收手机号失败');
    }
  }

  /**
   * 格式化短信级别名称
   * @param levelId 级别ID
   */
  static formatSmsLevelName(levelId: string): string {
    const levelOptions = this.getSmsLevelOptions();
    return levelOptions.find(level => level.id === levelId)?.name || levelId;
  }

  /**
   * 获取短信级别徽章样式
   * @param levelId 级别ID
   */
  static getSmsLevelBadgeClass(levelId: string): string {
    switch (levelId) {
      case 'system_failure':
        return 'bg-red-100 text-red-800';
      case 'service_exception':
        return 'bg-orange-100 text-orange-800';
      case 'performance_warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'maintenance_notice':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  /**
   * 格式化操作类型
   * @param actionType 操作类型
   */
  static formatActionType(actionType: string): { text: string; color: string } {
    const typeMap: { [key: string]: { text: string; color: string } } = {
      create: { text: '创建', color: '#00B42A' },
      update: { text: '更新', color: '#165DFF' },
      delete: { text: '删除', color: '#F53F3F' },
      query: { text: '查询', color: '#14C9C9' },
      login: { text: '登录', color: '#F7BA1E' },
      logout: { text: '登出', color: '#86909C' }
    };

    return typeMap[actionType] || { text: actionType, color: '#86909C' };
  }

  /**
   * 格式化时间范围
   * @param timeRange 时间范围
   */
  static formatTimeRange(timeRange: string): string {
    const rangeMap: { [key: string]: string } = {
      '1day': '最近1天',
      '7days': '最近7天',
      '30days': '最近30天',
      '90days': '最近90天',
      '1year': '最近1年'
    };

    return rangeMap[timeRange] || timeRange;
  }

  /**
   * 格式化文件大小
   * @param bytes 字节数
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 导出统计数据
   * @param type 导出类型
   * @param params 查询参数
   */
  static async exportStats(type: string, params: any = {}): Promise<Blob> {
    try {
      console.log('📤 导出统计数据:', type);

      const queryParams = {
        type,
        format: 'excel',
        ...params
      };

      const response = await httpClient.get('/operation/export', {
        params: queryParams,
        responseType: 'blob'
      });

      console.log('✅ 导出数据成功');
      return response as unknown as Blob;
    } catch (error: any) {
      console.error('❌ 导出数据失败:', error.message);
      throw new Error(error.message || '导出数据失败');
    }
  }

  // =============================================================================
  // 短信模板相关方法
  // =============================================================================

  /**
   * 获取短信模板
   */
  static async getSmsTemplate(): Promise<SmsTemplateSettingListResponse> {
    try {
      console.log('📝 获取短信模板...');

      // 直接使用axios，不经过httpClient的extractResponseData处理
      const rawResponse = await (httpClient as any).instance.get(
        API_ENDPOINTS.SMS_TEMPLATE.GET_SMS_TEMPLATE
      );
      console.log('📥 收到HTTP原始响应:', rawResponse);
      
      // 从原始响应中提取数据
      const response = {
        success: rawResponse.data.success,
        message: rawResponse.data.message,
        data: rawResponse.data.data
      };
      console.log('📦 处理后的响应:', response);

      console.log('✅ 获取短信模板成功');
      return response;
    } catch (error: any) {
      console.error('❌ 获取短信模板失败:', error);
      console.error('❌ 错误详情:', error.response?.data || error.message);
      throw new Error(error.message || '获取短信模板失败');
    }
  }

  /**
   * 保存短信模板
   * @param templateData 短信模板数据
   */
  static async saveSmsTemplate(templateData: SaveSmsTemplateRequestDto): Promise<SmsTemplateSettingResponse> {
    try {
      console.log('💾 保存短信模板...');
      console.log('📋 请求数据:', templateData);
      console.log('🌐 API端点:', API_ENDPOINTS.SMS_TEMPLATE.SAVE_SMS_TEMPLATE);

      if (!templateData.templateName?.trim()) {
        throw new Error('短信模板名称不能为空');
      }

      console.log('📤 发送HTTP POST请求...');
      // 直接使用axios，不经过httpClient的extractResponseData处理
      const rawResponse = await (httpClient as any).instance.post(
        API_ENDPOINTS.SMS_TEMPLATE.SAVE_SMS_TEMPLATE,
        templateData
      );
      console.log('📥 收到HTTP原始响应:', rawResponse);
      
      // 从原始响应中提取数据
      const response = {
        success: rawResponse.data.success,
        message: rawResponse.data.message,
        data: rawResponse.data.data
      };
      console.log('📦 处理后的响应:', response);

      console.log('✅ 保存短信模板成功');
      return response;
    } catch (error: any) {
      console.error('❌ 保存短信模板失败:', error);
      console.error('❌ 错误详情:', error.response?.data || error.message);
      throw new Error(error.message || '保存短信模板失败');
    }
  }

  /**
   * 发送告警短信
   * @param smsData 短信数据
   */
  static async sendAlertSms(smsData: any): Promise<any> {
    try {
      console.log('📱 发送告警短信...', smsData);

      const response = await httpClient.post<any>(
        '/SendMsg/send-alert-sms',
        smsData
      );

      console.log('✅ 发送告警短信成功');
      return response;
    } catch (error: any) {
      console.error('❌ 发送告警短信失败:', error.message);
      throw new Error(error.message || '发送告警短信失败');
    }
  }

  /**
   * 获取服务状态统计数据
   */
  static async getServiceStatusStats(): Promise<ServiceStatusStats> {
    try {
      console.log('📊 获取服务状态统计数据...');

      const response = await httpClient.get<ServiceStatusStats>(
        API_ENDPOINTS.OPERATION.SERVICE_STATUS_STATS
      );

      console.log('✅ 获取服务状态统计成功');
      return response;
    } catch (error: any) {
      console.error('❌ 获取服务状态统计失败:', error.message);
      throw new Error(error.message || '获取服务状态统计数据失败');
    }
  }

  /**
   * 获取近6个月使用率统计数据
   */
  static async getSixMonthUsageStats(): Promise<SixMonthUsageStatsResponse> {
    try {
      console.log('📊 获取近6个月使用率统计数据...');

      const response = await httpClient.get<SixMonthUsageStatsResponse>(
        API_ENDPOINTS.OPERATION.SIX_MONTH_USAGE_STATS
      );

      console.log(`✅ 获取近6个月使用率统计成功: ${response.monthlyData.length} 月数据`);
      return response;
    } catch (error: any) {
      console.error('❌ 获取近6个月使用率统计失败:', error.message);
      throw new Error(error.message || '获取近6个月使用率统计数据失败');
    }
  }

  /**
   * 获取运行状况统计数据
   */
  static async getServiceStatusCodeStats(): Promise<ServiceStatusStatsResponse> {
    try {
      console.log('📊 获取运行状况统计数据...');

      const response = await httpClient.get<ServiceStatusStatsResponse>(
        API_ENDPOINTS.OPERATION.SERVICE_STATUS_CODE_STATS
      );

      console.log('✅ 获取运行状况统计成功');
      return response;
    } catch (error: any) {
      console.error('❌ 获取运行状况统计失败:', error.message);
      throw new Error(error.message || '获取运行状况统计数据失败');
    }
  }

  /**
   * 获取租户信息列表
   */
  static async getAllTenants(): Promise<TenantInfo[]> {
    try {
      console.log('🏢 获取租户信息列表...');

      const response = await httpClient.get<TenantInfo[]>(
        API_ENDPOINTS.OPERATION.TENANTS
      );

      console.log(`✅ 获取租户信息成功: ${response.length} 条数据`);
      return response;
    } catch (error: any) {
      console.error('❌ 获取租户信息失败:', error.message);
      throw new Error(error.message || '获取租户信息失败');
    }
  }
}
