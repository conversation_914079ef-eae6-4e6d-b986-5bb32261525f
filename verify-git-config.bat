@echo off
chcp 65001 >nul
echo === Git配置验证工具 ===
echo.

echo 检查当前Git用户配置...
echo.

REM 获取当前配置
for /f "tokens=*" %%i in ('git config user.name 2^>nul') do set CURRENT_NAME=%%i
for /f "tokens=*" %%i in ('git config user.email 2^>nul') do set CURRENT_EMAIL=%%i

echo 当前配置：
echo 用户名: %CURRENT_NAME%
echo 邮箱: %CURRENT_EMAIL%
echo.

REM 检查是否为期望的配置
if "%CURRENT_NAME%"=="glau" (
    if "%CURRENT_EMAIL%"=="<EMAIL>" (
        echo ✅ Git用户配置正确！
        echo.
        echo 📝 您可以正常提交代码：
        echo    git add .
        echo    git commit -m "您的提交信息"
        echo    git push
    ) else (
        echo ❌ 邮箱配置不正确
        echo 期望邮箱: <EMAIL>
        echo 请运行 setup-git-portable.bat 重新配置
    )
) else (
    echo ❌ 用户名配置不正确
    echo 期望用户名: glau
    echo 请运行 setup-git-portable.bat 重新配置
)

echo.
echo 📋 当前目录: %CD%
echo 🔧 如需重新配置，请运行: setup-git-portable.bat
echo.
pause