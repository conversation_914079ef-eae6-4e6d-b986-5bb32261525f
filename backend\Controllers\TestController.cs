/**
 * 测试控制器
 * 提供测试环境的数据初始化、清理等功能
 * 仅在测试环境下启用
 */

using Microsoft.AspNetCore.Mvc;
using MapStudio.Api.Data.Context;
using Microsoft.AspNetCore.Authorization;

namespace MapStudio.Api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [ApiExplorerSettings(IgnoreApi = true)] // 在API文档中隐藏
    public class TestController : ControllerBase
    {
        private readonly TestDatabaseInitializer _testDbInitializer;
        private readonly IConfiguration _configuration;
        private readonly ILogger<TestController> _logger;
        private readonly IWebHostEnvironment _environment;

        public TestController(
            TestDatabaseInitializer testDbInitializer,
            IConfiguration configuration,
            ILogger<TestController> logger,
            IWebHostEnvironment environment)
        {
            _testDbInitializer = testDbInitializer;
            _configuration = configuration;
            _logger = logger;
            _environment = environment;
        }

        /// <summary>
        /// 初始化测试数据
        /// </summary>
        [HttpPost("initialize")]
        public async Task<IActionResult> InitializeTestData([FromBody] TestInitializeRequest request)
        {
            // 只有在测试环境下才允许访问
            if (!IsTestEnvironment())
            {
                return NotFound();
            }

            try
            {
                _logger.LogInformation($"接收到测试数据初始化请求: {request.Mode}");

                switch (request.Mode?.ToLower())
                {
                    case "unit-test":
                        await InitializeUnitTestData();
                        break;
                    case "integration-test":
                        await InitializeIntegrationTestData();
                        break;
                    case "e2e-test":
                        await InitializeE2ETestData();
                        break;
                    default:
                        await _testDbInitializer.InitializeAsync();
                        break;
                }

                return Ok(new
                {
                    success = true,
                    message = "测试数据初始化完成",
                    mode = request.Mode,
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试数据初始化失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "测试数据初始化失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 清理测试数据
        /// </summary>
        [HttpPost("cleanup")]
        public async Task<IActionResult> CleanupTestData([FromBody] TestCleanupRequest request)
        {
            if (!IsTestEnvironment())
            {
                return NotFound();
            }

            try
            {
                _logger.LogInformation($"接收到测试数据清理请求: {request.Mode}");

                await _testDbInitializer.CleanTestDataAsync();

                return Ok(new
                {
                    success = true,
                    message = "测试数据清理完成",
                    mode = request.Mode,
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试数据清理失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "测试数据清理失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 重置测试数据
        /// </summary>
        [HttpPost("reset")]
        public async Task<IActionResult> ResetTestData([FromBody] TestResetRequest request)
        {
            if (!IsTestEnvironment())
            {
                return NotFound();
            }

            try
            {
                _logger.LogInformation("重置测试数据");

                await _testDbInitializer.ResetDatabaseAsync();

                return Ok(new
                {
                    success = true,
                    message = "测试数据重置完成",
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试数据重置失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "测试数据重置失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 获取测试环境状态
        /// </summary>
        [HttpGet("status")]
        public IActionResult GetTestStatus()
        {
            if (!IsTestEnvironment())
            {
                return NotFound();
            }

            var status = new
            {
                environment = _environment.EnvironmentName,
                isTestMode = IsTestEnvironment(),
                enableTestEndpoints = _configuration.GetValue<bool>("TestSettings:EnableTestEndpoints"),
                autoSeedData = _configuration.GetValue<bool>("TestSettings:AutoSeedData"),
                cleanupAfterTest = _configuration.GetValue<bool>("TestSettings:CleanupAfterTest"),
                mockExternalServices = _configuration.GetValue<bool>("TestSettings:MockExternalServices"),
                databaseConnection = _configuration.GetConnectionString("DefaultConnection")?.Replace("Pwd=", "Pwd=***"),
                timestamp = DateTime.UtcNow
            };

            return Ok(status);
        }

        /// <summary>
        /// 生成测试令牌
        /// </summary>
        [HttpPost("generate-token")]
        public IActionResult GenerateTestToken([FromBody] TestTokenRequest request)
        {
            if (!IsTestEnvironment())
            {
                return NotFound();
            }

            try
            {
                // 这里可以生成测试用的JWT令牌
                var token = GenerateJwtToken(request.UserId, request.Role, request.ExpirationMinutes ?? 30);

                return Ok(new
                {
                    success = true,
                    token = token,
                    userId = request.UserId,
                    role = request.Role,
                    expiresAt = DateTime.UtcNow.AddMinutes(request.ExpirationMinutes ?? 30)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成测试令牌失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "生成测试令牌失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 创建测试文件
        /// </summary>
        [HttpPost("create-file")]
        public async Task<IActionResult> CreateTestFile([FromBody] TestFileRequest request)
        {
            if (!IsTestEnvironment())
            {
                return NotFound();
            }

            try
            {
                var uploadPath = _configuration["FileStorage:BasePath"] ?? "./test_uploads";
                
                if (!Directory.Exists(uploadPath))
                {
                    Directory.CreateDirectory(uploadPath);
                }

                var fileName = request.FileName ?? $"test_file_{DateTime.Now:yyyyMMddHHmmss}.txt";
                var filePath = Path.Combine(uploadPath, fileName);
                var content = request.Content ?? "Test file content";

                await System.IO.File.WriteAllTextAsync(filePath, content);

                return Ok(new
                {
                    success = true,
                    fileName = fileName,
                    filePath = filePath,
                    size = content.Length,
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建测试文件失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "创建测试文件失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 检查是否为测试环境
        /// </summary>
        private bool IsTestEnvironment()
        {
            var enableTestEndpoints = _configuration.GetValue<bool>("TestSettings:EnableTestEndpoints");
            var isTestEnv = _environment.EnvironmentName.Equals("Testing", StringComparison.OrdinalIgnoreCase) ||
                           _environment.EnvironmentName.Equals("Test", StringComparison.OrdinalIgnoreCase);
            
            return enableTestEndpoints && isTestEnv;
        }

        /// <summary>
        /// 初始化单元测试数据
        /// </summary>
        private async Task InitializeUnitTestData()
        {
            _logger.LogInformation("初始化单元测试数据");
            
            // 清理现有数据
            await _testDbInitializer.CleanTestDataAsync();
            
            // 只创建最基本的测试数据
            await _testDbInitializer.SeedUsersAsync();
            await _testDbInitializer.SeedServiceTypesAsync();
            await _testDbInitializer.SeedPermissionScopesAsync();
        }

        /// <summary>
        /// 初始化集成测试数据
        /// </summary>
        private async Task InitializeIntegrationTestData()
        {
            _logger.LogInformation("初始化集成测试数据");
            
            // 完整的测试数据初始化
            await _testDbInitializer.InitializeAsync();
        }

        /// <summary>
        /// 初始化E2E测试数据
        /// </summary>
        private async Task InitializeE2ETestData()
        {
            _logger.LogInformation("初始化E2E测试数据");
            
            // 为E2E测试创建特定的数据集
            await _testDbInitializer.InitializeAsync();
            
            // 可以添加E2E测试特有的数据
        }

        /// <summary>
        /// 生成JWT令牌
        /// </summary>
        private string GenerateJwtToken(string userId, string role, int expirationMinutes)
        {
            // 这里应该使用与正式认证相同的JWT生成逻辑
            // 为简化示例，返回一个模拟令牌
            return $"test_token_{userId}_{role}_{DateTime.UtcNow.Ticks}";
        }
    }

    // 请求模型
    public class TestInitializeRequest
    {
        public string? Mode { get; set; }
    }

    public class TestCleanupRequest
    {
        public string? Mode { get; set; }
    }

    public class TestResetRequest
    {
        public bool Force { get; set; }
    }

    public class TestTokenRequest
    {
        public string UserId { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
        public int? ExpirationMinutes { get; set; }
    }

    public class TestFileRequest
    {
        public string? FileName { get; set; }
        public string? Content { get; set; }
    }
}