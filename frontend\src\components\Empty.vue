<template>
  <div class="flex flex-col items-center justify-center min-h-96 p-8">
    <div class="w-24 h-24 mb-6 text-gray-300">
      <svg viewBox="0 0 24 24" fill="currentColor" class="w-full h-full">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
      </svg>
    </div>
    <h3 class="text-xl font-semibold text-gray-600 mb-2">{{ title }}</h3>
    <p class="text-gray-500 text-center max-w-md">{{ description }}</p>
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title?: string
  description?: string
}

withDefaults(defineProps<Props>(), {
  title: '暂无内容',
  description: '这里还没有任何内容，请稍后再来查看。'
})
</script>