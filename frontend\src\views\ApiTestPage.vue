<template>
  <div class="p-8 max-w-4xl mx-auto">
    <div class="bg-white rounded-lg shadow-sm border p-6">
      <h1 class="text-2xl font-bold mb-6">前后端连接测试</h1>

      <!-- 配置信息 -->
      <div class="mb-8">
        <h2 class="text-lg font-semibold mb-4">当前配置</h2>
        <div class="bg-gray-50 rounded-lg p-4 space-y-2">
          <div><strong>前端地址:</strong> {{ frontendUrl }}</div>
          <div><strong>后端API地址:</strong> {{ apiConfig.baseURL }}</div>
          <div><strong>环境模式:</strong> {{ environmentInfo.mode }}</div>
          <div><strong>Mock模式:</strong> {{ apiConfig.useMock ? '启用' : '禁用' }}</div>
          <div><strong>超时时间:</strong> {{ apiConfig.timeout }}ms</div>
        </div>
      </div>

      <!-- 连接状态 -->
      <div class="mb-8">
        <h2 class="text-lg font-semibold mb-4">连接状态</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="border rounded-lg p-4">
            <div class="flex items-center justify-between mb-2">
              <span class="font-medium">API连接</span>
              <div :class="[
                'px-2 py-1 rounded text-sm',
                connectionStatus.api ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              ]">
                {{ connectionStatus.api ? '已连接' : '连接失败' }}
              </div>
            </div>
            <button
              @click="testApiConnection"
              :disabled="testing.api"
              class="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {{ testing.api ? '测试中...' : '测试API连接' }}
            </button>
          </div>

          <div class="border rounded-lg p-4">
            <div class="flex items-center justify-between mb-2">
              <span class="font-medium">服务状态</span>
              <div :class="[
                'px-2 py-1 rounded text-sm',
                connectionStatus.services ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              ]">
                {{ connectionStatus.services ? '正常' : '异常' }}
              </div>
            </div>
            <button
              @click="testServices"
              :disabled="testing.services"
              class="w-full px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
            >
              {{ testing.services ? '测试中...' : '测试服务' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 测试结果 -->
      <div class="mb-8">
        <h2 class="text-lg font-semibold mb-4">测试结果</h2>
        <div class="bg-gray-900 text-green-400 rounded-lg p-4 h-64 overflow-y-auto font-mono text-sm">
          <div v-for="(log, index) in logs" :key="index" class="mb-1">
            <span class="text-gray-500">{{ log.timestamp }}</span>
            <span :class="getLogColor(log.level)"> [{{ log.level }}]</span>
            {{ log.message }}
          </div>
          <div v-if="logs.length === 0" class="text-gray-500">
            点击测试按钮开始测试...
          </div>
        </div>
        <button
          @click="clearLogs"
          class="mt-2 px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
        >
          清除日志
        </button>
      </div>

      <!-- 快速操作 -->
      <div>
        <h2 class="text-lg font-semibold mb-4">快速操作</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            @click="toggleMockMode"
            :class="[
              'px-4 py-2 rounded font-medium',
              apiConfig.useMock
                ? 'bg-orange-500 text-white hover:bg-orange-600'
                : 'bg-green-500 text-white hover:bg-green-600'
            ]"
          >
            {{ apiConfig.useMock ? '切换到真实API' : '切换到Mock模式' }}
          </button>

          <button
            @click="testAllEndpoints"
            :disabled="testing.all"
            class="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600 disabled:opacity-50"
          >
            {{ testing.all ? '测试中...' : '测试所有端点' }}
          </button>

          <router-link
            to="/swagger"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-center"
          >
            打开API文档
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { httpClient, apiConfig, toggleMockMode as toggleMock, getEnvironmentInfo } from '@/services'

// 响应式数据
const frontendUrl = window.location.origin
const environmentInfo = getEnvironmentInfo()

const connectionStatus = ref({
  api: false,
  services: false
})

const testing = ref({
  api: false,
  services: false,
  all: false
})

const logs = ref<Array<{
  timestamp: string
  level: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR'
  message: string
}>>([])

// 日志记录
const addLog = (level: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR', message: string) => {
  logs.value.push({
    timestamp: new Date().toLocaleTimeString(),
    level,
    message
  })
}

const getLogColor = (level: string) => {
  switch (level) {
    case 'SUCCESS': return 'text-green-400'
    case 'WARNING': return 'text-yellow-400'
    case 'ERROR': return 'text-red-400'
    default: return 'text-blue-400'
  }
}

const clearLogs = () => {
  logs.value = []
}

// 测试API连接
const testApiConnection = async () => {
  testing.value.api = true
  addLog('INFO', '开始测试API连接...')

  try {
    const response = await httpClient.get('/Metadata/service-types')

    if (response && response.success) {
      connectionStatus.value.api = true
      addLog('SUCCESS', `API连接成功 - 获取到 ${response.data?.length || 0} 个服务类型`)
    } else {
      connectionStatus.value.api = false
      addLog('ERROR', 'API响应格式异常')
    }
  } catch (error: any) {
    connectionStatus.value.api = false
    addLog('ERROR', `API连接失败: ${error.message || error}`)
  } finally {
    testing.value.api = false
  }
}

// 测试服务
const testServices = async () => {
  testing.value.services = true
  addLog('INFO', '开始测试业务服务...')

  try {
    // 测试多个服务端点
    const tests = [
      { name: '服务类型', endpoint: '/Metadata/service-types' },
      { name: '权限范围', endpoint: '/Metadata/permission-scopes' }
    ]

    let successCount = 0

    for (const test of tests) {
      try {
        const response = await httpClient.get(test.endpoint)
        if (response && response.success) {
          addLog('SUCCESS', `${test.name}服务正常`)
          successCount++
        } else {
          addLog('WARNING', `${test.name}服务响应异常`)
        }
      } catch (error: any) {
        addLog('ERROR', `${test.name}服务失败: ${error.message}`)
      }
    }

    connectionStatus.value.services = successCount === tests.length

    if (connectionStatus.value.services) {
      addLog('SUCCESS', '所有业务服务测试通过')
    } else {
      addLog('WARNING', `部分业务服务异常 (${successCount}/${tests.length})`)
    }

  } catch (error: any) {
    connectionStatus.value.services = false
    addLog('ERROR', `服务测试失败: ${error.message}`)
  } finally {
    testing.value.services = false
  }
}

// 测试所有端点
const testAllEndpoints = async () => {
  testing.value.all = true
  addLog('INFO', '开始测试所有端点...')

  await testApiConnection()
  await testServices()

  testing.value.all = false
  addLog('INFO', '所有端点测试完成')
}

// 切换Mock模式
const toggleMockMode = () => {
  const newMode = !apiConfig.useMock
  toggleMock(newMode)
  addLog('INFO', `已切换到${newMode ? 'Mock' : '真实API'}模式`)
}

// 初始化
onMounted(async () => {
  addLog('INFO', '页面初始化完成')
  addLog('INFO', `当前API地址: ${apiConfig.baseURL}`)
  addLog('INFO', `Mock模式: ${apiConfig.useMock ? '启用' : '禁用'}`)

  // 自动进行一次连接测试
  await testApiConnection()
})
</script>
