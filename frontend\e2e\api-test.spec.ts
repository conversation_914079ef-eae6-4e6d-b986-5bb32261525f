import { test, expect } from '@playwright/test';

test.describe('API测试页面功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 访问API测试页面
    await page.goto('/api-test');
  });

  test('应该正确加载API测试页面', async ({ page }) => {
    // 检查页面标题或主要内容
    await expect(page.locator('h1, h2, .page-title')).toBeVisible();

    // 确保页面加载完成
    await expect(page.locator('body')).toBeVisible();
  });

  test('应该能够测试API连接', async ({ page }) => {
    // 查找API连接测试相关的按钮或控件
    const testButtons = page.locator('button:has-text("测试"), button:has-text("连接"), .test-api, .check-connection');

    if (await testButtons.count() > 0) {
      const testButton = testButtons.first();
      await expect(testButton).toBeVisible();

      // 点击测试按钮
      await testButton.click();

      // 等待响应
      await page.waitForTimeout(2000);

      // 检查是否有结果显示
      const results = page.locator('.result, .response, .status, .api-result');
      if (await results.count() > 0) {
        await expect(results.first()).toBeVisible();
      }
    }
  });

  test('应该显示环境信息', async ({ page }) => {
    // 查找环境信息相关内容
    const envInfo = page.locator('text=环境, text=Environment, .env-info, .environment');

    if (await envInfo.count() > 0) {
      await expect(envInfo.first()).toBeVisible();
    }

    // 检查是否显示了开发/生产环境信息
    const envIndicators = page.locator('text=开发, text=生产, text=development, text=production');
    if (await envIndicators.count() > 0) {
      await expect(envIndicators.first()).toBeVisible();
    }
  });

  test('应该能够切换Mock模式', async ({ page }) => {
    // 查找Mock模式切换控件
    const mockToggle = page.locator('button:has-text("Mock"), input[type="checkbox"], .mock-toggle, .switch-mock');

    if (await mockToggle.count() > 0) {
      const toggle = mockToggle.first();
      await expect(toggle).toBeVisible();

      // 尝试切换
      await toggle.click();

      // 等待状态更新
      await page.waitForTimeout(1000);

      // 检查是否有状态变化指示
      const statusIndicators = page.locator('.mock-status, .api-mode, text=Mock, text=真实');
      if (await statusIndicators.count() > 0) {
        await expect(statusIndicators.first()).toBeVisible();
      }
    }
  });

  test('应该显示API端点列表', async ({ page }) => {
    // 查找API端点列表
    const apiList = page.locator('.api-list, .endpoint-list, ul li, .api-item');

    if (await apiList.count() > 0) {
      await expect(apiList.first()).toBeVisible();

      // 检查是否有多个API端点
      if (await apiList.count() > 1) {
        expect(await apiList.count()).toBeGreaterThan(1);
      }
    }
  });

  test('应该能够测试具体的API端点', async ({ page }) => {
    // 查找具体的API测试按钮
    const apiTestButtons = page.locator('button:has-text("GET"), button:has-text("POST"), .test-endpoint');

    if (await apiTestButtons.count() > 0) {
      const testButton = apiTestButtons.first();
      await expect(testButton).toBeVisible();

      // 点击测试
      await testButton.click();

      // 等待响应
      await page.waitForTimeout(3000);

      // 检查响应结果
      const responseArea = page.locator('.response, .result, pre, .json-response');
      if (await responseArea.count() > 0) {
        await expect(responseArea.first()).toBeVisible();
      }
    }
  });

  test('应该显示请求和响应信息', async ({ page }) => {
    // 查找请求信息显示区域
    const requestInfo = page.locator('.request-info, .request-details, .url, .method');

    if (await requestInfo.count() > 0) {
      await expect(requestInfo.first()).toBeVisible();
    }

    // 查找响应信息显示区域
    const responseInfo = page.locator('.response-info, .response-details, .status-code, .response-time');

    if (await responseInfo.count() > 0) {
      await expect(responseInfo.first()).toBeVisible();
    }
  });

  test('应该能够返回主页', async ({ page }) => {
    // 查找返回主页的链接或按钮
    const homeLink = page.locator('a[href="/"], a:has-text("首页"), a:has-text("返回"), .back-home');

    if (await homeLink.count() > 0) {
      const link = homeLink.first();
      await expect(link).toBeVisible();

      // 点击返回
      await link.click();

      // 验证回到首页
      await expect(page.locator('h1:has-text("地图工作室SaaS平台")')).toBeVisible();
    }
  });

  test('页面应该是响应式的', async ({ page }) => {
    // 测试桌面端
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.reload();
    await expect(page.locator('body')).toBeVisible();

    // 测试平板端
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.reload();
    await expect(page.locator('body')).toBeVisible();

    // 测试移动端
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();
    await expect(page.locator('body')).toBeVisible();

    // 内容应该仍然可见和可用
    const mainContent = page.locator('main, .main-content, .container');
    if (await mainContent.count() > 0) {
      await expect(mainContent.first()).toBeVisible();
    }
  });
});
