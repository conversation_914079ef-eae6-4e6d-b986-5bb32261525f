# 后端统计逻辑实现计划

## 概述
本文档描述了如何在后端实现服务状态统计功能，包括修改TenantService中的GetServiceStatusAsync方法以支持从数据库获取实时统计数据。

## 实现步骤

### 1. 创建ServiceStatusResponse DTO
首先需要在后端创建ServiceStatusResponse DTO类，用于封装四个统计指标：
- TotalUsers (总用户数)
- ActiveUsers (活跃用户数)
- TodayVisits (今日访问量)
- PendingApplications (待审批申请数)

### 2. 修改TenantService接口
在ITenantService接口中添加新的方法定义：
```csharp
Task<ServiceStatusResponse> GetServiceStatusStatisticsAsync();
```

### 3. 实现统计逻辑
在TenantService的GetServiceStatusStatisticsAsync方法中实现具体的统计逻辑：

#### 3.1 总用户数统计
```csharp
var totalUsers = await _db.Queryable<AuthApplication>().CountAsync();
```

#### 3.2 活跃用户统计
```csharp
var activeUsers = await _db.Queryable<OperationLog>()
    .Where(log => log.OperatedAt.Date == DateTime.Today)
    .GroupBy(log => log.TenantId)
    .CountAsync();
```

#### 3.3 今日访问量统计
```csharp
var todayVisits = await _db.Queryable<OperationLog>()
    .Where(log => log.OperatedAt.Date == DateTime.Today)
    .CountAsync();
```

#### 3.4 待审批申请统计
```csharp
var pendingApplications = await _db.Queryable<AuthApplication>()
    .Where(app => app.Status == "Pending")
    .CountAsync();
```

### 4. 修改现有GetServiceStatusAsync方法
更新现有的GetServiceStatusAsync方法，使其调用新的统计方法并返回结果。

### 5. 错误处理
添加适当的异常处理和日志记录，确保服务的稳定性。

## 性能优化建议

### 5.1 数据库索引
为提高查询性能，建议在以下字段上创建索引：
- ms_auth_applications表的Status字段
- ms_operation_logs表的OperatedAt和TenantId字段

### 5.2 查询优化
- 使用异步查询避免阻塞
- 尽量在数据库层面完成统计计算，避免加载大量数据到内存

## 测试计划

### 6.1 单元测试
- 测试每个统计指标的正确性
- 测试边界条件（如空数据、大量数据等）
- 测试异常情况处理

### 6.2 集成测试
- 测试API端点的响应
- 验证数据一致性
- 测试性能表现

## 部署注意事项
- 确保数据库连接配置正确
- 验证新添加的DTO类已正确编译
- 测试在不同数据量下的性能表现