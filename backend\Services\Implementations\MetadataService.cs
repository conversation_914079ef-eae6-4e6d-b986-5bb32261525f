using SqlSugar;
using MapStudio.Api.Services.Interfaces;
using MapStudio.Api.Models.DTOs;
using MapStudio.Api.Models.Entities;

namespace MapStudio.Api.Services.Implementations;

public class MetadataService : IMetadataService
{
    private readonly ISqlSugarClient _db;
    private readonly ILogger<MetadataService> _logger;

    public MetadataService(ISqlSugarClient db, ILogger<MetadataService> logger)
    {
        _db = db;
        _logger = logger;
    }

    public async Task<List<ServiceTypeOption>> GetServiceTypesAsync()
    {
        try
        {
            var serviceTypes = await _db.Queryable<ServiceType>()
                .Where(st => st.IsActive)
                .ToListAsync();

            return serviceTypes.Select(st => new ServiceTypeOption
            {
                Id = st.Id,
                Name = st.Name,
                Description = st.Description
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取服务类型失败");
            throw;
        }
    }

    public async Task<List<PermissionScopeOption>> GetPermissionScopesAsync()
    {
        try
        {
            var permissionScopes = await _db.Queryable<PermissionScope>()
                .ToListAsync();

            return permissionScopes.Select(ps => new PermissionScopeOption
            {
                Id = ps.Id,
                Name = ps.Name,
                Description = ps.Description,
                Permissions = ps.Permissions
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取权限范围失败");
            throw;
        }
    }

    public async Task<List<AuthPeriodOption>> GetAuthPeriodsAsync()
    {
        // 返回固定的授权期限选项
        return await Task.FromResult(new List<AuthPeriodOption>
        {
            new AuthPeriodOption { Id = "3months", Name = "3个月", Description = "3个月有效期" },
            new AuthPeriodOption { Id = "6months", Name = "6个月", Description = "6个月有效期" },
            new AuthPeriodOption { Id = "1year", Name = "1年", Description = "1年有效期" },
            new AuthPeriodOption { Id = "2years", Name = "2年", Description = "2年有效期" },
            new AuthPeriodOption { Id = "3years", Name = "3年", Description = "3年有效期" }
        });
    }

    public async Task<List<TenantSizeOption>> GetTenantSizesAsync()
    {
        // 返回固定的租户规模选项
        return await Task.FromResult(new List<TenantSizeOption>
        {
            new TenantSizeOption { Id = "small", Name = "小型", Description = "1-50人" },
            new TenantSizeOption { Id = "medium", Name = "中型", Description = "51-200人" },
            new TenantSizeOption { Id = "large", Name = "大型", Description = "201-1000人" },
            new TenantSizeOption { Id = "enterprise", Name = "企业级", Description = "1000人以上" }
        });
    }

    public async Task<List<IndustryOption>> GetIndustriesAsync()
    {
        // 返回固定的行业选项
        return await Task.FromResult(new List<IndustryOption>
        {
            new IndustryOption { Id = "government", Name = "政府机构", Description = "政府部门和事业单位" },
            new IndustryOption { Id = "education", Name = "教育科研", Description = "学校和科研院所" },
            new IndustryOption { Id = "healthcare", Name = "医疗卫生", Description = "医院和卫生机构" },
            new IndustryOption { Id = "finance", Name = "金融保险", Description = "银行、保险等金融机构" },
            new IndustryOption { Id = "manufacturing", Name = "制造业", Description = "生产制造企业" },
            new IndustryOption { Id = "transportation", Name = "交通运输", Description = "物流和运输企业" },
            new IndustryOption { Id = "energy", Name = "能源电力", Description = "电力、石油、燃气等能源企业" },
            new IndustryOption { Id = "construction", Name = "建筑工程", Description = "建筑和工程企业" },
            new IndustryOption { Id = "agriculture", Name = "农林牧渔", Description = "农业和林业企业" },
            new IndustryOption { Id = "technology", Name = "科技互联网", Description = "互联网和科技企业" },
            new IndustryOption { Id = "retail", Name = "零售商贸", Description = "零售和商贸企业" },
            new IndustryOption { Id = "other", Name = "其他", Description = "其他行业" }
        });
    }
}