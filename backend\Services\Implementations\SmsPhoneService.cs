using SqlSugar;
using MapStudio.Api.Services.Interfaces;
using MapStudio.Api.Models.DTOs;
using MapStudio.Api.Models.Entities;

namespace MapStudio.Api.Services.Implementations
{
    /// <summary>
    /// 短信接收手机号服务实现
    /// </summary>
    public class SmsPhoneService : ISmsPhoneService
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<SmsPhoneService> _logger;

        public SmsPhoneService(ISqlSugarClient db, ILogger<SmsPhoneService> logger)
        {
            _db = db;
            _logger = logger;
        }

        /// <summary>
        /// 获取短信接收手机号列表，按创建时间倒序排序
        /// </summary>
        /// <returns>短信接收手机号列表</returns>
        public async Task<SmsPhoneSettingListResponse> GetSmsPhoneSettingsAsync()
        {
            try
            {
                var settings = await _db.Queryable<SmsPhoneSetting>()
                    .OrderBy(s => s.CreateDate, OrderByType.Desc)
                    .ToListAsync();

                var dtoList = settings.Select(s => new SmsPhoneSettingDto
                {
                    Id = s.Id,
                    Phone = s.Phone,
                    Levels = string.IsNullOrEmpty(s.Levels) ? new List<string>() : System.Text.Json.JsonSerializer.Deserialize<List<string>>(s.Levels) ?? new List<string>(),
                    Status = s.Status,
                    CreateDate = s.CreateDate
                }).ToList();

                return new SmsPhoneSettingListResponse
                {
                    Success = true,
                    Data = dtoList,
                    Message = "获取短信接收手机号列表成功"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取短信接收手机号列表失败");
                return new SmsPhoneSettingListResponse
                {
                    Success = false,
                    Message = "获取短信接收手机号列表失败: " + ex.Message,
                    ErrorCode = "GET_PHONE_LIST_FAILED"
                };
            }
        }

        /// <summary>
        /// 添加短信接收手机号
        /// </summary>
        /// <param name="phone">手机号</param>
        /// <param name="levels">告警级别</param>
        /// <returns>添加结果</returns>
        public async Task<SmsPhoneSettingResponse> AddSmsPhoneSettingAsync(string phone, string[] levels)
        {
            try
            {
                // 检查手机号是否已存在
                var existingPhone = await _db.Queryable<SmsPhoneSetting>()
                    .FirstAsync(s => s.Phone == phone);
                if (existingPhone != null)
                {
                    return new SmsPhoneSettingResponse
                    {
                        Success = false,
                        Message = "手机号已存在",
                        ErrorCode = "PHONE_EXISTS"
                    };
                }

                var setting = new SmsPhoneSetting
                {
                    Phone = phone,
                    Levels = System.Text.Json.JsonSerializer.Serialize(levels),
                    Status = true, // 默认启用
                    CreateDate = DateTime.Now
                };

                var insertedId = await _db.Insertable(setting).ExecuteReturnIdentityAsync();
                setting.Id = insertedId;

                var dto = new SmsPhoneSettingDto
                {
                    Id = setting.Id,
                    Phone = setting.Phone,
                    Levels = levels.ToList(),
                    Status = setting.Status,
                    CreateDate = setting.CreateDate
                };

                return new SmsPhoneSettingResponse
                {
                    Success = true,
                    Data = dto,
                    Message = "添加短信接收手机号成功"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加短信接收手机号失败");
                return new SmsPhoneSettingResponse
                {
                    Success = false,
                    Message = "添加短信接收手机号失败: " + ex.Message,
                    ErrorCode = "ADD_PHONE_FAILED"
                };
            }
        }

        /// <summary>
        /// 更新短信接收手机号状态
        /// </summary>
        /// <param name="id">设置ID</param>
        /// <param name="status">状态</param>
        /// <param name="levels">告警级别</param>
        /// <returns>更新结果</returns>
        public async Task<SmsPhoneSettingResponse> UpdateSmsPhoneSettingAsync(int id, bool status, string[] levels)
        {
            try
            {
                var setting = await _db.Queryable<SmsPhoneSetting>()
                    .FirstAsync(s => s.Id == id);
                if (setting == null)
                {
                    return new SmsPhoneSettingResponse
                    {
                        Success = false,
                        Message = "未找到指定的手机号设置",
                        ErrorCode = "PHONE_NOT_FOUND"
                    };
                }

                setting.Status = status;
                setting.Levels = System.Text.Json.JsonSerializer.Serialize(levels);

                await _db.Updateable(setting).ExecuteCommandAsync();

                var dto = new SmsPhoneSettingDto
                {
                    Id = setting.Id,
                    Phone = setting.Phone,
                    Levels = levels.ToList(),
                    Status = setting.Status,
                    CreateDate = setting.CreateDate
                };

                return new SmsPhoneSettingResponse
                {
                    Success = true,
                    Data = dto,
                    Message = "更新短信接收手机号状态成功"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新短信接收手机号状态失败");
                return new SmsPhoneSettingResponse
                {
                    Success = false,
                    Message = "更新短信接收手机号状态失败: " + ex.Message,
                    ErrorCode = "UPDATE_PHONE_FAILED"
                };
            }
        }

        /// <summary>
        /// 删除短信接收手机号
        /// </summary>
        /// <param name="id">设置ID</param>
        /// <returns>删除结果</returns>
        public async Task<SmsPhoneSettingResponse> DeleteSmsPhoneSettingAsync(int id)
        {
            try
            {
                var setting = await _db.Queryable<SmsPhoneSetting>()
                    .FirstAsync(s => s.Id == id);
                if (setting == null)
                {
                    return new SmsPhoneSettingResponse
                    {
                        Success = false,
                        Message = "未找到指定的手机号设置",
                        ErrorCode = "PHONE_NOT_FOUND"
                    };
                }

                await _db.Deleteable<SmsPhoneSetting>().Where(s => s.Id == id).ExecuteCommandAsync();

                return new SmsPhoneSettingResponse
                {
                    Success = true,
                    Message = "删除短信接收手机号成功"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除短信接收手机号失败");
                return new SmsPhoneSettingResponse
                {
                    Success = false,
                    Message = "删除短信接收手机号失败: " + ex.Message,
                    ErrorCode = "DELETE_PHONE_FAILED"
                };
            }
        }
    }
}