{"ConnectionStrings": {"DefaultConnection": "Server=192.168.2.119;Port=3307;Database=MTNOH_AAA_Platform;Uid=dtauser;Pwd=dtauser;charset=utf8mb4;"}, "JwtSettings": {"Enabled": false, "SecretKey": "MapStudio2025_SecretKey_ForJWT_Authentication_Development", "Issuer": "MapStudio.Api", "Audience": "MapStudio.Frontend", "ExpirationInMinutes": 1440}, "FileStorage": {"BasePath": "./uploads", "MaxFileSize": 5242880, "AllowedExtensions": [".pdf", ".jpg", ".jpeg", ".png", ".doc", ".docx"]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "CorsSettings": {"AllowedOrigins": ["http://localhost:5173", "http://localhost:3000", "http://localhost:3001", "http://localhost:3002", "https://localhost:7007"]}, "SmsService": {"ServiceUrl": "http://*************:20333/service/smsProviderService"}}