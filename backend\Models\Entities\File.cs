using SqlSugar;

namespace MapStudio.Api.Models.Entities
{
    /// <summary>
    /// 文件实体，对应数据库表 ms_files
    /// </summary>
    [SugarTable("ms_files")]
    public class File
    {
        /// <summary>
        /// 文件ID (主键)
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, Length = 36)]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 文件名
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = false)]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// 原始文件名
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = false)]
        public string OriginalFileName { get; set; } = string.Empty;

        /// <summary>
        /// 文件类型
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        public string FileType { get; set; } = string.Empty;

        /// <summary>
        /// 文件路径
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = false)]
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// 文件大小 (字节)
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public long FileSize { get; set; }

        /// <summary>
        /// 内容类型 (MIME类型)
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        public string ContentType { get; set; } = string.Empty;

        /// <summary>
        /// 上传时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime UploadTime { get; set; }

        /// <summary>
        /// 上传用户ID
        /// </summary>
        [SugarColumn(Length = 36, IsNullable = true)]
        public string? UploadUserId { get; set; }

        /// <summary>
        /// 关联的租户ID
        /// </summary>
        [SugarColumn(Length = 36, IsNullable = true)]
        public string? RelatedTenantId { get; set; }
    }
}