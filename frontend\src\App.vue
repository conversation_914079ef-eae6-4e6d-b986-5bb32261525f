<template>
  <div id="app">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center min-h-screen bg-gray-50">
      <div class="flex flex-col items-center">
        <div class="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600">加载中...</p>
      </div>
    </div>

    <!-- 登录界面 -->
    <div v-else-if="!isAuthenticated" class="flex items-center justify-center min-h-screen bg-gray-50">
      <div class="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md">
        <div class="text-center">
          <h2 class="text-2xl font-bold text-gray-900">地图工作室SaaS平台</h2>
          <p class="mt-2 text-gray-600">请登录以继续</p>
        </div>
        <button
          @click="handleLogin"
          class="w-full px-4 py-2 font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
        >
          模拟登录
        </button>
      </div>
    </div>

    <!-- 主应用界面 -->
    <router-view v-else />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 模拟认证状态
const isAuthenticated = ref(false)
const loading = ref(true)

const handleLogin = async () => {
  isAuthenticated.value = true
}

// 组件挂载时检查认证状态
onMounted(() => {
  // 模拟加载过程
  setTimeout(() => {
    isAuthenticated.value = true
    loading.value = false
  }, 1000)
})
</script>

<style>
/* 全局样式已在 index.css 中定义 */
</style>
