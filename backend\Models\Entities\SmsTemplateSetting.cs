using SqlSugar;

namespace MapStudio.Api.Models.Entities
{
    /// <summary>
    /// 短信模板设置实体，对应数据库表 ms_sms_template_settings
    /// </summary>
    [SugarTable("ms_sms_template_settings")]
    public class SmsTemplateSetting
    {
        /// <summary>
        /// 模板ID (自增主键)
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int TempId { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        [SugarColumn(Length = 8000, IsNullable = false)]
        public string TemplateName { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime UpdatedAt { get; set; }
    }
}