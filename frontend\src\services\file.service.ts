/**
 * 文件管理服务
 * 处理文件上传、下载、预览等功能
 */

import { httpClient } from '../utils/http.client';
import { API_ENDPOINTS } from '../config/endpoints';
import type { FileUploadResponse, FileInfo } from './types';

export class FileService {
  /**
   * 上传文件
   * @param file 文件对象
   * @param fileType 文件类型（businessLicense, organizationCode等）
   * @param onProgress 上传进度回调
   */
  static async uploadFile(
    file: File,
    fileType: string,
    onProgress?: (progress: number) => void
  ): Promise<FileUploadResponse> {
    try {
      console.log('📁 开始上传文件:', file.name);

      // 验证文件
      this.validateFile(file);

      // 创建FormData
      const formData = new FormData();
      formData.append('file', file);
      formData.append('fileType', fileType);

      const response = await httpClient.upload<FileUploadResponse>(
        API_ENDPOINTS.FILES.UPLOAD,
        file,
        onProgress
      );

      console.log('✅ 文件上传成功:', response.fileId);
      return response;
    } catch (error: any) {
      console.error('❌ 文件上传失败:', error.message);
      throw new Error(error.message || '文件上传失败');
    }
  }

  /**
   * 下载文件
   * @param fileId 文件ID
   * @param fileName 文件名（可选，用于保存时的默认名称）
   */
  static async downloadFile(fileId: string, fileName?: string): Promise<void> {
    try {
      console.log('⬇️ 开始下载文件:', fileId);

      if (!fileId) {
        throw new Error('文件ID不能为空');
      }

      // 创建下载链接
      const downloadUrl = `${httpClient.getBaseURL()}${API_ENDPOINTS.FILES.DOWNLOAD(fileId)}`;

      // 获取认证token
      const token = localStorage.getItem('auth_token');

      // 创建临时链接进行下载
      const link = document.createElement('a');
      link.href = downloadUrl;

      if (token) {
        // 如果有token，通过fetch下载以包含认证头
        const response = await fetch(downloadUrl, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error('下载失败');
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        link.href = url;
      }

      if (fileName) {
        link.download = fileName;
      }

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 清理临时URL
      if (link.href.startsWith('blob:')) {
        window.URL.revokeObjectURL(link.href);
      }

      console.log('✅ 文件下载完成');
    } catch (error: any) {
      console.error('❌ 文件下载失败:', error.message);
      throw new Error(error.message || '文件下载失败');
    }
  }

  /**
   * 获取文件预览信息
   * @param fileId 文件ID
   */
  static async getFilePreview(fileId: string): Promise<any> {
    try {
      console.log('👁️ 获取文件预览:', fileId);

      if (!fileId) {
        throw new Error('文件ID不能为空');
      }

      const response = await httpClient.get(
        API_ENDPOINTS.FILES.PREVIEW(fileId)
      );

      console.log('✅ 获取文件预览成功');
      return response;
    } catch (error: any) {
      console.error('❌ 获取文件预览失败:', error.message);
      throw new Error(error.message || '获取文件预览失败');
    }
  }

  /**
   * 删除文件
   * @param fileId 文件ID
   */
  static async deleteFile(fileId: string): Promise<void> {
    try {
      console.log('🗑️ 删除文件:', fileId);

      if (!fileId) {
        throw new Error('文件ID不能为空');
      }

      await httpClient.delete(API_ENDPOINTS.FILES.DELETE(fileId));

      console.log('✅ 文件删除成功');
    } catch (error: any) {
      console.error('❌ 文件删除失败:', error.message);
      throw new Error(error.message || '文件删除失败');
    }
  }

  /**
   * 批量上传文件
   * @param files 文件列表
   * @param fileType 文件类型
   * @param onProgress 总体进度回调
   */
  static async uploadMultipleFiles(
    files: File[],
    fileType: string,
    onProgress?: (progress: number) => void
  ): Promise<FileUploadResponse[]> {
    try {
      console.log(`📁 开始批量上传文件: ${files.length} 个文件`);

      if (!files || files.length === 0) {
        throw new Error('请选择要上传的文件');
      }

      const results: FileUploadResponse[] = [];
      let completedCount = 0;

      // 并发上传文件
      const uploadPromises = files.map(async (file, index) => {
        try {
          const result = await this.uploadFile(file, fileType, (progress) => {
            // 计算总体进度
            const overallProgress = ((completedCount + progress / 100) / files.length) * 100;
            onProgress?.(Math.round(overallProgress));
          });

          completedCount++;
          results[index] = result;

          return result;
        } catch (error) {
          console.error(`文件 ${file.name} 上传失败:`, error);
          throw error;
        }
      });

      await Promise.all(uploadPromises);

      console.log(`✅ 批量上传完成: ${results.length} 个文件`);
      return results;
    } catch (error: any) {
      console.error('❌ 批量上传失败:', error.message);
      throw new Error(error.message || '批量上传失败');
    }
  }

  /**
   * 验证文件
   * @param file 文件对象
   */
  private static validateFile(file: File): void {
    if (!file) {
      throw new Error('请选择要上传的文件');
    }

    // 检查文件大小（5MB限制）
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      throw new Error('文件大小不能超过5MB');
    }

    // 检查文件类型
    const allowedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/jpg',
      'image/png',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    if (!allowedTypes.includes(file.type)) {
      throw new Error('只支持PDF、JPG、PNG、DOC、DOCX格式的文件');
    }

    // 检查文件扩展名
    const allowedExtensions = ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx'];
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

    if (!allowedExtensions.includes(fileExtension)) {
      throw new Error('文件扩展名不支持');
    }
  }

  /**
   * 格式化文件大小
   * @param bytes 字节数
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 获取文件图标
   * @param fileName 文件名
   */
  static getFileIcon(fileName: string): string {
    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));

    const iconMap: { [key: string]: string } = {
      '.pdf': '📄',
      '.doc': '📝',
      '.docx': '📝',
      '.jpg': '🖼️',
      '.jpeg': '🖼️',
      '.png': '🖼️',
      '.gif': '🖼️'
    };

    return iconMap[extension] || '📁';
  }

  /**
   * 检查文件是否为图片
   * @param fileName 文件名
   */
  static isImageFile(fileName: string): boolean {
    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    return imageExtensions.includes(extension);
  }

  /**
   * 检查文件是否可预览
   * @param fileName 文件名
   */
  static isPreviewable(fileName: string): boolean {
    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    const previewableExtensions = ['.pdf', '.jpg', '.jpeg', '.png', '.gif'];
    return previewableExtensions.includes(extension);
  }

  /**
   * 创建文件预览URL
   * @param fileId 文件ID
   * @param token 认证令牌（可选）
   */
  static createPreviewUrl(fileId: string, token?: string): string {
    const baseUrl = httpClient.getBaseURL();
    let url = `${baseUrl}${API_ENDPOINTS.FILES.PREVIEW(fileId)}`;

    if (token) {
      url += `?token=${encodeURIComponent(token)}`;
    }

    return url;
  }

  /**
   * 从URL读取文件并转换为File对象
   * @param url 文件URL
   * @param fileName 文件名
   */
  static async urlToFile(url: string, fileName: string): Promise<File> {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      return new File([blob], fileName, { type: blob.type });
    } catch (error: any) {
      console.error('URL转文件失败:', error.message);
      throw new Error('读取文件失败');
    }
  }

  /**
   * 压缩图片文件
   * @param file 图片文件
   * @param quality 压缩质量 (0-1)
   * @param maxWidth 最大宽度
   */
  static async compressImage(
    file: File,
    quality: number = 0.8,
    maxWidth: number = 1920
  ): Promise<File> {
    return new Promise((resolve, reject) => {
      if (!this.isImageFile(file.name)) {
        resolve(file); // 非图片文件直接返回
        return;
      }

      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // 计算压缩后的尺寸
        let { width, height } = img;
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }

        canvas.width = width;
        canvas.height = height;

        // 绘制压缩后的图片
        ctx?.drawImage(img, 0, 0, width, height);

        // 转换为Blob
        canvas.toBlob((blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            });
            resolve(compressedFile);
          } else {
            reject(new Error('图片压缩失败'));
          }
        }, file.type, quality);
      };

      img.onerror = () => reject(new Error('图片加载失败'));
      img.src = URL.createObjectURL(file);
    });
  }
}
