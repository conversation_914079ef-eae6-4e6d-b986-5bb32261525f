using SqlSugar;

namespace MapStudio.Api.Models.Entities
{
    /// <summary>
    /// 短信接收手机号设置实体，对应数据库表 ms_sms_phone_settings
    /// </summary>
    [SugarTable("ms_sms_phone_settings")]
    public class SmsPhoneSetting
    {
        /// <summary>
        /// 设置ID (自增主键)
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false)]
        public string Phone { get; set; } = string.Empty;

        /// <summary>
        /// 告警级别
        /// </summary>
        [SugarColumn(ColumnDataType = "json", IsNullable = true)]
        public string? Levels { get; set; }

        /// <summary>
        /// 状态 (默认启用)
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool Status { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreateDate { get; set; }
    }
}