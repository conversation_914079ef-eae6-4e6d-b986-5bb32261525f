<template>
  <div class="p-6 space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">租户服务平均日操作统计</h1>
    </div>

    <!-- 筛选条件卡片 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">租户选择</label>
          <select
            v-model="selectedTenant"
            :disabled="tenantsLoading"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            name="tenant"
          >
            <option v-if="tenantsLoading" value="" disabled>加载中...</option>
            <option v-for="t in tenantOptions" :key="t.id" :value="t.id">{{ t.name }}</option>
          </select>
          <div v-if="tenantsError" class="text-xs text-red-600 mt-1">
            {{ tenantsError }}
          </div>
        </div>
      
      </div>
    </div>

    <!-- 数据概览卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">租户总数</p>
            <h3 class="mt-1 text-2xl font-bold text-gray-900">{{ dailyStatsSummary.totalTenants }}</h3>
          </div>
          <div class="p-3 bg-blue-100 rounded-lg">
            <i class="fa-solid fa-building text-blue-600 text-xl"></i>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">平均日操作次数</p>
            <h3 class="mt-1 text-2xl font-bold text-gray-900">
              {{ dailyStatsSummary.averageDailyOperations }}
            </h3>
          </div>
          <div class="p-3 bg-purple-100 rounded-lg">
            <i class="fa-solid fa-mouse-pointer text-purple-600 text-xl"></i>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">最大日操作次数</p>
            <h3 class="mt-1 text-2xl font-bold text-gray-900">
              {{ dailyStatsSummary.maxDailyOperations }}
            </h3>
          </div>
          <div class="p-3 bg-green-100 rounded-lg">
            <i class="fa-solid fa-trophy text-green-600 text-xl"></i>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">操作总量</p>
            <h3 class="mt-1 text-2xl font-bold text-gray-900">
              {{ dailyStatsSummary.totalOperations.toLocaleString() }}
            </h3>
          </div>
          <div class="p-3 bg-orange-100 rounded-lg">
            <i class="fa-solid fa-calculator text-orange-600 text-xl"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计数据错误提示 -->
    <div v-if="summaryError" class="bg-red-50 border border-red-200 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <i class="fa-solid fa-exclamation-circle text-red-400"></i>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">统计数据加载失败</h3>
          <div class="mt-2 text-sm text-red-700">
            <p>{{ summaryError }}</p>
          </div>
          <div class="mt-4">
            <button
              @click="loadDailyStatsSummary"
              class="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200"
            >
              重新加载
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计数据加载提示 -->
    <div v-if="summaryLoading" class="bg-blue-50 border border-blue-200 rounded-md p-4">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <i class="fa-solid fa-spinner fa-spin text-blue-400"></i>
        </div>
        <div class="ml-3">
          <p class="text-sm text-blue-800">正在加载统计数据...</p>
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-50 border border-red-200 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <i class="fa-solid fa-exclamation-circle text-red-400"></i>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">加载失败</h3>
          <div class="mt-2 text-sm text-red-700">
            <p>{{ error }}</p>
          </div>
          <div class="mt-4">
            <button
              @click="loadDailyStats"
              class="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200"
            >
              重新加载
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载提示 -->
    <div v-if="loading" class="bg-blue-50 border border-blue-200 rounded-md p-4">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <i class="fa-solid fa-spinner fa-spin text-blue-400"></i>
        </div>
        <div class="ml-3">
          <p class="text-sm text-blue-800">正在加载日度统计数据...</p>
        </div>
      </div>
    </div>

    <!-- 图表展示区域 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-gray-900">租户平均日操作次数分布</h2>
        <div class="flex space-x-3">
          <button class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            <i class="fa-solid fa-download mr-2"></i> 导出数据
          </button>
          <button class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            <i class="fa-solid fa-filter mr-2"></i> 高级筛选
          </button>
        </div>
      </div>

      <div class="h-96">
        <ChartBase :option="chartOptions" :loading="chartLoading" class="h-96" />
      </div>
    </div>

    <!-- 分组统计卡片 -->
    <div v-if="false" class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div v-if="false" class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">按服务类型分组</h2>
        <div class="space-y-4">
          <div v-for="(g, idx) in serviceTypeGroupStats" :key="idx">
            <div class="flex justify-between items-center mb-2">
              <div class="flex items-center">
                <div class="w-3 h-3 rounded-full mr-2" :style="{ backgroundColor: g.color }"></div>
                <span class="text-sm font-medium text-gray-700">{{ g.name }}</span>
              </div>
              <div class="text-right">
                <div class="text-lg font-bold text-gray-900">{{ g.avgOperations }} 次/日</div>
                <div class="text-xs text-gray-500">{{ g.count }} 个租户</div>
              </div>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div
                class="h-2 rounded-full"
                :style="{
                  width: Math.min((g.avgOperations / 600) * 100, 100) + '%',
                  backgroundColor: g.color
                }"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="false" class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">按租户规模分组</h2>
        <div class="space-y-4">
          <div v-for="(g, idx) in sizeGroupStats" :key="idx">
            <div class="flex justify-between items-center mb-2">
              <div class="flex items-center">
                <div class="w-3 h-3 rounded-full mr-2" :style="{ backgroundColor: g.color }"></div>
                <span class="text-sm font-medium text-gray-700">{{ g.name }}</span>
              </div>
              <div class="text-right">
                <div class="text-lg font-bold text-gray-900">{{ g.avgOperations }} 次/日</div>
                <div class="text-xs text-gray-500">{{ g.count }} 个租户</div>
              </div>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div
                class="h-2 rounded-full"
                :style="{
                  width: Math.min((g.avgOperations / 600) * 100, 100) + '%',
                  backgroundColor: g.color
                }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import ChartBase from '../../components/ChartBase.vue'
import { OperationService } from '../../services/operation.service'
import type { DailyStatsParams, DailyStatsResponse, DailyStatsSummary } from '../../services/types'
type EChartsOption = any

type ViewMode = 'bar' | 'heatmap'

interface OptionItem { id: string; name: string }
interface DailyItem {
  id: string
  name: string
  serviceType: string
  serviceName: string
  size: 'small' | 'medium' | 'large' | 'enterprise'
  dailyOperations: number
  color: string
}

const tenantOptions = ref<OptionItem[]>([])
const tenantsLoading = ref(false)
const tenantsError = ref<string | null>(null)

const serviceTypeOptions: OptionItem[] = [
  { id: '', name: '全部服务' },
  { id: 'basic', name: '基础地图服务' },
  { id: 'advanced', name: '高级地图服务' },
  { id: 'enterprise', name: '企业级地图服务' },
  { id: 'custom', name: '定制化地图服务' }
]

const tenantSizeOptions: OptionItem[] = [
  { id: '', name: '全部规模' },
  { id: 'small', name: '小型企业 (<100人)' },
  { id: 'medium', name: '中型企业 (100-500人)' },
  { id: 'large', name: '大型企业 (500-1000人)' },
  { id: 'enterprise', name: ' enterprise (>1000人)' }
]

const selectedTenant = ref<string>('')
const selectedServiceType = ref<string>('')
const selectedSize = ref<string>('')
const viewMode = ref<ViewMode>('bar')
const chartLoading = ref(true)
const dailyStatsData = ref<DailyItem[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

// 统计数据汇总
const dailyStatsSummary = ref<DailyStatsSummary>({
  totalTenants: 0,
  averageDailyOperations: 0,
  maxDailyOperations: 0,
  totalOperations: 0
})
const summaryLoading = ref(false)
const summaryError = ref<string | null>(null)

const palette = ['#165DFF', '#36CFC9', '#722ED1', '#FF7D00']

/**
 * 加载租户信息
 */
const loadTenants = async () => {
  try {
    tenantsLoading.value = true
    tenantsError.value = null

    console.log('🏢 开始加载租户信息...')
    const tenants = await OperationService.getAllTenants()
    
    // 转换数据格式以适配组件，添加"全部租户"选项
    tenantOptions.value = [
      { id: '', name: '全部租户' },
      ...tenants.map(t => ({ id: t.tenantId, name: t.tenantName }))
    ]
    
    console.log('✅ 租户信息加载成功:', tenantOptions.value.length, '条')
  } catch (err: any) {
    console.error('❌ 加载租户信息失败:', err)
    tenantsError.value = err.message || '加载租户信息失败'
    // 如果加载失败，使用默认选项
    tenantOptions.value = [{ id: '', name: '全部租户' }]
  } finally {
    tenantsLoading.value = false
  }
}

/**
 * 加载日度统计数据
 */
const loadDailyStats = async () => {
  try {
    loading.value = true
    chartLoading.value = true
    error.value = null

    const params: DailyStatsParams = {}
    if (selectedTenant.value) params.selectedTenant = selectedTenant.value
    if (selectedServiceType.value) params.selectedServiceType = selectedServiceType.value
    if (selectedSize.value) params.selectedSize = selectedSize.value
    if (viewMode.value) params.viewMode = viewMode.value

    console.log('📊 开始加载日度统计数据:', params)
    const response: DailyStatsResponse = await OperationService.getDailyStats(params)
    
    // 转换数据格式以适配组件
    dailyStatsData.value = response.dailyStatsData || []
    
    console.log('✅ 日度统计数据加载成功:', dailyStatsData.value.length, '条')
  } catch (err: any) {
    console.error('❌ 加载日度统计数据失败:', err)
    error.value = err.message || '加载数据失败'
    dailyStatsData.value = []
  } finally {
    loading.value = false
    chartLoading.value = false
  }
}

/**
 * 加载日统计汇总数据
 * 获取四个关键统计数据：
 * 1. 租户总数：统计ms_auth_applications表的数据量
 * 2. 平均日操作次数：按天汇聚统计操作数，然后求平均值
 * 3. 最大日操作数：按天汇聚统计，找出最大值
 * 4. 操作总量：统计ms_operation_logs表当天的总数据量
 */
/**
 * 加载日统计汇总数据
 * 获取四个关键统计数据：
 * 1. 租户总数：统计ms_auth_applications表的数据量
 * 2. 平均日操作次数：按天汇聚统计操作数，然后求平均值
 * 3. 最大日操作数：按天汇聚统计，找出最大值
 * 4. 操作总量：统计ms_operation_logs表当天的总数据量
 * @param tenantName 租户名称，可选参数，为空时统计所有租户
 */
const loadDailyStatsSummary = async (tenantName?: string) => {
  try {
    summaryLoading.value = true
    summaryError.value = null

    // 如果没有传入租户名称，则根据选中的租户ID查找对应的租户名称
    let actualTenantName: string | undefined = tenantName;
    if (!actualTenantName && selectedTenant.value) {
      const selectedTenantOption = tenantOptions.value.find(t => t.id === selectedTenant.value);
      if (selectedTenantOption && selectedTenantOption.name !== '全部租户') {
        actualTenantName = selectedTenantOption.name;
      }
    }

    console.log('📊 开始加载日统计汇总数据...', actualTenantName ? `租户: ${actualTenantName}` : '所有租户');
    const response: DailyStatsSummary = await OperationService.getDailyStatsSummary(actualTenantName);
    
    // 更新统计数据
    dailyStatsSummary.value = response;
    
    console.log('✅ 日统计汇总数据加载成功:', response);
  } catch (err: any) {
    console.error('❌ 加载日统计汇总数据失败:', err);
    summaryError.value = err.message || '加载统计数据失败';
  } finally {
    summaryLoading.value = false;
  }
}

// 监听筛选条件变化，重新加载数据
watch([selectedTenant, selectedServiceType, selectedSize, viewMode], () => {
  loadDailyStats()
  loadDailyStatsSummary()
}, { deep: true })

// 组件挂载时加载数据
onMounted(async () => {
  await loadTenants()
  loadDailyStats()
  loadDailyStatsSummary()
})

// 图表：TOP15 横向柱状
const barOptionsTop = computed<EChartsOption>(() => {
  const top = dailyStatsData.value.slice(0, 15)
  return {
    grid: { top: 20, right: 30, bottom: 10, left: 150, containLabel: true },
    xAxis: {
      type: 'value',
      axisLabel: { color: '#6b7280' },
      splitLine: { lineStyle: { type: 'dashed', color: '#e5e7eb' } }
    },
    yAxis: {
      type: 'category',
      data: top.map(d => d.name),
      axisTick: { show: false },
      axisLine: { show: false },
      axisLabel: { color: '#374151' }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: (params: any) => {
        const p = Array.isArray(params) ? params[0] : params
        return `${p.name}<br/>日均操作次数：${p.value} 次`
      }
    },
    series: [
      {
        type: 'bar',
        data: top.map(d => ({ value: d.dailyOperations, itemStyle: { color: d.color } })),
        barMaxWidth: 20,
        itemStyle: { borderRadius: [0, 4, 4, 0] }
      }
    ]
  }
})

// 图表：全量纵向（热力视图风格）
const barOptionsAll = computed<EChartsOption>(() => {
  const data = dailyStatsData.value
  return {
    grid: { top: 20, right: 30, bottom: 80, left: 40, containLabel: true },
    xAxis: {
      type: 'category',
      data: data.map(d => d.name),
      axisTick: { show: false },
      axisLine: { lineStyle: { color: '#e5e7eb' } },
      axisLabel: { color: '#6b7280', rotate: -45 }
    },
    yAxis: {
      type: 'value',
      axisLabel: { color: '#6b7280' },
      splitLine: { lineStyle: { type: 'dashed', color: '#e5e7eb' } }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: (params: any[]) => {
        const p = params[0]
        return `${p.name}<br/>日均操作次数：${p.value} 次`
      }
    },
    series: [
      {
        type: 'bar',
        data: data.map(d => ({ value: d.dailyOperations, itemStyle: { color: d.color } })),
        barMaxWidth: 20,
        itemStyle: { borderRadius: [4, 4, 0, 0] }
      }
    ]
  }
})

const chartOptions = computed<EChartsOption>(() => (viewMode.value === 'bar' ? barOptionsTop.value : barOptionsAll.value))

// 分组统计：服务类型
const serviceTypeGroupStats = computed(() => {
  return serviceTypeOptions
    .filter(s => s.id)
    .map((service, idx) => {
      const list = dailyStatsData.value.filter(d => d.serviceType === service.id)
      const avg = list.length > 0 ? Math.floor(list.reduce((s, it) => s + it.dailyOperations, 0) / list.length) : 0
      return {
        name: service.name,
        count: list.length,
        avgOperations: avg,
        color: palette[idx % palette.length]
      }
    })
})

// 分组统计：租户规模
const sizeGroupStats = computed(() => {
  const sizeDefs = tenantSizeOptions.filter(s => s.id)
  return sizeDefs.map((size, idx) => {
    const list = dailyStatsData.value.filter(d => d.size === size.id)
    const avg = list.length > 0 ? Math.floor(list.reduce((s, it) => s + it.dailyOperations, 0) / list.length) : 0
    return {
      name: size.name,
      count: list.length,
      avgOperations: avg,
      color: palette[idx % palette.length]
    }
  })
})
</script>