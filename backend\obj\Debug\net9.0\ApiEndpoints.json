[{"ContainingType": "MapStudio.Api.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "MapStudio.Api.Models.DTOs.LoginRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.LoginResponse, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.AuthController", "Method": "Logout", "RelativePath": "api/Auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.AuthController", "Method": "GetProfile", "RelativePath": "api/Auth/profile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.UserProfileResponse, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.FilesController", "Method": "DeleteFile", "RelativePath": "api/Files/{fileId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.FilesController", "Method": "DownloadFile", "RelativePath": "api/Files/download/{fileId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MapStudio.Api.Controllers.FilesController", "Method": "GetFilePreview", "RelativePath": "api/Files/preview/{fileId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.FilePreviewResponse, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.FilesController", "Method": "UploadFile", "RelativePath": "api/Files/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "fileType", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.FileUploadResponse, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.MetadataController", "Method": "GetAuthPeriods", "RelativePath": "api/Metadata/auth-periods", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.AuthPeriodOption, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.MetadataController", "Method": "GetIndustries", "RelativePath": "api/Metadata/industries", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.IndustryOption, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.MetadataController", "Method": "GetPermissionScopes", "RelativePath": "api/Metadata/permission-scopes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.PermissionScopeOption, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.MetadataController", "Method": "GetServiceTypes", "RelativePath": "api/Metadata/service-types", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.MetadataController", "Method": "GetTenantSizes", "RelativePath": "api/Metadata/tenant-sizes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.TenantSizeOption, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.OperationController", "Method": "GetAccessStats", "RelativePath": "api/Operation/access-stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "timeRange", "Type": "System.String", "IsRequired": false}, {"Name": "chartType", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.AccessStatsResponse, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.OperationController", "Method": "GetActionTypeRank", "RelativePath": "api/Operation/action-type-rank", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "selectedOperationType", "Type": "System.String", "IsRequired": false}, {"Name": "tenantSize", "Type": "System.String", "IsRequired": false}, {"Name": "industry", "Type": "System.String", "IsRequired": false}, {"Name": "timeRange", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ActionTypeRankResponse, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.OperationController", "Method": "AddOperationLogs", "RelativePath": "api/Operation/addOperationlogs", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "operationLog", "Type": "MapStudio.Api.Models.Entities.OperationLog", "IsRequired": true}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.OperationController", "Method": "GetAlarmSettings", "RelativePath": "api/Operation/alarm-settings", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.AlarmSettingsResponse, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.OperationController", "Method": "UpdateAlarmTemplate", "RelativePath": "api/Operation/alarm-settings/template", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "emailTemplate", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.AlarmTemplateUpdateResponse, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.OperationController", "Method": "GetDailyStats", "RelativePath": "api/Operation/daily-stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "selectedServiceType", "Type": "System.String", "IsRequired": false}, {"Name": "selectedSize", "Type": "System.String", "IsRequired": false}, {"Name": "viewMode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.OperationController", "Method": "GetDailyStats", "RelativePath": "api/Operation/daily-stats/summary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenantName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.DTOs.DailyStatsDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.OperationController", "Method": "GetOperationLogs", "RelativePath": "api/Operation/logs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "tenantName", "Type": "System.String", "IsRequired": false}, {"Name": "actionType", "Type": "System.String", "IsRequired": false}, {"Name": "userName", "Type": "System.String", "IsRequired": false}, {"Name": "operationDate", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.OperationLogListResponse, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.OperationController", "Method": "GetOperationLogs", "RelativePath": "api/Operation/logs/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.OperationLogListResponse, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.OperationController", "Method": "GetMonthlyStats", "RelativePath": "api/Operation/monthly-stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "comparisonType", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.MonthlyStatsResponse, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.OperationController", "Method": "GetServiceStatusCodeStats", "RelativePath": "api/Operation/service-status-code-stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.OperationController", "Method": "GetServiceStatusStats", "RelativePath": "api/Operation/service-status-stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.OperationController", "Method": "GetSixMonthUsageStats", "RelativePath": "api/Operation/six-month-usage-stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.OperationController", "Method": "GetTenantActionRank", "RelativePath": "api/Operation/tenant-action-rank", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.TenantActionRankResponse, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.OperationController", "Method": "GetAllTenants", "RelativePath": "api/Operation/tenants", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.TenantDataDto, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.OperationController", "Method": "GetUserUsage", "RelativePath": "api/Operation/user-usage", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "timeRange", "Type": "System.String", "IsRequired": false}, {"Name": "timeGranularity", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.UserUsageResponse, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.SendMsgController", "Method": "GetSmsPhones", "RelativePath": "api/SendMsg/phones", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.SendMsgController", "Method": "AddSmsPhone", "RelativePath": "api/SendMsg/phones", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "MapStudio.Api.Models.DTOs.AddSmsPhoneSettingRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.SendMsgController", "Method": "UpdateSmsPhoneStatus", "RelativePath": "api/SendMsg/phones/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "MapStudio.Api.Models.DTOs.UpdateSmsPhoneSettingRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.SendMsgController", "Method": "DeleteSmsPhone", "RelativePath": "api/SendMsg/phones/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.SendMsgController", "Method": "SendAlertSms", "RelativePath": "api/SendMsg/send-alert-sms", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "MapStudio.Api.Models.DTOs.SendAlertSmsRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SendSmsResponseDto, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.SendMsgController", "Method": "SendTestSms", "RelativePath": "api/SendMsg/send-test-sms", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "MapStudio.Api.Models.DTOs.SendSmsRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SendSmsResponseDto, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.SendMsgController", "Method": "GetSmsTemplate", "RelativePath": "api/SendMsg/sms-template", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.SendMsgController", "Method": "SaveSmsTemplate", "RelativePath": "api/SendMsg/sms-template", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "MapStudio.Api.Models.DTOs.SaveSmsTemplateRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.SystemController", "Method": "Health", "RelativePath": "api/System/health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MapStudio.Api.Controllers.SystemController", "Method": "GetSystemInfo", "RelativePath": "api/System/info", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MapStudio.Api.Controllers.SystemController", "Method": "<PERSON>", "RelativePath": "api/System/ping", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MapStudio.Api.Controllers.TenantController", "Method": "SubmitAuthApplication", "RelativePath": "api/Tenant/auth-applications", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "MapStudio.Api.Models.DTOs.SubmitAuthApplicationRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.AuthApplicationResponse, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.TenantController", "Method": "GetAuthApplications", "RelativePath": "api/Tenant/auth-applications", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "serviceType", "Type": "System.String", "IsRequired": false}, {"Name": "authPeriod", "Type": "System.String", "IsRequired": false}, {"Name": "status", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.AuthApplicationListResponse, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.TenantController", "Method": "GetAuthApplicationDetail", "RelativePath": "api/Tenant/auth-applications/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.AuthApplicationDetailResponse, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.TenantController", "Method": "GetAuthApplicationProgress", "RelativePath": "api/Tenant/auth-progress", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "serviceType", "Type": "System.String", "IsRequired": false}, {"Name": "authPeriod", "Type": "System.String", "IsRequired": false}, {"Name": "status", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.TenantServiceProgressResponse, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.TenantController", "Method": "GetAuthApplicationProgress", "RelativePath": "api/Tenant/auth-progress/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.TenantServiceProgressDto, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MapStudio.Api.Controllers.TenantController", "Method": "GetServiceStatus", "RelativePath": "api/Tenant/service-status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenantId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.TenantServiceStatusResponse, MapStudio.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]