2025-09-11 09:23:52.687 +08:00 [INF] 开始初始化数据库...
2025-09-11 09:23:59.097 +08:00 [INF] 数据库表创建完成
2025-09-11 09:23:59.608 +08:00 [INF] 数据库初始化完成
2025-09-11 09:23:59.632 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-11 09:23:59.879 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-11 09:23:59.879 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-11 09:23:59.944 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-11 09:23:59.945 +08:00 [INF] Hosting environment: Development
2025-09-11 09:23:59.946 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-11 09:24:46.665 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/swagger/index.html - null null
2025-09-11 09:24:46.721 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/swagger/index.html - null null
2025-09-11 09:24:46.896 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/swagger/index.html - 200 null text/html;charset=utf-8 174.4806ms
2025-09-11 09:24:46.907 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/swagger/index.js - null null
2025-09-11 09:24:46.896 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/swagger/index.html - 200 null text/html;charset=utf-8 233.6004ms
2025-09-11 09:24:46.901 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/_framework/aspnetcore-browser-refresh.js - null null
2025-09-11 09:24:46.946 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/_vs/browserLink - null null
2025-09-11 09:24:46.955 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/swagger/index.js - 200 null application/javascript;charset=utf-8 47.7455ms
2025-09-11 09:24:46.968 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/_framework/aspnetcore-browser-refresh.js - 200 16511 application/javascript; charset=utf-8 66.3184ms
2025-09-11 09:24:46.994 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/_vs/browserLink - 200 null text/javascript; charset=UTF-8 47.5589ms
2025-09-11 09:24:47.041 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/swagger/v1/swagger.json - null null
2025-09-11 09:24:47.109 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 67.8962ms
2025-09-11 09:25:19.635 +08:00 [INF] Request starting HTTP/2 POST https://localhost:7007/api/SendMsg/send-sms - application/json 166
2025-09-11 09:25:19.652 +08:00 [INF] CORS policy execution successful.
2025-09-11 09:25:19.661 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.SendSms (MapStudio.Api)'
2025-09-11 09:25:19.691 +08:00 [INF] Route matched with {action = "SendSms", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SendSmsResponseDto]]] SendSms(MapStudio.Api.Models.DTOs.SendSmsRequestDto) on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 09:25:22.498 +08:00 [INF] 开始发送短信: Type=1, Modular=1, Function=1
2025-09-11 09:29:47.207 +08:00 [INF] 开始初始化数据库...
2025-09-11 09:29:53.250 +08:00 [INF] 数据库表创建完成
2025-09-11 09:29:53.726 +08:00 [INF] 数据库初始化完成
2025-09-11 09:29:53.758 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-11 09:29:54.006 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-11 09:29:54.006 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-11 09:29:54.089 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-11 09:29:54.090 +08:00 [INF] Hosting environment: Development
2025-09-11 09:29:54.092 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-11 09:30:20.167 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 09:30:20.167 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 09:30:20.244 +08:00 [INF] CORS policy execution successful.
2025-09-11 09:30:20.244 +08:00 [INF] CORS policy execution successful.
2025-09-11 09:30:20.249 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 84.1781ms
2025-09-11 09:30:20.249 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 84.176ms
2025-09-11 09:30:20.254 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 09:30:20.254 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 09:30:20.269 +08:00 [INF] CORS policy execution successful.
2025-09-11 09:30:20.269 +08:00 [INF] CORS policy execution successful.
2025-09-11 09:30:20.274 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 09:30:20.274 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 09:30:20.291 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 09:30:20.291 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 09:30:20.571 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 09:30:20.588 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 293.3163ms
2025-09-11 09:30:20.591 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 09:30:20.684 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 09:30:20.691 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 396.7549ms
2025-09-11 09:30:20.692 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 09:30:20.783 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 528.7155ms
2025-09-11 09:30:20.820 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 566.1355ms
2025-09-11 09:30:33.936 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 09:30:33.936 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 09:30:33.945 +08:00 [INF] CORS policy execution successful.
2025-09-11 09:30:33.953 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 17.7651ms
2025-09-11 09:30:33.950 +08:00 [INF] CORS policy execution successful.
2025-09-11 09:30:33.956 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 09:30:33.962 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 26.5962ms
2025-09-11 09:30:33.968 +08:00 [INF] CORS policy execution successful.
2025-09-11 09:30:33.969 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 09:30:33.979 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 09:30:33.987 +08:00 [INF] CORS policy execution successful.
2025-09-11 09:30:33.989 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 09:30:33.989 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 09:30:33.997 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 09:30:34.141 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 09:30:34.142 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 09:30:34.150 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 147.3801ms
2025-09-11 09:30:34.161 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 165.48ms
2025-09-11 09:30:34.170 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 09:30:34.171 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 09:30:34.330 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 373.4274ms
2025-09-11 09:30:34.355 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 385.374ms
2025-09-11 09:38:48.488 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/logs?page=1&pageSize=20 - null null
2025-09-11 09:38:48.516 +08:00 [INF] CORS policy execution successful.
2025-09-11 09:38:48.517 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/logs?page=1&pageSize=20 - 204 null null 29.3914ms
2025-09-11 09:38:48.520 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/logs?page=1&pageSize=20 - null null
2025-09-11 09:38:48.532 +08:00 [INF] CORS policy execution successful.
2025-09-11 09:38:48.535 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetOperationLogs (MapStudio.Api)'
2025-09-11 09:38:48.562 +08:00 [INF] Route matched with {action = "GetOperationLogs", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.OperationLogListResponse]]] GetOperationLogs(Int32, Int32, System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-11 09:38:49.072 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.OperationLogListResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 09:38:49.083 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetOperationLogs (MapStudio.Api) in 515.9795ms
2025-09-11 09:38:49.085 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetOperationLogs (MapStudio.Api)'
2025-09-11 09:38:49.215 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/logs?page=1&pageSize=20 - 200 null application/json; charset=utf-8 695.4861ms
2025-09-11 09:39:34.411 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/logs/92c50c77-c9a0-4938-863b-4f0b6b3147ae - null null
2025-09-11 09:39:34.428 +08:00 [INF] CORS policy execution successful.
2025-09-11 09:39:34.429 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/logs/92c50c77-c9a0-4938-863b-4f0b6b3147ae - 204 null null 17.9054ms
2025-09-11 09:39:34.431 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/logs/92c50c77-c9a0-4938-863b-4f0b6b3147ae - null null
2025-09-11 09:39:34.434 +08:00 [INF] CORS policy execution successful.
2025-09-11 09:39:34.602 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/logs/92c50c77-c9a0-4938-863b-4f0b6b3147ae - 404 0 null 170.9992ms
2025-09-11 09:39:34.606 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7007/api/operation/logs/92c50c77-c9a0-4938-863b-4f0b6b3147ae, Response status code: 404
2025-09-11 09:39:37.370 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/logs/570cf3c2-c6a6-4cce-bd48-ed83aaaff734 - null null
2025-09-11 09:39:37.373 +08:00 [INF] CORS policy execution successful.
2025-09-11 09:39:37.375 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/logs/570cf3c2-c6a6-4cce-bd48-ed83aaaff734 - 204 null null 4.1563ms
2025-09-11 09:39:37.376 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/logs/570cf3c2-c6a6-4cce-bd48-ed83aaaff734 - null null
2025-09-11 09:39:37.380 +08:00 [INF] CORS policy execution successful.
2025-09-11 09:39:37.484 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/logs/570cf3c2-c6a6-4cce-bd48-ed83aaaff734 - 404 0 null 108.2777ms
2025-09-11 09:39:37.488 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7007/api/operation/logs/570cf3c2-c6a6-4cce-bd48-ed83aaaff734, Response status code: 404
2025-09-11 09:39:40.002 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/logs/92c50c77-c9a0-4938-863b-4f0b6b3147ae - null null
2025-09-11 09:39:40.007 +08:00 [INF] CORS policy execution successful.
2025-09-11 09:39:40.010 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/logs/92c50c77-c9a0-4938-863b-4f0b6b3147ae - 204 null null 8.0192ms
2025-09-11 09:39:40.012 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/logs/92c50c77-c9a0-4938-863b-4f0b6b3147ae - null null
2025-09-11 09:39:40.019 +08:00 [INF] CORS policy execution successful.
2025-09-11 09:39:40.143 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/logs/92c50c77-c9a0-4938-863b-4f0b6b3147ae - 404 0 null 131.4819ms
2025-09-11 09:39:40.147 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7007/api/operation/logs/92c50c77-c9a0-4938-863b-4f0b6b3147ae, Response status code: 404
2025-09-11 09:47:22.482 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 09:47:22.487 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 09:47:22.491 +08:00 [INF] CORS policy execution successful.
2025-09-11 09:47:22.494 +08:00 [INF] CORS policy execution successful.
2025-09-11 09:47:22.495 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 13.4604ms
2025-09-11 09:47:22.504 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 18.2118ms
2025-09-11 09:47:22.504 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 09:47:22.529 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 09:47:22.534 +08:00 [INF] CORS policy execution successful.
2025-09-11 09:47:22.538 +08:00 [INF] CORS policy execution successful.
2025-09-11 09:47:22.550 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 09:47:22.551 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 09:47:22.552 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 09:47:22.554 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 09:47:22.986 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 09:47:22.988 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 431.1718ms
2025-09-11 09:47:22.989 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 09:47:23.005 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 09:47:23.008 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 438.4876ms
2025-09-11 09:47:23.009 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 09:47:23.120 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 615.9249ms
2025-09-11 09:47:23.144 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 615.0812ms
2025-09-11 10:09:47.310 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones/2 - null null
2025-09-11 10:09:47.316 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:09:47.319 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones/2 - 204 null null 12.0237ms
2025-09-11 10:09:47.322 +08:00 [INF] Request starting HTTP/2 PUT https://localhost:7007/api/SendMsg/phones/2 - application/json 106
2025-09-11 10:09:47.327 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:09:47.328 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.UpdateSmsPhoneStatus (MapStudio.Api)'
2025-09-11 10:09:47.342 +08:00 [INF] Route matched with {action = "UpdateSmsPhoneStatus", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]] UpdateSmsPhoneStatus(Int32, MapStudio.Api.Models.DTOs.UpdateSmsPhoneSettingRequest) on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 10:09:48.025 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 10:09:48.028 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.UpdateSmsPhoneStatus (MapStudio.Api) in 683.5875ms
2025-09-11 10:09:48.031 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.UpdateSmsPhoneStatus (MapStudio.Api)'
2025-09-11 10:09:48.143 +08:00 [INF] Request finished HTTP/2 PUT https://localhost:7007/api/SendMsg/phones/2 - 200 null application/json; charset=utf-8 820.6654ms
2025-09-11 10:09:51.931 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 10:09:51.931 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 10:09:51.933 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:09:51.935 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:09:51.936 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 5.5772ms
2025-09-11 10:09:51.942 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 11.6739ms
2025-09-11 10:09:51.942 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 10:09:51.948 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 10:09:51.972 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:09:51.973 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:09:51.974 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 10:09:51.975 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 10:09:51.980 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 10:09:52.002 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 10:09:52.143 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 10:09:52.146 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 139.2107ms
2025-09-11 10:09:52.147 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 10:09:52.323 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 381.0957ms
2025-09-11 10:09:52.372 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 10:09:52.373 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 359.0247ms
2025-09-11 10:09:52.374 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 10:09:52.480 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 531.742ms
2025-09-11 10:10:38.863 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-11 10:10:38.866 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:10:38.867 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 4.1982ms
2025-09-11 10:10:38.868 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-11 10:10:38.871 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:10:38.871 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-11 10:10:38.878 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-11 10:10:41.864 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 10:10:41.872 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 2985.6552ms
2025-09-11 10:10:41.875 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-11 10:10:42.002 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 3133.7961ms
2025-09-11 10:11:12.214 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=heatmap - null null
2025-09-11 10:11:12.217 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:11:12.218 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=heatmap - 204 null null 3.7623ms
2025-09-11 10:11:12.220 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=heatmap - null null
2025-09-11 10:11:12.228 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:11:12.229 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-11 10:11:12.230 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-11 10:11:12.317 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 10:11:12.320 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 88.3479ms
2025-09-11 10:11:12.321 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-11 10:11:12.445 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=heatmap - 200 null application/json; charset=utf-8 224.7744ms
2025-09-11 10:11:13.334 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-11 10:11:13.345 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:11:13.358 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 24.2772ms
2025-09-11 10:11:13.361 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-11 10:11:13.372 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:11:13.373 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-11 10:11:13.380 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-11 10:11:13.483 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 10:11:13.485 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 92.2ms
2025-09-11 10:11:13.486 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-11 10:11:13.655 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 293.6356ms
2025-09-11 10:12:14.815 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/logs?page=1&pageSize=20 - null null
2025-09-11 10:12:14.825 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:12:14.826 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/logs?page=1&pageSize=20 - 204 null null 10.7601ms
2025-09-11 10:12:14.828 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/logs?page=1&pageSize=20 - null null
2025-09-11 10:12:14.831 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:12:14.832 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetOperationLogs (MapStudio.Api)'
2025-09-11 10:12:14.835 +08:00 [INF] Route matched with {action = "GetOperationLogs", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.OperationLogListResponse]]] GetOperationLogs(Int32, Int32, System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-11 10:12:15.144 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.OperationLogListResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 10:12:15.146 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetOperationLogs (MapStudio.Api) in 307.96ms
2025-09-11 10:12:15.147 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetOperationLogs (MapStudio.Api)'
2025-09-11 10:12:15.293 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/logs?page=1&pageSize=20 - 200 null application/json; charset=utf-8 465.7931ms
2025-09-11 10:21:19.490 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - null null
2025-09-11 10:21:19.493 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:21:19.494 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - 204 null null 3.4429ms
2025-09-11 10:21:19.504 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - null null
2025-09-11 10:21:19.509 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:21:19.511 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api)'
2025-09-11 10:21:19.515 +08:00 [INF] Route matched with {action = "GetAuthApplications", controller = "Tenant"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.AuthApplicationListResponse]]] GetAuthApplications(Int32, Int32, System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.TenantController (MapStudio.Api).
2025-09-11 10:21:20.142 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.AuthApplicationListResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 10:21:20.147 +08:00 [INF] Executed action MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api) in 630.8976ms
2025-09-11 10:21:20.149 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api)'
2025-09-11 10:21:20.277 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - 200 null application/json; charset=utf-8 772.8208ms
2025-09-11 10:25:00.757 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - null null
2025-09-11 10:25:00.771 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:25:00.772 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - 204 null null 14.2417ms
2025-09-11 10:25:00.773 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - null null
2025-09-11 10:25:00.777 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:25:00.778 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api)'
2025-09-11 10:25:00.781 +08:00 [INF] Route matched with {action = "GetAuthApplications", controller = "Tenant"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.AuthApplicationListResponse]]] GetAuthApplications(Int32, Int32, System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.TenantController (MapStudio.Api).
2025-09-11 10:25:01.027 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.AuthApplicationListResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 10:25:01.029 +08:00 [INF] Executed action MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api) in 238.3268ms
2025-09-11 10:25:01.031 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api)'
2025-09-11 10:25:01.154 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - 200 null application/json; charset=utf-8 380.6199ms
2025-09-11 10:25:02.196 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - null null
2025-09-11 10:25:02.198 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:25:02.199 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api)'
2025-09-11 10:25:02.202 +08:00 [INF] Route matched with {action = "GetAuthApplications", controller = "Tenant"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.AuthApplicationListResponse]]] GetAuthApplications(Int32, Int32, System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.TenantController (MapStudio.Api).
2025-09-11 10:25:02.463 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.AuthApplicationListResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 10:25:02.465 +08:00 [INF] Executed action MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api) in 260.7078ms
2025-09-11 10:25:02.466 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api)'
2025-09-11 10:25:02.677 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - 200 null application/json; charset=utf-8 481.5486ms
2025-09-11 10:25:02.737 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-progress?page=1&pageSize=10 - null null
2025-09-11 10:25:02.741 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:25:02.742 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-progress?page=1&pageSize=10 - 204 null null 5.4322ms
2025-09-11 10:25:02.744 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Tenant/auth-progress?page=1&pageSize=10 - null null
2025-09-11 10:25:02.752 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:25:02.753 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplicationProgress (MapStudio.Api)'
2025-09-11 10:25:02.761 +08:00 [INF] Route matched with {action = "GetAuthApplicationProgress", controller = "Tenant"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.TenantServiceProgressResponse]]] GetAuthApplicationProgress(Int32, Int32, System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.TenantController (MapStudio.Api).
2025-09-11 10:25:05.955 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.TenantServiceProgressResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 10:25:05.967 +08:00 [INF] Executed action MapStudio.Api.Controllers.TenantController.GetAuthApplicationProgress (MapStudio.Api) in 3204.3116ms
2025-09-11 10:25:05.969 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplicationProgress (MapStudio.Api)'
2025-09-11 10:25:06.118 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Tenant/auth-progress?page=1&pageSize=10 - 200 null application/json; charset=utf-8 3373.3583ms
2025-09-11 10:25:10.704 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/logs?page=1&pageSize=20 - null null
2025-09-11 10:25:10.706 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:25:10.708 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/logs?page=1&pageSize=20 - 204 null null 3.5469ms
2025-09-11 10:25:10.709 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/logs?page=1&pageSize=20 - null null
2025-09-11 10:25:10.715 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:25:10.716 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetOperationLogs (MapStudio.Api)'
2025-09-11 10:25:10.717 +08:00 [INF] Route matched with {action = "GetOperationLogs", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.OperationLogListResponse]]] GetOperationLogs(Int32, Int32, System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-11 10:25:10.972 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.OperationLogListResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 10:25:10.975 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetOperationLogs (MapStudio.Api) in 255.8501ms
2025-09-11 10:25:10.977 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetOperationLogs (MapStudio.Api)'
2025-09-11 10:25:11.129 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/logs?page=1&pageSize=20 - 200 null application/json; charset=utf-8 420.0289ms
2025-09-11 10:30:13.986 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - null null
2025-09-11 10:30:13.991 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:30:13.992 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - 204 null null 5.5333ms
2025-09-11 10:30:13.999 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - null null
2025-09-11 10:30:14.003 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:30:14.003 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api)'
2025-09-11 10:30:14.004 +08:00 [INF] Route matched with {action = "GetAuthApplications", controller = "Tenant"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.AuthApplicationListResponse]]] GetAuthApplications(Int32, Int32, System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.TenantController (MapStudio.Api).
2025-09-11 10:30:14.428 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.AuthApplicationListResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 10:30:14.430 +08:00 [INF] Executed action MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api) in 423.8101ms
2025-09-11 10:30:14.432 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api)'
2025-09-11 10:30:14.592 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - 200 null application/json; charset=utf-8 593.1166ms
2025-09-11 10:30:20.835 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - null null
2025-09-11 10:30:20.837 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:30:20.838 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - 204 null null 3.1269ms
2025-09-11 10:30:20.839 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - null null
2025-09-11 10:30:20.845 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:30:20.846 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api)'
2025-09-11 10:30:20.848 +08:00 [INF] Route matched with {action = "GetAuthApplications", controller = "Tenant"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.AuthApplicationListResponse]]] GetAuthApplications(Int32, Int32, System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.TenantController (MapStudio.Api).
2025-09-11 10:30:21.076 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.AuthApplicationListResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 10:30:21.079 +08:00 [INF] Executed action MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api) in 228.6012ms
2025-09-11 10:30:21.081 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api)'
2025-09-11 10:30:21.236 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - 200 null application/json; charset=utf-8 396.3475ms
2025-09-11 10:30:24.404 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - null null
2025-09-11 10:30:24.407 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:30:24.408 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api)'
2025-09-11 10:30:24.409 +08:00 [INF] Route matched with {action = "GetAuthApplications", controller = "Tenant"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.AuthApplicationListResponse]]] GetAuthApplications(Int32, Int32, System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.TenantController (MapStudio.Api).
2025-09-11 10:30:24.715 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.AuthApplicationListResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 10:30:24.717 +08:00 [INF] Executed action MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api) in 304.8924ms
2025-09-11 10:30:24.718 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api)'
2025-09-11 10:30:24.842 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - 200 null application/json; charset=utf-8 437.4869ms
2025-09-11 10:30:25.166 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-progress?page=1&pageSize=10 - null null
2025-09-11 10:30:25.170 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:30:25.171 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-progress?page=1&pageSize=10 - 204 null null 4.9234ms
2025-09-11 10:30:25.172 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Tenant/auth-progress?page=1&pageSize=10 - null null
2025-09-11 10:30:25.179 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:30:25.180 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplicationProgress (MapStudio.Api)'
2025-09-11 10:30:25.182 +08:00 [INF] Route matched with {action = "GetAuthApplicationProgress", controller = "Tenant"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.TenantServiceProgressResponse]]] GetAuthApplicationProgress(Int32, Int32, System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.TenantController (MapStudio.Api).
2025-09-11 10:30:28.921 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.TenantServiceProgressResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 10:30:28.922 +08:00 [INF] Executed action MapStudio.Api.Controllers.TenantController.GetAuthApplicationProgress (MapStudio.Api) in 3737.2852ms
2025-09-11 10:30:28.923 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplicationProgress (MapStudio.Api)'
2025-09-11 10:30:29.054 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Tenant/auth-progress?page=1&pageSize=10 - 200 null application/json; charset=utf-8 3881.3704ms
2025-09-11 10:34:19.315 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 10:34:19.318 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:34:19.320 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 5.8682ms
2025-09-11 10:34:19.326 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 10:34:19.330 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:34:19.331 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 10:34:19.335 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 10:34:19.415 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 10:34:19.420 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 83.4493ms
2025-09-11 10:34:19.422 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 10:34:19.560 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 234.2089ms
2025-09-11 10:34:28.700 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - null null
2025-09-11 10:34:28.702 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:34:28.704 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - 204 null null 4.0256ms
2025-09-11 10:34:28.706 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - null null
2025-09-11 10:34:28.711 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:34:28.712 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api)'
2025-09-11 10:34:28.713 +08:00 [INF] Route matched with {action = "GetAuthApplications", controller = "Tenant"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.AuthApplicationListResponse]]] GetAuthApplications(Int32, Int32, System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.TenantController (MapStudio.Api).
2025-09-11 10:34:28.939 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.AuthApplicationListResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 10:34:28.942 +08:00 [INF] Executed action MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api) in 227.2171ms
2025-09-11 10:34:28.943 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api)'
2025-09-11 10:34:29.083 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - 200 null application/json; charset=utf-8 377.0636ms
2025-09-11 10:34:46.337 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-progress?page=1&pageSize=10 - null null
2025-09-11 10:34:46.345 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:34:46.347 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-progress?page=1&pageSize=10 - 204 null null 9.9284ms
2025-09-11 10:34:46.349 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Tenant/auth-progress?page=1&pageSize=10 - null null
2025-09-11 10:34:46.354 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:34:46.355 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplicationProgress (MapStudio.Api)'
2025-09-11 10:34:46.356 +08:00 [INF] Route matched with {action = "GetAuthApplicationProgress", controller = "Tenant"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.TenantServiceProgressResponse]]] GetAuthApplicationProgress(Int32, Int32, System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.TenantController (MapStudio.Api).
2025-09-11 10:34:46.588 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.TenantServiceProgressResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 10:34:46.590 +08:00 [INF] Executed action MapStudio.Api.Controllers.TenantController.GetAuthApplicationProgress (MapStudio.Api) in 230.9496ms
2025-09-11 10:34:46.591 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplicationProgress (MapStudio.Api)'
2025-09-11 10:34:46.707 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Tenant/auth-progress?page=1&pageSize=10 - 200 null application/json; charset=utf-8 357.8276ms
2025-09-11 10:36:45.286 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 10:36:45.296 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:36:45.296 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 10.7409ms
2025-09-11 10:36:45.298 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 10:36:45.301 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:36:45.302 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 10:36:45.303 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 10:36:45.524 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 10:36:45.528 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 220.723ms
2025-09-11 10:36:45.529 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 10:36:45.658 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 360.4566ms
2025-09-11 10:41:32.826 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - null null
2025-09-11 10:41:32.832 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:41:32.832 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - 204 null null 6.9107ms
2025-09-11 10:41:32.839 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - null null
2025-09-11 10:41:32.844 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:41:32.845 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api)'
2025-09-11 10:41:32.846 +08:00 [INF] Route matched with {action = "GetAuthApplications", controller = "Tenant"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.AuthApplicationListResponse]]] GetAuthApplications(Int32, Int32, System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.TenantController (MapStudio.Api).
2025-09-11 10:41:33.115 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.AuthApplicationListResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 10:41:33.117 +08:00 [INF] Executed action MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api) in 268.9565ms
2025-09-11 10:41:33.118 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api)'
2025-09-11 10:41:33.255 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - 200 null application/json; charset=utf-8 415.8385ms
2025-09-11 10:55:55.794 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 10:55:55.796 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:55:55.797 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 3.1383ms
2025-09-11 10:55:55.798 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 10:55:55.801 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:55:55.802 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 10:55:55.803 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 10:55:56.062 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 10:55:56.063 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 258.9532ms
2025-09-11 10:55:56.064 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 10:55:56.199 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 400.7796ms
2025-09-11 10:55:57.703 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/logs?page=1&pageSize=20 - null null
2025-09-11 10:55:57.706 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:55:57.707 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/logs?page=1&pageSize=20 - 204 null null 3.5436ms
2025-09-11 10:55:57.709 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/logs?page=1&pageSize=20 - null null
2025-09-11 10:55:57.712 +08:00 [INF] CORS policy execution successful.
2025-09-11 10:55:57.712 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetOperationLogs (MapStudio.Api)'
2025-09-11 10:55:57.713 +08:00 [INF] Route matched with {action = "GetOperationLogs", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.OperationLogListResponse]]] GetOperationLogs(Int32, Int32, System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-11 10:55:58.021 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.OperationLogListResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 10:55:58.023 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetOperationLogs (MapStudio.Api) in 307.7147ms
2025-09-11 10:55:58.024 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetOperationLogs (MapStudio.Api)'
2025-09-11 10:55:58.144 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/logs?page=1&pageSize=20 - 200 null application/json; charset=utf-8 435.2166ms
2025-09-11 11:13:19.634 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/logs?page=1&pageSize=20 - null null
2025-09-11 11:13:19.640 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:13:19.640 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/logs?page=1&pageSize=20 - 204 null null 5.9284ms
2025-09-11 11:13:19.641 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/logs?page=1&pageSize=20 - null null
2025-09-11 11:13:19.643 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:13:19.644 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetOperationLogs (MapStudio.Api)'
2025-09-11 11:13:19.645 +08:00 [INF] Route matched with {action = "GetOperationLogs", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.OperationLogListResponse]]] GetOperationLogs(Int32, Int32, System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-11 11:13:20.163 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.OperationLogListResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 11:13:20.165 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetOperationLogs (MapStudio.Api) in 519.3085ms
2025-09-11 11:13:20.166 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetOperationLogs (MapStudio.Api)'
2025-09-11 11:13:20.413 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/logs?page=1&pageSize=20 - 200 null application/json; charset=utf-8 771.4602ms
2025-09-11 11:13:30.128 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 11:13:30.128 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 11:13:30.130 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:13:30.133 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:13:30.133 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 4.9075ms
2025-09-11 11:13:30.139 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 10.4913ms
2025-09-11 11:13:30.139 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 11:13:30.144 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 11:13:30.150 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:13:30.151 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:13:30.152 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 11:13:30.152 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 11:13:30.153 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 11:13:30.153 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 11:13:30.295 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 11:13:30.296 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 141.6862ms
2025-09-11 11:13:30.298 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 11:13:30.468 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 328.8855ms
2025-09-11 11:13:30.572 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 11:13:30.574 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 417.9161ms
2025-09-11 11:13:30.575 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 11:13:30.717 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 572.5489ms
2025-09-11 11:18:53.966 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 11:18:53.967 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:18:53.968 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 2.2885ms
2025-09-11 11:18:53.973 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 11:18:53.978 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:18:53.978 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 11:18:53.980 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 11:18:54.399 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 11:18:54.401 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 419.5899ms
2025-09-11 11:18:54.402 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 11:18:54.528 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 554.5749ms
2025-09-11 11:18:55.199 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 11:18:55.199 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 11:18:55.205 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:18:55.206 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:18:55.208 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 8.4792ms
2025-09-11 11:18:55.207 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 7.6858ms
2025-09-11 11:18:55.210 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 11:18:55.210 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 11:18:55.218 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:18:55.219 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:18:55.219 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 11:18:55.220 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 11:18:55.220 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 11:18:55.221 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 11:18:55.386 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 11:18:55.388 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 165.9502ms
2025-09-11 11:18:55.389 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 11:18:55.516 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 305.5196ms
2025-09-11 11:18:55.573 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 11:18:55.575 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 351.0661ms
2025-09-11 11:18:55.576 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 11:18:55.714 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 504.4513ms
2025-09-11 11:19:26.760 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 11:19:26.761 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 11:19:26.776 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:19:26.778 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:19:26.778 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 18.9848ms
2025-09-11 11:19:26.779 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 18.4822ms
2025-09-11 11:19:26.780 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 11:19:26.780 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 11:19:26.788 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:19:26.789 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:19:26.789 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 11:19:26.790 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 11:19:26.790 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 11:19:26.791 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 11:19:26.932 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 11:19:26.932 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 11:19:26.934 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 141.4485ms
2025-09-11 11:19:26.936 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 141.9649ms
2025-09-11 11:19:26.938 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 11:19:26.939 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 11:19:27.104 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 323.4476ms
2025-09-11 11:19:27.136 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 355.8765ms
2025-09-11 11:20:16.039 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 11:20:16.043 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 11:20:16.047 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:20:16.049 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:20:16.049 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 10.2661ms
2025-09-11 11:20:16.051 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 6.8271ms
2025-09-11 11:20:16.056 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 11:20:16.056 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 11:20:16.066 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:20:16.067 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:20:16.068 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 11:20:16.069 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 11:20:16.070 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 11:20:16.071 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 11:20:16.233 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 11:20:16.235 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 161.8673ms
2025-09-11 11:20:16.236 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 11:20:16.237 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 11:20:16.238 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 161.8407ms
2025-09-11 11:20:16.239 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 11:20:16.449 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 393.114ms
2025-09-11 11:20:16.449 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 393.298ms
2025-09-11 11:20:27.273 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/send-sms - null null
2025-09-11 11:20:27.274 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:20:27.275 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/send-sms - 204 null null 2.278ms
2025-09-11 11:20:27.276 +08:00 [INF] Request starting HTTP/2 POST https://localhost:7007/api/SendMsg/send-sms - application/json 135
2025-09-11 11:20:27.279 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:20:27.279 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.SendSms (MapStudio.Api)'
2025-09-11 11:20:27.287 +08:00 [INF] Route matched with {action = "SendSms", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SendSmsResponseDto]]] SendSms(MapStudio.Api.Models.DTOs.SendSmsRequestDto) on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 11:20:27.441 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-09-11 11:20:27.462 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.SendSms (MapStudio.Api) in 172.6641ms
2025-09-11 11:20:27.462 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.SendSms (MapStudio.Api)'
2025-09-11 11:20:27.891 +08:00 [INF] Request finished HTTP/2 POST https://localhost:7007/api/SendMsg/send-sms - 400 null application/problem+json; charset=utf-8 615.0644ms
2025-09-11 11:25:57.377 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/send-sms - null null
2025-09-11 11:25:57.386 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:25:57.387 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/send-sms - 204 null null 10.0733ms
2025-09-11 11:25:57.389 +08:00 [INF] Request starting HTTP/2 POST https://localhost:7007/api/SendMsg/send-sms - application/json 135
2025-09-11 11:25:57.392 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:25:57.393 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.SendSms (MapStudio.Api)'
2025-09-11 11:25:57.393 +08:00 [INF] Route matched with {action = "SendSms", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SendSmsResponseDto]]] SendSms(MapStudio.Api.Models.DTOs.SendSmsRequestDto) on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 11:25:57.674 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-09-11 11:25:57.675 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.SendSms (MapStudio.Api) in 280.1127ms
2025-09-11 11:25:57.676 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.SendSms (MapStudio.Api)'
2025-09-11 11:25:57.784 +08:00 [INF] Request finished HTTP/2 POST https://localhost:7007/api/SendMsg/send-sms - 400 null application/problem+json; charset=utf-8 395.3351ms
2025-09-11 11:43:47.709 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 11:43:47.710 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 11:43:47.714 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:43:47.716 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:43:47.717 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 9.4233ms
2025-09-11 11:43:47.720 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 10.8729ms
2025-09-11 11:43:47.723 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 11:43:47.724 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 11:43:47.732 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:43:47.733 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:43:47.734 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 11:43:47.734 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 11:43:47.735 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 11:43:47.735 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 11:43:48.161 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 11:43:48.161 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 11:43:48.162 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 424.6572ms
2025-09-11 11:43:48.164 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 427.187ms
2025-09-11 11:43:48.165 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 11:43:48.166 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 11:43:48.303 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 579.09ms
2025-09-11 11:43:48.327 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 603.6009ms
2025-09-11 11:44:48.571 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 11:44:48.571 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 11:44:48.585 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:44:48.586 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:44:48.587 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 15.5267ms
2025-09-11 11:44:48.587 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 16.0554ms
2025-09-11 11:44:48.589 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 11:44:48.589 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 11:44:48.592 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:44:48.593 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:44:48.594 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 11:44:48.594 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 11:44:48.595 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 11:44:48.595 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 11:44:48.754 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 11:44:48.755 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 11:44:48.755 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 157.0908ms
2025-09-11 11:44:48.756 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 159.3983ms
2025-09-11 11:44:48.758 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 11:44:48.757 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 11:44:48.915 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 326.4909ms
2025-09-11 11:44:48.944 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 355.5494ms
2025-09-11 11:47:49.017 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 11:47:49.017 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 11:47:49.039 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:47:49.041 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:47:49.042 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 24.5126ms
2025-09-11 11:47:49.044 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 26.0751ms
2025-09-11 11:47:49.046 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 11:47:49.049 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 11:47:49.059 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:47:49.062 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:47:49.063 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 11:47:49.063 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 11:47:49.064 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 11:47:49.065 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 11:47:49.201 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 11:47:49.203 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 11:47:49.203 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 133.8984ms
2025-09-11 11:47:49.205 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 137.8251ms
2025-09-11 11:47:49.206 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 11:47:49.207 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 11:47:49.354 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 304.6783ms
2025-09-11 11:47:49.372 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 326.8423ms
2025-09-11 11:47:51.636 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones/2 - null null
2025-09-11 11:47:51.638 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:47:51.639 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones/2 - 204 null null 3.0011ms
2025-09-11 11:47:51.640 +08:00 [INF] Request starting HTTP/2 PUT https://localhost:7007/api/SendMsg/phones/2 - application/json 107
2025-09-11 11:47:51.643 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:47:51.644 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.UpdateSmsPhoneStatus (MapStudio.Api)'
2025-09-11 11:47:51.646 +08:00 [INF] Route matched with {action = "UpdateSmsPhoneStatus", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]] UpdateSmsPhoneStatus(Int32, MapStudio.Api.Models.DTOs.UpdateSmsPhoneSettingRequest) on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 11:47:51.941 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 11:47:51.942 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.UpdateSmsPhoneStatus (MapStudio.Api) in 295.2501ms
2025-09-11 11:47:51.944 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.UpdateSmsPhoneStatus (MapStudio.Api)'
2025-09-11 11:47:52.071 +08:00 [INF] Request finished HTTP/2 PUT https://localhost:7007/api/SendMsg/phones/2 - 200 null application/json; charset=utf-8 430.6936ms
2025-09-11 11:48:00.475 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 11:48:00.475 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 11:48:00.478 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:48:00.480 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:48:00.481 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 5.7314ms
2025-09-11 11:48:00.482 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 6.6706ms
2025-09-11 11:48:00.483 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 11:48:00.483 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 11:48:00.487 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:48:00.489 +08:00 [INF] CORS policy execution successful.
2025-09-11 11:48:00.489 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 11:48:00.490 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 11:48:00.491 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 11:48:00.492 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 11:48:00.657 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 11:48:00.658 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 11:48:00.658 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 163.7081ms
2025-09-11 11:48:00.659 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 163.2228ms
2025-09-11 11:48:00.660 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 11:48:00.661 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 11:48:00.794 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 310.4304ms
2025-09-11 11:48:00.794 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 310.8201ms
2025-09-11 12:00:20.845 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 12:00:20.851 +08:00 [INF] CORS policy execution successful.
2025-09-11 12:00:20.852 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 7.5324ms
2025-09-11 12:00:20.853 +08:00 [INF] Request starting HTTP/2 POST https://localhost:7007/api/SendMsg/sms-template - application/json 96
2025-09-11 12:00:20.856 +08:00 [INF] CORS policy execution successful.
2025-09-11 12:00:20.857 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.SaveSmsTemplate (MapStudio.Api)'
2025-09-11 12:00:20.865 +08:00 [INF] Route matched with {action = "SaveSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]] SaveSmsTemplate(MapStudio.Api.Models.DTOs.SaveSmsTemplateRequestDto) on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 12:00:21.425 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 12:00:21.428 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.SaveSmsTemplate (MapStudio.Api) in 560.9398ms
2025-09-11 12:00:21.429 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.SaveSmsTemplate (MapStudio.Api)'
2025-09-11 12:00:21.552 +08:00 [INF] Request finished HTTP/2 POST https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 698.3237ms
2025-09-11 12:00:25.138 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 12:00:25.138 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 12:00:25.140 +08:00 [INF] CORS policy execution successful.
2025-09-11 12:00:25.142 +08:00 [INF] CORS policy execution successful.
2025-09-11 12:00:25.143 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 4.877ms
2025-09-11 12:00:25.147 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 12:00:25.148 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 12:00:25.151 +08:00 [INF] CORS policy execution successful.
2025-09-11 12:00:25.152 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 12:00:25.153 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 12:00:25.155 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 12:00:25.326 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 12:00:25.328 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 173.4385ms
2025-09-11 12:00:25.328 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 12:00:25.540 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 402.5181ms
2025-09-11 12:00:25.588 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 12:00:25.590 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 432.617ms
2025-09-11 12:00:25.591 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 12:00:25.717 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 570.4788ms
2025-09-11 12:48:05.762 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 12:48:08.345 +08:00 [INF] CORS policy execution successful.
2025-09-11 12:48:08.398 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 2636.1352ms
2025-09-11 12:48:08.425 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 12:48:08.429 +08:00 [INF] CORS policy execution successful.
2025-09-11 12:48:08.432 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 12:48:08.433 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 12:48:15.679 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 12:48:15.680 +08:00 [INF] CORS policy execution successful.
2025-09-11 12:48:15.681 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 1.8429ms
2025-09-11 12:48:15.683 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 12:48:15.685 +08:00 [INF] CORS policy execution successful.
2025-09-11 12:48:15.685 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 12:48:15.686 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 12:48:23.881 +08:00 [ERR] 获取服务类型失败
SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="".
English Message : Connection open error . Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.MySqlProvider.GetCommand(String sql, SugarParameter[] parameters)
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.MetadataService.GetServiceTypesAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\MetadataService.cs:line 23
2025-09-11 12:48:23.934 +08:00 [ERR] 获取服务类型失败
SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="".
English Message : Connection open error . Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.MySqlProvider.GetCommand(String sql, SugarParameter[] parameters)
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.MetadataService.GetServiceTypesAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\MetadataService.cs:line 23
   at MapStudio.Api.Controllers.MetadataController.GetServiceTypes() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Controllers\FileController.cs:line 142
2025-09-11 12:48:23.939 +08:00 [INF] Executing ObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 12:48:24.026 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 15590.9152ms
2025-09-11 12:48:24.027 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 12:48:24.324 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 499 null application/json; charset=utf-8 15898.8547ms
2025-09-11 12:48:27.682 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 12:48:27.683 +08:00 [INF] CORS policy execution successful.
2025-09-11 12:48:27.684 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 2.0757ms
2025-09-11 12:48:27.685 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 12:48:27.687 +08:00 [INF] CORS policy execution successful.
2025-09-11 12:48:27.687 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 12:48:27.688 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 12:48:27.746 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 12:48:27.747 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 58.5182ms
2025-09-11 12:48:27.749 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 12:48:27.947 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 262.2691ms
2025-09-11 12:48:31.063 +08:00 [ERR] 获取服务类型失败
SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="".
English Message : Connection open error . Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.MySqlProvider.GetCommand(String sql, SugarParameter[] parameters)
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.MetadataService.GetServiceTypesAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\MetadataService.cs:line 23
2025-09-11 12:48:31.131 +08:00 [ERR] 获取服务类型失败
SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="".
English Message : Connection open error . Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.MySqlProvider.GetCommand(String sql, SugarParameter[] parameters)
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.MetadataService.GetServiceTypesAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\MetadataService.cs:line 23
   at MapStudio.Api.Controllers.MetadataController.GetServiceTypes() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Controllers\FileController.cs:line 142
2025-09-11 12:48:31.136 +08:00 [INF] Executing ObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 12:48:31.219 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 15531.9432ms
2025-09-11 12:48:31.220 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 12:48:31.330 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 499 null application/json; charset=utf-8 15646.5598ms
2025-09-11 13:01:17.140 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 13:01:17.143 +08:00 [INF] CORS policy execution successful.
2025-09-11 13:01:17.156 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 15.9677ms
2025-09-11 13:01:17.161 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 13:01:17.164 +08:00 [INF] CORS policy execution successful.
2025-09-11 13:01:17.164 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 13:01:17.165 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 13:01:28.090 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 13:01:28.095 +08:00 [INF] CORS policy execution successful.
2025-09-11 13:01:28.095 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 5.5368ms
2025-09-11 13:01:28.096 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 13:01:28.098 +08:00 [INF] CORS policy execution successful.
2025-09-11 13:01:28.098 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 13:01:28.099 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 13:01:32.615 +08:00 [ERR] 获取服务类型失败
SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="".
English Message : Connection open error . Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.MySqlProvider.GetCommand(String sql, SugarParameter[] parameters)
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.MetadataService.GetServiceTypesAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\MetadataService.cs:line 23
2025-09-11 13:01:32.662 +08:00 [ERR] 获取服务类型失败
SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="".
English Message : Connection open error . Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.MySqlProvider.GetCommand(String sql, SugarParameter[] parameters)
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.MetadataService.GetServiceTypesAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\MetadataService.cs:line 23
   at MapStudio.Api.Controllers.MetadataController.GetServiceTypes() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Controllers\FileController.cs:line 142
2025-09-11 13:01:32.666 +08:00 [INF] Executing ObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 13:01:32.749 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 15581.1204ms
2025-09-11 13:01:32.750 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 13:01:40.094 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 13:01:40.096 +08:00 [INF] CORS policy execution successful.
2025-09-11 13:01:40.097 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 2.1341ms
2025-09-11 13:01:40.098 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 13:01:40.099 +08:00 [INF] CORS policy execution successful.
2025-09-11 13:01:40.099 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 13:01:40.100 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 13:01:43.541 +08:00 [ERR] 获取服务类型失败
SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="".
English Message : Connection open error . Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.MySqlProvider.GetCommand(String sql, SugarParameter[] parameters)
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.MetadataService.GetServiceTypesAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\MetadataService.cs:line 23
2025-09-11 13:01:43.589 +08:00 [ERR] 获取服务类型失败
SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="".
English Message : Connection open error . Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.MySqlProvider.GetCommand(String sql, SugarParameter[] parameters)
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.MetadataService.GetServiceTypesAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\MetadataService.cs:line 23
   at MapStudio.Api.Controllers.MetadataController.GetServiceTypes() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Controllers\FileController.cs:line 142
2025-09-11 13:01:43.593 +08:00 [INF] Executing ObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 13:01:43.678 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 15578.2452ms
2025-09-11 13:01:43.679 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 13:01:44.121 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 499 null application/json; charset=utf-8 16024.1543ms
2025-09-11 13:01:48.007 +08:00 [ERR] 记录操作日志失败
SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="".
English Message : Connection open error . Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.MySqlProvider.GetCommand(String sql, SugarParameter[] parameters)
   at SqlSugar.AdoProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at MapStudio.Api.Services.Implementations.OperationService.LogOperationAsync(String tenantId, String userId, String actionType, String description) in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\OperationService.cs:line 260
2025-09-11 13:01:48.011 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 499 null application/json; charset=utf-8 30849.5468ms
2025-09-11 13:01:54.096 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 13:01:54.097 +08:00 [INF] CORS policy execution successful.
2025-09-11 13:01:54.098 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 1.5752ms
2025-09-11 13:01:54.099 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 13:01:54.100 +08:00 [INF] CORS policy execution successful.
2025-09-11 13:01:54.101 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 13:01:54.101 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 13:01:54.171 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 13:01:54.173 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 70.9335ms
2025-09-11 13:01:54.174 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 13:01:54.295 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 196.1863ms
2025-09-11 13:01:55.505 +08:00 [ERR] 获取服务类型失败
SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="".
English Message : Connection open error . Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.MySqlProvider.GetCommand(String sql, SugarParameter[] parameters)
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.MetadataService.GetServiceTypesAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\MetadataService.cs:line 23
2025-09-11 13:01:55.562 +08:00 [ERR] 获取服务类型失败
SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="".
English Message : Connection open error . Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.MySqlProvider.GetCommand(String sql, SugarParameter[] parameters)
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.MetadataService.GetServiceTypesAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\MetadataService.cs:line 23
   at MapStudio.Api.Controllers.MetadataController.GetServiceTypes() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Controllers\FileController.cs:line 142
2025-09-11 13:01:55.567 +08:00 [INF] Executing ObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 13:01:55.660 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 15559.033ms
2025-09-11 13:01:55.661 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 13:01:55.801 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 499 null application/json; charset=utf-8 15703.6929ms
2025-09-11 14:09:37.138 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 14:09:39.030 +08:00 [INF] CORS policy execution successful.
2025-09-11 14:09:39.031 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 1892.5443ms
2025-09-11 14:09:39.272 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 14:09:44.528 +08:00 [INF] CORS policy execution successful.
2025-09-11 14:09:44.529 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 14:09:44.530 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 14:09:47.582 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 14:09:47.584 +08:00 [INF] CORS policy execution successful.
2025-09-11 14:09:47.584 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 2.6031ms
2025-09-11 14:09:47.590 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 14:09:47.593 +08:00 [INF] CORS policy execution successful.
2025-09-11 14:09:47.593 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 14:09:47.594 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 14:09:59.628 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 14:09:59.706 +08:00 [INF] CORS policy execution successful.
2025-09-11 14:09:59.722 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 94.6861ms
2025-09-11 14:09:59.725 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 14:09:59.833 +08:00 [INF] CORS policy execution successful.
2025-09-11 14:09:59.895 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 14:09:59.929 +08:00 [ERR] 获取服务类型失败
SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="".
English Message : Connection open error . Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.MySqlProvider.GetCommand(String sql, SugarParameter[] parameters)
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.MetadataService.GetServiceTypesAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\MetadataService.cs:line 23
2025-09-11 14:09:59.930 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 14:10:00.004 +08:00 [ERR] 获取服务类型失败
SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="".
English Message : Connection open error . Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.MySqlProvider.GetCommand(String sql, SugarParameter[] parameters)
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.MetadataService.GetServiceTypesAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\MetadataService.cs:line 23
   at MapStudio.Api.Controllers.MetadataController.GetServiceTypes() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Controllers\FileController.cs:line 142
2025-09-11 14:10:00.011 +08:00 [INF] Executing ObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 14:10:00.102 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 15570.0288ms
2025-09-11 14:10:00.103 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 14:10:03.070 +08:00 [ERR] 获取服务类型失败
SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="".
English Message : Connection open error . Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.MySqlProvider.GetCommand(String sql, SugarParameter[] parameters)
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.MetadataService.GetServiceTypesAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\MetadataService.cs:line 23
2025-09-11 14:10:03.116 +08:00 [ERR] 获取服务类型失败
SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="".
English Message : Connection open error . Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.MySqlProvider.GetCommand(String sql, SugarParameter[] parameters)
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.MetadataService.GetServiceTypesAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\MetadataService.cs:line 23
   at MapStudio.Api.Controllers.MetadataController.GetServiceTypes() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Controllers\FileController.cs:line 142
2025-09-11 14:10:03.120 +08:00 [INF] Executing ObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 14:10:03.208 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 15612.7283ms
2025-09-11 14:10:03.209 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 14:10:03.579 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 499 null application/json; charset=utf-8 15988.4395ms
2025-09-11 14:10:13.593 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 14:10:13.595 +08:00 [INF] CORS policy execution successful.
2025-09-11 14:10:13.597 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 3.8983ms
2025-09-11 14:10:13.598 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 14:10:13.608 +08:00 [INF] CORS policy execution successful.
2025-09-11 14:10:13.613 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 14:10:13.615 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 14:10:13.708 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 14:10:13.710 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 86.1592ms
2025-09-11 14:10:13.712 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 14:10:13.853 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 255.408ms
2025-09-11 14:10:15.751 +08:00 [ERR] 获取服务类型失败
SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="".
English Message : Connection open error . Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.MySqlProvider.GetCommand(String sql, SugarParameter[] parameters)
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.MetadataService.GetServiceTypesAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\MetadataService.cs:line 23
2025-09-11 14:10:15.757 +08:00 [ERR] 记录操作日志失败
SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="".
English Message : Connection open error . Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.MySqlProvider.GetCommand(String sql, SugarParameter[] parameters)
   at SqlSugar.AdoProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at MapStudio.Api.Services.Implementations.OperationService.LogOperationAsync(String tenantId, String userId, String actionType, String description) in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\OperationService.cs:line 260
2025-09-11 14:10:15.807 +08:00 [ERR] 获取服务类型失败
SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="".
English Message : Connection open error . Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.MySqlProvider.GetCommand(String sql, SugarParameter[] parameters)
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.MetadataService.GetServiceTypesAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\MetadataService.cs:line 23
   at MapStudio.Api.Controllers.MetadataController.GetServiceTypes() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Controllers\FileController.cs:line 142
2025-09-11 14:10:15.808 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 499 null application/json; charset=utf-8 36535.0695ms
2025-09-11 14:10:15.812 +08:00 [INF] Executing ObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 14:10:15.906 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 15953.0803ms
2025-09-11 14:10:15.906 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 14:10:16.059 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 499 null application/json; charset=utf-8 16334.3801ms
2025-09-11 14:28:01.222 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 14:28:01.223 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 14:28:01.230 +08:00 [INF] CORS policy execution successful.
2025-09-11 14:28:01.230 +08:00 [INF] CORS policy execution successful.
2025-09-11 14:28:01.231 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 8.5376ms
2025-09-11 14:28:01.235 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 11.7519ms
2025-09-11 14:28:01.237 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 14:28:01.238 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 14:28:01.247 +08:00 [INF] CORS policy execution successful.
2025-09-11 14:28:01.249 +08:00 [INF] CORS policy execution successful.
2025-09-11 14:28:01.249 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 14:28:01.250 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 14:28:01.252 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 14:28:01.252 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 14:28:01.586 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 14:28:01.588 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 332.9226ms
2025-09-11 14:28:01.590 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 14:28:01.602 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 14:28:01.604 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 350.0115ms
2025-09-11 14:28:01.605 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 14:28:01.775 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 538.1634ms
2025-09-11 14:28:01.785 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 547.7536ms
2025-09-11 14:54:07.374 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 14:54:07.376 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 14:54:07.380 +08:00 [INF] CORS policy execution successful.
2025-09-11 14:54:07.381 +08:00 [INF] CORS policy execution successful.
2025-09-11 14:54:07.383 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 6.4969ms
2025-09-11 14:54:07.383 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 8.4467ms
2025-09-11 14:54:07.389 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 14:54:07.389 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 14:54:07.399 +08:00 [INF] CORS policy execution successful.
2025-09-11 14:54:07.400 +08:00 [INF] CORS policy execution successful.
2025-09-11 14:54:07.400 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 14:54:07.400 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 14:54:07.401 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 14:54:07.401 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 14:54:07.796 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 14:54:07.801 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 394.6793ms
2025-09-11 14:54:07.801 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 14:54:07.804 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 14:54:07.805 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 401.9918ms
2025-09-11 14:54:07.806 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 14:54:07.935 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 546.7029ms
2025-09-11 14:54:07.951 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 562.4754ms
2025-09-11 14:54:16.264 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 14:54:16.264 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 14:54:16.265 +08:00 [INF] CORS policy execution successful.
2025-09-11 14:54:16.267 +08:00 [INF] CORS policy execution successful.
2025-09-11 14:54:16.268 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 3.7775ms
2025-09-11 14:54:16.268 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 4.4325ms
2025-09-11 14:54:16.270 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 14:54:16.270 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 14:54:16.289 +08:00 [INF] CORS policy execution successful.
2025-09-11 14:54:16.290 +08:00 [INF] CORS policy execution successful.
2025-09-11 14:54:16.291 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 14:54:16.291 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 14:54:16.292 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 14:54:16.292 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 14:54:16.437 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 14:54:16.438 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 14:54:16.438 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 142.4422ms
2025-09-11 14:54:16.439 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 145.3926ms
2025-09-11 14:54:16.440 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 14:54:16.441 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 14:54:16.605 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 335.1555ms
2025-09-11 14:54:16.645 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 374.9335ms
2025-09-11 15:11:37.152 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 15:11:37.152 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 15:11:37.156 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:11:37.166 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:11:37.167 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 15.2829ms
2025-09-11 15:11:37.167 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 15.9605ms
2025-09-11 15:11:37.169 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 15:11:37.169 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 15:11:37.181 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 15:11:37.184 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:11:37.185 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:11:37.186 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:11:37.187 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 15:11:37.187 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 15:11:37.188 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 6.3017ms
2025-09-11 15:11:37.189 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 15:11:37.190 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 15:11:37.190 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 15:11:37.195 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:11:37.200 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 15:11:37.201 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 15:11:37.682 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 15:11:37.684 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 481.0914ms
2025-09-11 15:11:37.685 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 15:11:37.738 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 15:11:37.739 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 546.004ms
2025-09-11 15:11:37.740 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 15:11:37.742 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 15:11:37.745 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 546.0293ms
2025-09-11 15:11:37.746 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 15:11:37.809 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 618.3274ms
2025-09-11 15:11:37.867 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 697.788ms
2025-09-11 15:11:37.890 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 720.6224ms
2025-09-11 15:12:23.638 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 15:12:23.638 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 15:12:23.644 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:12:23.645 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:12:23.646 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 8.3182ms
2025-09-11 15:12:23.647 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 9.242ms
2025-09-11 15:12:23.649 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 15:12:23.648 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 15:12:23.656 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:12:23.657 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:12:23.657 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 15:12:23.658 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 15:12:23.658 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 15:12:23.659 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 15:12:23.780 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 15:12:23.781 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 15:12:23.782 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 122.1292ms
2025-09-11 15:12:23.783 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 121.3367ms
2025-09-11 15:12:23.784 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 15:12:23.785 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 15:12:23.915 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 266.567ms
2025-09-11 15:12:23.926 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 277.9531ms
2025-09-11 15:16:11.999 +08:00 [INF] 开始初始化数据库...
2025-09-11 15:16:17.433 +08:00 [INF] 数据库表创建完成
2025-09-11 15:16:17.990 +08:00 [INF] 数据库初始化完成
2025-09-11 15:16:18.017 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-11 15:16:18.288 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-11 15:16:18.289 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-11 15:16:18.355 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-11 15:16:18.356 +08:00 [INF] Hosting environment: Development
2025-09-11 15:16:18.357 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-11 15:18:39.927 +08:00 [INF] 开始初始化数据库...
2025-09-11 15:18:45.521 +08:00 [INF] 数据库表创建完成
2025-09-11 15:18:45.949 +08:00 [INF] 数据库初始化完成
2025-09-11 15:18:45.983 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-11 15:18:46.250 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-11 15:18:46.251 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-11 15:18:46.321 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-11 15:18:46.326 +08:00 [INF] Hosting environment: Development
2025-09-11 15:18:46.327 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-11 15:19:22.649 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 15:19:22.649 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 15:19:22.738 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:19:22.738 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:19:22.744 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 97.0095ms
2025-09-11 15:19:22.744 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 97.0062ms
2025-09-11 15:19:22.749 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 15:19:22.749 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 15:19:22.768 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:19:22.768 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:19:22.773 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 15:19:22.773 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 15:19:22.796 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 15:19:22.796 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 15:19:23.061 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 15:19:23.085 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 282.6332ms
2025-09-11 15:19:23.086 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 15:19:23.199 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 15:19:23.204 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 405.9581ms
2025-09-11 15:19:23.205 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 15:19:23.280 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 531.4562ms
2025-09-11 15:19:23.334 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 584.8109ms
2025-09-11 15:19:26.235 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/send-alert-sms - null null
2025-09-11 15:19:26.238 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:19:26.239 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/send-alert-sms - 204 null null 3.2402ms
2025-09-11 15:19:26.240 +08:00 [INF] Request starting HTTP/2 POST https://localhost:7007/api/SendMsg/send-alert-sms - application/json 54
2025-09-11 15:19:26.243 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:19:26.244 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.SendAlertSms (MapStudio.Api)'
2025-09-11 15:19:26.253 +08:00 [INF] Route matched with {action = "SendAlertSms", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SendSmsResponseDto]]] SendAlertSms(MapStudio.Api.Models.DTOs.SendAlertSmsRequestDto) on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 15:19:35.817 +08:00 [INF] 开始发送告警短信到手机号: 15167172303, 级别: system_failure
2025-09-11 15:19:41.347 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/send-alert-sms - null null
2025-09-11 15:19:41.375 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:19:41.376 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/send-alert-sms - 204 null null 29.579ms
2025-09-11 15:19:41.378 +08:00 [INF] Request starting HTTP/2 POST https://localhost:7007/api/SendMsg/send-alert-sms - application/json 54
2025-09-11 15:19:55.502 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/send-alert-sms - null null
2025-09-11 15:19:56.167 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:19:56.168 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:19:56.169 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.SendAlertSms (MapStudio.Api)'
2025-09-11 15:19:56.170 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/send-alert-sms - 204 null null 668.9689ms
2025-09-11 15:19:56.171 +08:00 [INF] Route matched with {action = "SendAlertSms", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SendSmsResponseDto]]] SendAlertSms(MapStudio.Api.Models.DTOs.SendAlertSmsRequestDto) on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 15:19:56.172 +08:00 [INF] Request starting HTTP/2 POST https://localhost:7007/api/SendMsg/send-alert-sms - application/json 54
2025-09-11 15:19:56.178 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:19:56.198 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.SendAlertSms (MapStudio.Api)'
2025-09-11 15:19:56.214 +08:00 [INF] Route matched with {action = "SendAlertSms", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SendSmsResponseDto]]] SendAlertSms(MapStudio.Api.Models.DTOs.SendAlertSmsRequestDto) on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 15:20:02.873 +08:00 [INF] 开始发送告警短信到手机号: 15167172303, 级别: system_failure
2025-09-11 15:20:03.287 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.SendAlertSms (MapStudio.Api) in 7110.8584ms
2025-09-11 15:20:03.321 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.SendAlertSms (MapStudio.Api)'
2025-09-11 15:20:03.863 +08:00 [ERR] 未处理的异常发生在 /api/SendMsg/send-alert-sms
System.IO.IOException: The client reset the request stream.
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http.HttpRequestStream.ReadAsyncInternal(Memory`1 destination, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at System.Text.Json.Serialization.ReadBufferState.ReadFromStreamAsync(Stream utf8Json, CancellationToken cancellationToken, Boolean fillBuffer)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsObjectAsync(Stream utf8Json, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonInputFormatter.ReadRequestBodyAsync(InputFormatterContext context, Encoding encoding)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonInputFormatter.ReadRequestBodyAsync(InputFormatterContext context, Encoding encoding)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BodyModelBinder.BindModelAsync(ModelBindingContext bindingContext)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.BindModelAsync(ActionContext actionContext, IModelBinder modelBinder, IValueProvider valueProvider, ParameterDescriptor parameter, ModelMetadata metadata, Object value, Object container)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerBinderDelegateProvider.<>c__DisplayClass0_0.<<CreateBinderDelegate>g__Bind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at MapStudio.Api.Middleware.OperationLoggingMiddleware.InvokeAsync(HttpContext context) in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Middleware\OperationLoggingMiddleware.cs:line 30
   at MapStudio.Api.Middleware.GlobalExceptionMiddleware.InvokeAsync(HttpContext context) in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Middleware\GlobalExceptionMiddleware.cs:line 22
2025-09-11 15:20:06.371 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/send-alert-sms - null null
2025-09-11 15:20:07.316 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:20:07.317 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/send-alert-sms - 204 null null 945.7001ms
2025-09-11 15:20:07.318 +08:00 [INF] Request starting HTTP/2 POST https://localhost:7007/api/SendMsg/send-alert-sms - application/json 54
2025-09-11 15:20:07.319 +08:00 [INF] Request finished HTTP/2 POST https://localhost:7007/api/SendMsg/send-alert-sms - 500 null application/json 25941.2665ms
2025-09-11 15:20:07.320 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:20:07.323 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.SendAlertSms (MapStudio.Api)'
2025-09-11 15:20:07.323 +08:00 [INF] Route matched with {action = "SendAlertSms", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SendSmsResponseDto]]] SendAlertSms(MapStudio.Api.Models.DTOs.SendAlertSmsRequestDto) on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 15:20:29.936 +08:00 [INF] 开始发送告警短信到手机号: 15167172303, 级别: system_failure
2025-09-11 15:26:45.273 +08:00 [INF] 开始初始化数据库...
2025-09-11 15:26:50.899 +08:00 [INF] 数据库表创建完成
2025-09-11 15:26:51.403 +08:00 [INF] 数据库初始化完成
2025-09-11 15:26:51.432 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-11 15:26:51.798 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-11 15:26:51.799 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-11 15:26:51.882 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-11 15:26:51.883 +08:00 [INF] Hosting environment: Development
2025-09-11 15:26:51.884 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-11 15:26:55.153 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 15:26:55.153 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 15:26:55.277 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:26:55.277 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:26:55.287 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 138.0551ms
2025-09-11 15:26:55.287 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 137.6305ms
2025-09-11 15:26:55.299 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 15:26:55.299 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 15:26:55.331 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:26:55.331 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:26:55.339 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 15:26:55.339 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 15:26:55.368 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 15:26:55.368 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 15:26:55.664 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 15:26:55.687 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 308.304ms
2025-09-11 15:26:55.689 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 15:26:55.900 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 15:26:55.906 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 535.3206ms
2025-09-11 15:26:55.908 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 15:26:55.912 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 613.2928ms
2025-09-11 15:26:56.080 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 780.8115ms
2025-09-11 15:26:58.920 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/send-alert-sms - null null
2025-09-11 15:26:58.922 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:26:58.923 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/send-alert-sms - 204 null null 2.528ms
2025-09-11 15:26:58.925 +08:00 [INF] Request starting HTTP/2 POST https://localhost:7007/api/SendMsg/send-alert-sms - application/json 54
2025-09-11 15:26:58.928 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:26:58.928 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.SendAlertSms (MapStudio.Api)'
2025-09-11 15:26:58.938 +08:00 [INF] Route matched with {action = "SendAlertSms", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SendSmsResponseDto]]] SendAlertSms(MapStudio.Api.Models.DTOs.SendAlertSmsRequestDto) on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 15:27:00.727 +08:00 [INF] 开始发送告警短信到手机号: 15167172303, 级别: system_failure
2025-09-11 15:28:03.798 +08:00 [INF] 开始初始化数据库...
2025-09-11 15:28:09.038 +08:00 [INF] 数据库表创建完成
2025-09-11 15:28:09.533 +08:00 [INF] 数据库初始化完成
2025-09-11 15:28:09.551 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-11 15:28:09.734 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-11 15:28:09.735 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-11 15:28:09.788 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-11 15:28:09.789 +08:00 [INF] Hosting environment: Development
2025-09-11 15:28:09.790 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-11 15:28:16.565 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 15:28:16.565 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 15:28:16.615 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:28:16.615 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:28:16.620 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 57.2812ms
2025-09-11 15:28:16.620 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 57.2828ms
2025-09-11 15:28:16.625 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-11 15:28:16.625 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-11 15:28:16.644 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:28:16.644 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:28:16.649 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 15:28:16.649 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 15:28:16.667 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 15:28:16.667 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 15:28:16.923 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 15:28:16.939 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 266.0346ms
2025-09-11 15:28:16.941 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-11 15:28:17.087 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 15:28:17.093 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 424.48ms
2025-09-11 15:28:17.094 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-11 15:28:17.125 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 499.9142ms
2025-09-11 15:28:17.231 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 606.1135ms
2025-09-11 15:28:18.768 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/send-alert-sms - null null
2025-09-11 15:28:18.770 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:28:18.771 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/send-alert-sms - 204 null null 2.8381ms
2025-09-11 15:28:18.772 +08:00 [INF] Request starting HTTP/2 POST https://localhost:7007/api/SendMsg/send-alert-sms - application/json 54
2025-09-11 15:28:18.775 +08:00 [INF] CORS policy execution successful.
2025-09-11 15:28:18.776 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.SendAlertSms (MapStudio.Api)'
2025-09-11 15:28:18.786 +08:00 [INF] Route matched with {action = "SendAlertSms", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SendSmsResponseDto]]] SendAlertSms(MapStudio.Api.Models.DTOs.SendAlertSmsRequestDto) on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-11 15:28:21.462 +08:00 [INF] 开始发送告警短信到手机号: 15167172303, 级别: system_failure
2025-09-11 17:54:32.290 +08:00 [INF] Start processing HTTP request POST http://*************:20333/service/smsProviderService
2025-09-11 17:54:35.226 +08:00 [INF] Sending HTTP request POST http://*************:20333/service/smsProviderService
2025-09-11 17:54:35.236 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/send-alert-sms - null null
2025-09-11 17:54:35.286 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 17:54:35.877 +08:00 [INF] CORS policy execution successful.
2025-09-11 17:54:35.881 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/send-alert-sms - 499 null null 646.3502ms
2025-09-11 17:54:35.888 +08:00 [INF] CORS policy execution successful.
2025-09-11 17:54:35.898 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 611.9728ms
2025-09-11 17:54:36.680 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 17:54:36.688 +08:00 [INF] CORS policy execution successful.
2025-09-11 17:54:36.690 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 17:54:36.729 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 17:54:37.057 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 17:54:37.075 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 342.3958ms
2025-09-11 17:54:37.078 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 17:54:37.250 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 570.5658ms
2025-09-11 17:54:58.523 +08:00 [ERR] 发送告警短信时发生异常
System.Net.Http.HttpRequestException: 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 (*************:20333)
 ---> System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MapStudio.Api.Controllers.SendMsgController.SendAlertSms(SendAlertSmsRequestDto request) in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Controllers\SendMsgController.cs:line 416
2025-09-11 17:54:58.557 +08:00 [INF] Executing ObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SendSmsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-11 17:54:59.949 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.SendAlertSms (MapStudio.Api) in 8801160.9192ms
2025-09-11 17:54:59.950 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.SendAlertSms (MapStudio.Api)'
2025-09-11 17:55:00.118 +08:00 [INF] Request finished HTTP/2 POST https://localhost:7007/api/SendMsg/send-alert-sms - 499 null application/json; charset=utf-8 8801345.6266ms
2025-09-11 17:55:32.238 +08:00 [INF] 开始初始化数据库...
2025-09-11 17:55:38.266 +08:00 [INF] 数据库表创建完成
2025-09-11 17:55:38.842 +08:00 [INF] 数据库初始化完成
2025-09-11 17:55:38.887 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-11 17:55:39.190 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-11 17:55:39.195 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-11 17:55:39.269 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-11 17:55:39.270 +08:00 [INF] Hosting environment: Development
2025-09-11 17:55:39.272 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-11 17:56:25.345 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 17:56:25.434 +08:00 [INF] CORS policy execution successful.
2025-09-11 17:56:25.440 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 97.1241ms
2025-09-11 17:56:25.444 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 17:56:25.464 +08:00 [INF] CORS policy execution successful.
2025-09-11 17:56:25.470 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 17:56:25.491 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 17:56:25.616 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 17:56:25.637 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 138.7875ms
2025-09-11 17:56:25.638 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 17:56:25.962 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 517.1846ms
2025-09-11 17:57:21.610 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 17:57:21.626 +08:00 [INF] CORS policy execution successful.
2025-09-11 17:57:21.628 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 17.9881ms
2025-09-11 17:57:21.630 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 17:57:21.635 +08:00 [INF] CORS policy execution successful.
2025-09-11 17:57:21.637 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 17:57:21.638 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 17:57:21.702 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 17:57:21.704 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 63.5137ms
2025-09-11 17:57:21.706 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 17:57:21.865 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 235.671ms
2025-09-11 18:00:21.029 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 18:00:21.038 +08:00 [INF] CORS policy execution failed.
2025-09-11 18:00:21.039 +08:00 [INF] Request origin http://*************:3001 does not have permission to access the resource.
2025-09-11 18:00:21.040 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 10.9436ms
2025-09-11 18:00:22.096 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 18:00:22.097 +08:00 [INF] CORS policy execution failed.
2025-09-11 18:00:22.098 +08:00 [INF] Request origin http://*************:3001 does not have permission to access the resource.
2025-09-11 18:00:22.099 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 3.8018ms
2025-09-11 18:00:33.308 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 18:00:33.312 +08:00 [INF] CORS policy execution successful.
2025-09-11 18:00:33.314 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 5.7344ms
2025-09-11 18:00:33.316 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 18:00:33.322 +08:00 [INF] CORS policy execution successful.
2025-09-11 18:00:33.323 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 18:00:33.325 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 18:00:33.396 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 18:00:33.398 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 68.636ms
2025-09-11 18:00:33.399 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 18:00:33.514 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 197.1189ms
2025-09-11 18:00:36.601 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 18:00:36.612 +08:00 [INF] CORS policy execution successful.
2025-09-11 18:00:36.614 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 18:00:36.615 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 18:00:36.722 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 18:00:36.725 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 107.7194ms
2025-09-11 18:00:36.726 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 18:00:36.855 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 253.1217ms
2025-09-11 18:01:07.464 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 18:01:07.470 +08:00 [INF] CORS policy execution successful.
2025-09-11 18:01:07.471 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 7.0378ms
2025-09-11 18:01:07.472 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 18:01:07.475 +08:00 [INF] CORS policy execution successful.
2025-09-11 18:01:07.476 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 18:01:07.477 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 18:01:07.540 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 18:01:07.543 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 64.2252ms
2025-09-11 18:01:07.543 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 18:01:07.670 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 197.7785ms
2025-09-11 18:02:09.912 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 18:02:09.921 +08:00 [INF] CORS policy execution successful.
2025-09-11 18:02:09.924 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 12.5291ms
2025-09-11 18:02:09.926 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 18:02:09.930 +08:00 [INF] CORS policy execution successful.
2025-09-11 18:02:09.931 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 18:02:09.933 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 18:02:10.012 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 18:02:10.018 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 80.7666ms
2025-09-11 18:02:10.020 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 18:02:10.169 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 243.4867ms
2025-09-11 18:02:12.107 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-11 18:02:12.112 +08:00 [INF] CORS policy execution successful.
2025-09-11 18:02:12.113 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 18:02:12.115 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-11 18:02:12.204 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-11 18:02:12.208 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 91.0688ms
2025-09-11 18:02:12.210 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-11 18:02:12.331 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 223.9153ms
