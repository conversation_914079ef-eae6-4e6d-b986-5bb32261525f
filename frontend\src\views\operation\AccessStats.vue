<template>
  <div class="p-6 space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">租户服务访问总统计报表</h1>
      <p class="mt-1 text-gray-500">查看平台访问量、操作次数和响应时间等核心指标</p>
    </div>

    <!-- 条件与操作 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div class="flex items-center space-x-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">时间范围</label>
            <div class="flex space-x-2">
              <button
                v-for="range in ranges"
                :key="range.value"
                @click="handleTimeRangeChange(range.value)"
                class="px-4 py-2 rounded-md text-sm font-medium transition-colors"
                :class="timeRange === range.value ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'"
              >
                {{ range.label }}
              </button>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">图表类型</label>
            <div class="flex space-x-2">
              <button
                @click="handleChartTypeChange('line')"
                class="px-4 py-2 rounded-md text-sm font-medium transition-colors"
                :class="chartType === 'line' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'"
              >
                折线图
              </button>
              <button
                @click="handleChartTypeChange('bar')"
                class="px-4 py-2 rounded-md text-sm font-medium transition-colors"
                :class="chartType === 'bar' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'"
              >
                柱状图
              </button>
            </div>
          </div>
        </div>

        <div class="flex space-x-3">
          <button class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            导出报表
          </button>
          <button class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            打印
          </button>
        </div>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">总访问量</p>
            <h3 class="mt-1 text-2xl font-bold text-gray-900">{{ totalVisits.toLocaleString() }}</h3>
            <p class="mt-1 text-xs text-green-500 flex items-center">12.5% 较上期</p>
          </div>
          <div class="p-3 bg-blue-100 rounded-lg">
            <span class="text-blue-600 text-xl">👁️</span>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">总操作次数</p>
            <h3 class="mt-1 text-2xl font-bold text-gray-900">{{ totalOperations.toLocaleString() }}</h3>
            <p class="mt-1 text-xs text-green-500 flex items-center">8.3% 较上期</p>
          </div>
          <div class="p-3 bg-purple-100 rounded-lg">
            <span class="text-purple-600 text-xl">🖱️</span>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">平均响应时间</p>
            <h3 class="mt-1 text-2xl font-bold text-gray-900">{{ avgResponseTime }} ms</h3>
            <p class="mt-1 text-xs text-red-500 flex items-center">3.2% 较上期</p>
          </div>
          <div class="p-3 bg-green-100 rounded-lg">
            <span class="text-green-600 text-xl">⏱️</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 访问趋势图表（容器占位，后续接入图表库） -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-gray-900">访问趋势分析</h2>
        <div class="flex space-x-2">
          <button class="text-sm text-gray-500 hover:text-gray-700">导出数据</button>
          <button class="text-sm text-gray-500 hover:text-gray-700">全屏查看</button>
        </div>
      </div>

      <div class="h-96">
        <ChartBase :option="trendOptions" :loading="chartLoading" class="h-96" />
      </div>
    </div>

    <!-- 服务类型分布 + 访问来源分布 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">服务类型访问分布</h2>
        <div class="h-80">
          <ChartBase :option="serviceTypeOptions" :loading="chartLoading" class="h-80" />
        </div>
      </div>

      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">访问来源分布</h2>
        <div class="space-y-6">
          <div>
            <h3 class="text-base font-medium text-gray-900 mb-3">按设备类型</h3>
            <div class="space-y-3">
              <div v-for="(item, index) in deviceDistribution" :key="'device-'+index">
                <div class="flex justify-between mb-1">
                  <span class="text-sm font-medium text-gray-700">{{ item.name }}</span>
                  <span class="text-sm font-medium text-gray-900">{{ item.value }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="h-2 rounded-full" :style="{ width: item.value + '%', backgroundColor: item.color }"></div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-base font-medium text-gray-900 mb-3">按地区分布</h3>
            <div class="space-y-3">
              <div v-for="(item, index) in regionDistribution" :key="'region-'+index">
                <div class="flex justify-between mb-1">
                  <span class="text-sm font-medium text-gray-700">{{ item.name }}</span>
                  <span class="text-sm font-medium text-gray-900">{{ item.value }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="h-2 rounded-full" :style="{ width: item.value + '%', backgroundColor: item.color }"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch } from 'vue'
import type { EChartsOption } from 'echarts'
import ChartBase from '../../components/ChartBase.vue'
import { OperationService } from '../../services/operation.service'

type TimeRange = '7d' | '30d' | '90d' | '180d' | '365d'
type ChartType = 'line' | 'bar'

const ranges = [
  { value: '7d', label: '7天' },
  { value: '30d', label: '30天' },
  { value: '90d', label: '90天' },
  { value: '180d', label: '半年' },
  { value: '365d', label: '一年' },
] as const

const timeRange = ref<TimeRange>('30d')
const chartType = ref<ChartType>('line')
const chartLoading = ref(true)
const loading = ref(false)
const error = ref<string | null>(null)

// 数据
const accessData = ref<any>(null)
const totalVisits = ref(0)
const totalOperations = ref(0)
const avgResponseTime = ref(0)
const trendData = ref<any[]>([])
const serviceTypeDistribution = ref<any[]>([])
const deviceDistribution = ref<any[]>([])
const regionDistribution = ref<any[]>([])

/**
 * 加载访问统计数据
 */
const loadAccessStats = async () => {
  try {
    loading.value = true
    chartLoading.value = true
    error.value = null

    console.log('🔍 开始加载访问统计数据...')
    const response = await OperationService.getAccessStats(timeRange.value, chartType.value)
    
    accessData.value = response
    totalVisits.value = response.overview?.totalVisits || 0
    totalOperations.value = response.overview?.totalOperations || 0
    avgResponseTime.value = response.overview?.avgResponseTime || 0
    trendData.value = response.trendData || []
    serviceTypeDistribution.value = response.serviceTypeDistribution || []
    deviceDistribution.value = response.deviceDistribution || []
    regionDistribution.value = response.regionDistribution || []
    
    console.log('✅ 访问统计数据加载成功')
  } catch (err: any) {
    console.error('❌ 加载访问统计数据失败:', err)
    error.value = err.message || '加载数据失败'
    // 重置数据
    accessData.value = null
    totalVisits.value = 0
    totalOperations.value = 0
    avgResponseTime.value = 0
    trendData.value = []
    serviceTypeDistribution.value = []
    deviceDistribution.value = []
    regionDistribution.value = []
  } finally {
    loading.value = false
    chartLoading.value = false
  }
}

// 处理时间范围变化
const handleTimeRangeChange = (range: TimeRange) => {
  timeRange.value = range
}

// 处理图表类型变化
const handleChartTypeChange = (type: ChartType) => {
  chartType.value = type
}

// 监听变化，重新加载数据
watch([timeRange, chartType], () => {
  loadAccessStats()
})

// 组件挂载时加载数据
onMounted(() => {
  loadAccessStats()
})

function generateAccessData(days = 30): AccessItem[] {
  return Array.from({ length: days }, (_, i) => {
    const date = new Date()
    date.setDate(date.getDate() - (days - i - 1))
    const visits = Math.floor(Math.random() * 1000) + 1000
    const operations = Math.floor(visits * (0.5 + Math.random() * 0.5))
    const responseTime = parseFloat((Math.random() * 300 + 100).toFixed(2))
    return {
      date: date.toLocaleDateString('zh-CN'),
      visits,
      operations,
      responseTime,
    }
  })
}

function generateServiceTypeData(): ServiceTypeItem[] {
  const base = [
    { name: '基础地图服务', value: Math.floor(Math.random() * 40) + 30 },
    { name: '高级地图服务', value: Math.floor(Math.random() * 25) + 15 },
    { name: '企业级地图服务', value: Math.floor(Math.random() * 20) + 10 },
    { name: '定制化地图服务', value: Math.floor(Math.random() * 15) + 5 },
  ]
  const total = base.reduce((s, it) => s + it.value, 0)
  return base.map((it, idx) => ({
    name: it.name,
    value: it.value,
    percentage: parseFloat(((it.value / total) * 100).toFixed(1)),
    color: colors[idx % colors.length],
  }))
}

const serviceTypeData = ref<ServiceTypeItem[]>(generateServiceTypeData())

// 组件挂载
onMounted(() => {
  // 模拟数据加载
  setTimeout(() => {
    chartLoading.value = false
  }, 500)
})

// ECharts：访问趋势（折线/柱状 + 左右双Y轴）
const trendOptions = computed<EChartsOption>(() => {
  const dates = accessData.value.map(d => d.date)
  const isLine = chartType.value === 'line'
  const series = isLine
    ? [
        {
          name: '访问量',
          type: 'line',
          smooth: true,
          data: accessData.value.map(d => d.visits),
          yAxisIndex: 0,
          symbolSize: 4,
          lineStyle: { width: 2 },
          emphasis: { focus: 'series' }
        },
        {
          name: '操作次数',
          type: 'line',
          smooth: true,
          data: accessData.value.map(d => d.operations),
          yAxisIndex: 0,
          symbolSize: 4,
          lineStyle: { width: 2 },
          emphasis: { focus: 'series' }
        },
        {
          name: '响应时间(ms)',
          type: 'line',
          smooth: true,
          data: accessData.value.map(d => d.responseTime),
          yAxisIndex: 1,
          symbolSize: 4,
          lineStyle: { width: 2 },
          emphasis: { focus: 'series' }
        }
      ]
    : [
        {
          name: '访问量',
          type: 'bar',
          data: accessData.value.map(d => d.visits),
          yAxisIndex: 0,
          barMaxWidth: 20,
          itemStyle: { borderRadius: [4, 4, 0, 0] }
        },
        {
          name: '操作次数',
          type: 'bar',
          data: accessData.value.map(d => d.operations),
          yAxisIndex: 0,
          barMaxWidth: 20,
          itemStyle: { borderRadius: [4, 4, 0, 0] }
        },
        {
          name: '响应时间(ms)',
          type: 'bar',
          data: accessData.value.map(d => d.responseTime),
          yAxisIndex: 1,
          barMaxWidth: 20,
          itemStyle: { borderRadius: [4, 4, 0, 0] }
        }
      ]

  const option: EChartsOption = {
    color: ['#165DFF', '#00B42A', '#F53F3F'],
    tooltip: { trigger: 'axis', axisPointer: { type: 'line' } },
    legend: { bottom: 0 },
    grid: { top: 30, left: 40, right: 40, bottom: 40, containLabel: true },
    xAxis: {
      type: 'category',
      data: dates,
      axisTick: { show: false },
      axisLine: { lineStyle: { color: '#e5e7eb' } },
      axisLabel: { color: '#6b7280' }
    },
    yAxis: [
      {
        type: 'value',
        axisTick: { show: false },
        splitLine: { lineStyle: { type: 'dashed', color: '#e5e7eb' } },
        axisLabel: { color: '#6b7280' }
      },
      {
        type: 'value',
        position: 'right',
        axisTick: { show: false },
        splitLine: { show: false },
        axisLabel: { color: '#6b7280' }
      }
    ],
    series
  }
  return option
})

// ECharts：服务类型访问分布（横向百分比）
const serviceTypeOptions = computed<EChartsOption>(() => {
  const names = serviceTypeData.value.map(d => d.name)
  const values = serviceTypeData.value.map(d => d.percentage)
  const palette = serviceTypeData.value.map(d => d.color)
  const option: EChartsOption = {
    grid: { top: 10, left: 120, right: 20, bottom: 10, containLabel: true },
    xAxis: {
      type: 'value',
      max: 100,
      axisLabel: { formatter: '{value}%' },
      splitLine: { lineStyle: { type: 'dashed', color: '#e5e7eb' } }
    },
    yAxis: {
      type: 'category',
      data: names,
      axisTick: { show: false },
      axisLine: { show: false },
      axisLabel: { color: '#374151' }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: (params: any) => {
        const p = Array.isArray(params) ? params[0] : params
        return `${p.name}<br/>占比：${p.value}%`
      }
    },
    series: [
      {
        type: 'bar',
        data: values.map((v, i) => ({ value: v, itemStyle: { color: palette[i] } })),
        barMaxWidth: 18,
        itemStyle: { borderRadius: [0, 4, 4, 0] }
      }
    ]
  }
  return option
})
</script>