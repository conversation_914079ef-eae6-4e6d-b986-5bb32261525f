@echo off
title MySQL MCP Server - 阿里云镜像优化版
echo ===========================================
echo MySQL MCP Server 启动脚本（阿里云镜像优化）
echo ===========================================
echo.
echo 数据库连接信息:
echo   主机: *************
echo   端口: 3307
echo   数据库: MTNOH_AAA_Platform
echo   用户名: dtauser
echo.
echo 使用阿里云镜像源加速安装...

REM 设置uv镜像源环境变量
set UV_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/
set PIP_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/
set PIP_TRUSTED_HOST=mirrors.aliyun.com

REM 设置MySQL连接环境变量
set MYSQL_HOST=*************
set MYSQL_PORT=3307
set MYSQL_USER=dtauser
set MYSQL_PASSWORD=dtauser
set MYSQL_DATABASE=MTNOH_AAA_Platform

echo 启动 MySQL MCP Server...
REM 使用阿里云镜像源启动MCP Server
uvx --index-url https://mirrors.aliyun.com/pypi/simple/ mcp-server-mysql

if %errorlevel% neq 0 (
    echo.
    echo 启动失败，请检查：
    echo 1. 网络连接到 *************:3307
    echo 2. MySQL服务器是否运行
    echo 3. 用户名密码是否正确
    echo 4. MCP Server是否正确安装
    pause
)