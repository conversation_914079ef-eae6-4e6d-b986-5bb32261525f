using SqlSugar;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using MapStudio.Api.Services.Interfaces;
using MapStudio.Api.Models.DTOs;
using MapStudio.Api.Models.Entities;

namespace MapStudio.Api.Services.Implementations;

public class AuthService : IAuthService
{
    private readonly ISqlSugarClient _db;
    private readonly IConfiguration _configuration;
    private readonly ILogger<AuthService> _logger;

    public AuthService(ISqlSugarClient db, IConfiguration configuration, ILogger<AuthService> logger)
    {
        _db = db;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<LoginResponse> LoginAsync(LoginRequest request)
    {
        try
        {
            // 简化的登录验证 - 实际项目中应该验证密码哈希
            var user = await _db.Queryable<User>()
                .Where(u => u.Email == request.Email)
                .FirstAsync();

            if (user == null)
            {
                return new LoginResponse
                {
                    Success = false,
                    Message = "用户名或密码错误"
                };
            }

            // 生成JWT token
            var token = GenerateJwtToken(user);

            return new LoginResponse
            {
                Success = true,
                Token = token,
                User = new UserDto
                {
                    Id = user.Id,
                    Name = user.Name,
                    Email = user.Email,
                    Role = user.Role
                },
                Message = "登录成功"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "登录失败: {Email}", request.Email);
            return new LoginResponse
            {
                Success = false,
                Message = "登录失败，请重试"
            };
        }
    }

    public async Task<User?> GetUserByIdAsync(string userId)
    {
        return await _db.Queryable<User>()
            .Where(u => u.Id == userId)
            .FirstAsync();
    }

    public async Task<bool> ValidateTokenAsync(string token)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var jwtSettings = _configuration.GetSection("JwtSettings");
            var secretKey = Encoding.ASCII.GetBytes(jwtSettings["SecretKey"]);

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(secretKey),
                ValidateIssuer = true,
                ValidIssuer = jwtSettings["Issuer"],
                ValidateAudience = true,
                ValidAudience = jwtSettings["Audience"],
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };

            tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);
            return true;
        }
        catch
        {
            return false;
        }
    }

    public string GenerateJwtToken(User user)
    {
        var jwtSettings = _configuration.GetSection("JwtSettings");
        var secretKey = Encoding.ASCII.GetBytes(jwtSettings["SecretKey"]);

        var claims = new List<Claim>
        {
            new Claim(ClaimTypes.NameIdentifier, user.Id),
            new Claim(ClaimTypes.Name, user.Name),
            new Claim(ClaimTypes.Email, user.Email),
            new Claim(ClaimTypes.Role, user.Role),
            new Claim("UserId", user.Id)

        };

        // 添加租户信息到JWT claims
        if (!string.IsNullOrEmpty(user.TenantId))
        {
            claims.Add(new Claim("TenantId", user.TenantId));
        }
        else
        {
            // 如果没有租户ID，设置为SYSTEM
            claims.Add(new Claim("TenantId", "SYSTEM"));
        }

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddMinutes(int.Parse(jwtSettings["ExpirationInMinutes"])),
            Issuer = jwtSettings["Issuer"],
            Audience = jwtSettings["Audience"],
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(secretKey), SecurityAlgorithms.HmacSha256Signature)
        };

        var tokenHandler = new JwtSecurityTokenHandler();
        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }

    public async Task LogoutAsync()
    {
        // JWT 无状态，logout主要在客户端清除token
        await Task.CompletedTask;
    }

    public async Task<UserProfileResponse> GetUserProfileAsync(string userId)
    {
        var user = await GetUserByIdAsync(userId);
        if (user == null)
        {
            throw new ArgumentException("用户不存在");
        }

        return new UserProfileResponse
        {
            Id = user.Id,
            Name = user.Name,
            Email = user.Email,
            Role = user.Role
        };
    }
}