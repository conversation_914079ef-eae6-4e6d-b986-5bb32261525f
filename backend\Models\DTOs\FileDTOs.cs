namespace MapStudio.Api.Models.DTOs;

public class FileUploadResponse
{
    public string FileId { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string FileType { get; set; } = string.Empty;
    public DateTime UploadTime { get; set; }
    public string DownloadUrl { get; set; } = string.Empty;
}

public class FilePreviewResponse
{
    public string FileId { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public string PreviewUrl { get; set; } = string.Empty;
}

// 元数据选项类
public class ServiceTypeOption
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
}

public class PermissionScopeOption
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Permissions { get; set; } = string.Empty;
}

public class AuthPeriodOption
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
}

public class TenantSizeOption
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
}

public class IndustryOption
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
}

// 告警设置相关
public class AlarmSettingsResponse
{
    public string Id { get; set; } = string.Empty;
    public string EmailTemplate { get; set; } = string.Empty;
    public List<AlarmEmailDto> EmailList { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class AlarmEmailDto
{
    public string Id { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public bool Enabled { get; set; }
    public List<string> Levels { get; set; } = new();
}

public class AlarmTemplateUpdateResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime UpdatedAt { get; set; }
}

public class AlarmEmailResponse
{
    public string Id { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public bool Enabled { get; set; }
    public List<string> Levels { get; set; } = new();
    public string Message { get; set; } = string.Empty;
}

public class AlarmEmailUpdateResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
}

// 服务状态相关
public class TenantServiceStatusResponse
{
    public List<TenantServiceStatusDto> Services { get; set; } = new();
}

public class TenantServiceStatusDto
{
    public string TenantId { get; set; } = string.Empty;
    public string TenantName { get; set; } = string.Empty;
    public string ServiceType { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string StatusName { get; set; } = string.Empty;
    public DateTime? ExpiryDate { get; set; }
    public int RemainingDays { get; set; }
}
