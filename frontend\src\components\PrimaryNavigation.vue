<template>
  <aside 
    :class="sidebarClasses"
  >
    <!-- 侧边栏头部 -->
    <div class="flex items-center justify-between h-16 px-4 border-b border-gray-200">
      <div :class="logoContainerClasses">
        <i class="fa-solid fa-map-marked-alt text-blue-600 text-xl"></i>
        <span v-if="sidebarOpen" class="ml-2 font-bold text-gray-900">地图工作室</span>
      </div>
      <button 
        v-if="sidebarOpen"
        @click="handleToggleSidebar"
        class="p-1 rounded-md text-gray-500 hover:text-gray-900 hover:bg-gray-100"
      >
        <i class="fa-solid fa-angle-left"></i>
      </button>
    </div>

    <!-- 导航菜单 -->
    <nav class="p-4 space-y-1">
      <div 
        v-for="item in navItems" 
        :key="item.title" 
        class="mb-2"
      >
        <!-- 一级菜单按钮 -->
        <button
          @click="() => handleToggleMenu(item.title)"
          class="flex items-center w-full px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-100 transition-colors duration-200"
        >
          <i :class="getMenuIconClasses(item.icon)"></i>
          <template v-if="sidebarOpen">
            <span class="ml-3">{{ item.title }}</span>
            <i :class="getExpandIconClasses(item.title)"></i>
          </template>
        </button>

        <!-- 二级子菜单 -->
        <div 
          v-if="expandedMenus.includes(item.title) && sidebarOpen" 
          class="mt-1 pl-10 space-y-1"
        >
          <router-link
            v-for="child in item.children"
            :key="child.title"
            :to="child.path"
            :class="getSubmenuItemClasses(child.path)"
          >
            {{ child.title }}
          </router-link>
        </div>
      </div>
    </nav>
  </aside>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import { cn } from '@/utils/styleUtils';

// 导航菜单数据 - 修复路由配置，与实际路由保持一致
const navItems = [
  {
    title: '租户管理',
    icon: 'fa-building',
    children: [
      { title: '租户授权申请', path: '/tenant/auth-apply' },
      { title: '待审批清单', path: '/tenant/auth-approval-list' },
      { title: '授权进度查询', path: '/tenant/auth-progress' },
      { title: '授权销毁', path: '/tenant/auth-destroy' },
      { title: '服务状态查询', path: '/tenant/service-status' },
    ],
  },
  {
    title: '运营管理',
    icon: 'fa-line-chart',
    children: [
      { title: '运营日志清单', path: '/operation/log-list' },
      { title: '租户操作排行', path: '/operation/tenant-action-rank' },
      { title: '操作类型排行', path: '/operation/action-type-rank' },
      { title: '用户使用量查询', path: '/operation/user-usage' },
      { title: '告警设置', path: '/operation/alarm-setting' },
      { title: '访问统计报表', path: '/operation/access-stats' },
      { title: '日操作统计', path: '/operation/daily-stats' },
      { title: '月操作统计', path: '/operation/monthly-stats' },
    ],
  },
];

// Props 定义 - 与React版本的state对应
interface Props {
  sidebarOpen?: boolean;
  expandedMenus?: string[];
}

const props = withDefaults(defineProps<Props>(), {
  sidebarOpen: true,
  expandedMenus: () => ['租户管理', '运营管理']
});

// Emits 定义 - 与React版本的事件处理对应
const emit = defineEmits<{
  'toggle-sidebar': [];
  'toggle-menu': [title: string];
  'menu-item-click': [path: string];
}>();

const route = useRoute();

// 计算属性 - 复刻React版本的className逻辑
const sidebarClasses = computed(() => 
  cn(
    "bg-white border-r border-gray-200 transition-all duration-300 ease-in-out",
    props.sidebarOpen ? "w-64" : "w-20"
  )
);

const logoContainerClasses = computed(() => 
  cn(
    "flex items-center",
    !props.sidebarOpen && "justify-center w-full"
  )
);

// 方法定义 - 与React版本的函数对应
const getMenuIconClasses = (icon: string) => 
  cn(
    `fa-solid ${icon}`,
    !props.sidebarOpen && 'mx-auto'
  );

const getExpandIconClasses = (title: string) => 
  cn(
    'fa-solid ml-auto',
    props.expandedMenus.includes(title) ? 'fa-angle-down' : 'fa-angle-right'
  );

const getSubmenuItemClasses = (path: string) => 
  cn(
    "block px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200",
    route.path === path
      ? "text-blue-600 bg-blue-50"
      : "text-gray-600 hover:text-blue-600 hover:bg-blue-50"
  );

// 事件处理函数 - 与React版本的处理逻辑一致
const handleToggleSidebar = () => {
  emit('toggle-sidebar');
};

const handleToggleMenu = (title: string) => {
  emit('toggle-menu', title);
};

// 暴露组件实例方法
defineExpose({
  navItems
});
</script>

<style scoped>
/* 与React版本完全一致的样式 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬停效果 - 与React版本的hover:bg-gray-100一致 */
button:hover {
  background-color: #f3f4f6;
}

/* 子菜单项悬停效果 */
a:hover {
  background-color: #dbeafe !important;
  color: #2563eb !important;
}

/* 激活状态样式 */
.text-blue-600 {
  color: #2563eb;
}

.bg-blue-50 {
  background-color: #eff6ff;
}

/* 响应式设计 - 与React版本保持一致 */
@media (max-width: 768px) {
  aside {
    position: fixed;
    z-index: 40;
    height: 100vh;
  }
}

/* 焦点样式 */
button:focus,
a:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 过渡动画优化 */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}
</style>