{"ConnectionStrings": {"DefaultConnection": "Server=YOUR_PRODUCTION_SERVER;Port=3306;Database=MapStudio_Production;Uid=mapstudio_user;Pwd=YOUR_SECURE_PASSWORD;charset=utf8mb4;"}, "JwtSettings": {"Enabled": true, "SecretKey": "YOUR_PRODUCTION_SECRET_KEY_MUST_BE_AT_LEAST_256_BITS_LONG_FOR_SECURITY", "Issuer": "MapStudio.Api.Production", "Audience": "MapStudio.Frontend.Production", "ExpirationInMinutes": 60}, "FileStorage": {"BasePath": "/app/uploads", "MaxFileSize": 10485760, "AllowedExtensions": [".pdf", ".jpg", ".jpeg", ".png", ".doc", ".docx"]}, "Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Error", "Microsoft.EntityFrameworkCore": "Error"}}, "AllowedHosts": "*", "CorsSettings": {"AllowedOrigins": ["https://your-production-domain.com"]}}