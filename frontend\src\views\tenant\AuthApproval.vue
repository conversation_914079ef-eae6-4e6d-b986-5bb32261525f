<template>
  <div class="p-6 space-y-6">
    <!-- 页面标题和返回按钮 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">租户授权审批</h1>
        <p class="mt-1 text-gray-500">审批租户提交的授权申请</p>
      </div>
      <button
        @click="handleGoBack"
        class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        <i class="fa-solid fa-arrow-left mr-2"></i>
        返回列表
      </button>
    </div>

    <!-- 申请基本信息卡片 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div class="flex flex-col md:flex-row md:items-start md:justify-between mb-6">
        <div>
          <h2 class="text-lg font-semibold text-gray-900">申请基本信息</h2>
          <p class="mt-1 text-sm text-gray-500">申请单号: {{ applicationId }}</p>
        </div>
        <div class="mt-4 md:mt-0">
          <span :class="getStatusBadgeClass(applicationDetails.status)">
            {{ getStatusText(applicationDetails.status) }}
          </span>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <h3 class="text-sm font-medium text-gray-500 mb-3">申请时间</h3>
          <p class="text-base text-gray-900">{{ applicationDetails.applyTime }}</p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500 mb-3">权限类型</h3>
          <p class="text-base text-gray-900">{{ applicationDetails.serviceInfo.typeName }}</p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500 mb-3">过期时间</h3>
          <p class="text-base text-gray-900">{{ applicationDetails.serviceInfo.authPeriodName }}</p>
        </div>
      </div>
    </div>

    <!-- 租户信息卡片 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
        <i class="fa-solid fa-building-circle-info text-blue-600 mr-2"></i>
        租户基本信息
      </h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-x-12 gap-y-6">
        <div>
          <h3 class="text-sm font-medium text-gray-500 mb-1">租户名称</h3>
          <p class="text-base text-gray-900">{{ applicationDetails.tenantInfo.name }}</p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500 mb-1">租户ID</h3>
          <p class="text-base text-gray-900">{{ applicationDetails.tenantInfo.id }}</p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500 mb-1">联系人</h3>
          <p class="text-base text-gray-900">{{ applicationDetails.tenantInfo.contactPerson }}</p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500 mb-1">联系电话</h3>
          <p class="text-base text-gray-900">{{ applicationDetails.tenantInfo.contactPhone }}</p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500 mb-1">联系邮箱</h3>
          <p class="text-base text-gray-900">{{ applicationDetails.tenantInfo.contactEmail }}</p>
        </div>
        <!-- <div>
          <h3 class="text-sm font-medium text-gray-500 mb-1">公司地址</h3>
          <p class="text-base text-gray-900">{{ applicationDetails.tenantInfo.address }}</p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500 mb-1">所属行业</h3>
          <p class="text-base text-gray-900">{{ applicationDetails.tenantInfo.industry }}</p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500 mb-1">公司规模</h3>
          <p class="text-base text-gray-900">{{ applicationDetails.tenantInfo.scale }}</p>
        </div> -->
      </div>
    </div>

    <!-- 服务申请详情卡片 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
        <i class="fa-solid fa-file-circle-plus text-blue-600 mr-2"></i>
        服务申请详情
      </h2>
      
      <div class="space-y-6">
        <div>
          <h3 class="text-sm font-medium text-gray-500 mb-1">权限类型</h3>
          <p class="text-base text-gray-900">{{ applicationDetails.serviceInfo.typeName }}</p>
        </div>
        
        <div>
          <h3 class="text-sm font-medium text-gray-500 mb-1">过期时间</h3>
          <p class="text-base text-gray-900">{{ applicationDetails.serviceInfo.authPeriodName }}</p>
        </div>
        
        <div>
          <h3 class="text-sm font-medium text-gray-500 mb-1">权限等级</h3>
          <p class="text-base text-gray-900">{{ applicationDetails.serviceInfo.permissionScopeName }}</p>
        </div>
        
        <div>
          <h3 class="text-sm font-medium text-gray-500 mb-1">申请描述</h3>
          <div class="mt-1 p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <p class="text-base text-gray-900 whitespace-pre-line">{{ applicationDetails.serviceInfo.description }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 资质文件卡片 -->
    <!-- <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
        <i class="fa-solid fa-file-certificate text-blue-600 mr-2"></i>
        资质文件
      </h2>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                文件名称
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                文件类型
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                文件大小
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                上传时间
              </th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr 
              v-for="doc in applicationDetails.documents" 
              :key="doc.id" 
              class="hover:bg-gray-50"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <i class="fa-solid fa-file-pdf text-red-500 mr-3"></i>
                  <div class="text-sm font-medium text-gray-900 truncate max-w-xs">{{ doc.name }}</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">
                  {{ doc.typeName }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ doc.size }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ doc.uploadTime }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button
                  @click="handleFilePreview(doc)"
                  class="text-blue-600 hover:text-blue-900"
                >
                  预览
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div> -->

    <!-- 审批历史卡片 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
        <i class="fa-solid fa-history text-blue-600 mr-2"></i>
        审批历史
      </h2>
      
      <div class="relative">
        <!-- 时间线 -->
        <div class="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>
        
        <div class="space-y-8">
          <div 
            v-for="(history, index) in applicationDetails.approvalHistory" 
            :key="history.step" 
            class="relative pl-12"
          >
            <!-- 时间点 -->
            <div class="absolute left-0 w-8 h-8 rounded-full bg-blue-100 border-2 border-blue-500 flex items-center justify-center">
              <i 
                :class="index === 0 ? 'fa-solid fa-paper-plane text-blue-600' : 'fa-solid fa-check text-blue-600'"
              ></i>
            </div>
            
            <!-- 内容 -->
            <div>
              <div class="flex items-center justify-between">
                <h3 class="text-sm font-medium text-gray-900">{{ history.stepName }}</h3>
                <p class="text-sm text-gray-500">{{ history.time }}</p>
              </div>
              <p class="mt-1 text-sm text-gray-500">操作人: {{ history.operator }}</p>
              <div 
                v-if="history.comment" 
                class="mt-2 p-3 bg-gray-50 border border-gray-200 rounded-lg"
              >
                <p class="text-sm text-gray-900">{{ history.comment }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 审批操作卡片 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <i class="fa-solid fa-gavel text-blue-600 mr-2"></i>
        审批操作
      </h2>
      
      <div class="space-y-4">
        <div>
          <label for="approvalComment" class="block text-sm font-medium text-gray-700">
            审批意见 <span class="text-red-500">*</span>
          </label>
          <textarea
            id="approvalComment"
            v-model="approvalComment"
            rows="4"
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            placeholder="请输入审批意见..."
          ></textarea>
          <p class="mt-1 text-xs text-gray-500">驳回申请时必须填写驳回理由</p>
        </div>
        
        <div class="flex justify-end space-x-4 pt-4 border-t border-gray-200">
          <button
            @click="handleApproval('reject')"
            :disabled="isProcessing"
            class="px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <template v-if="isProcessing">
              <i class="fa-solid fa-spinner fa-spin mr-2"></i>
              处理中...
            </template>
            <template v-else>
              <i class="fa-solid fa-times mr-2"></i>
              驳回
            </template>
          </button>
          <button
            @click="handleApproval('approve')"
            :disabled="isProcessing"
            class="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <template v-if="isProcessing">
              <i class="fa-solid fa-spinner fa-spin mr-2"></i>
              处理中...
            </template>
            <template v-else>
              <i class="fa-solid fa-check mr-2"></i>
              通过
            </template>
          </button>
        </div>
      </div>
    </div>

    <!-- 文件预览模态框 -->
    <Teleport to="body">
      <div 
        v-if="previewFile" 
        class="fixed inset-0 z-50 overflow-y-auto" 
        aria-labelledby="modal-title" 
        role="dialog" 
        aria-modal="true"
      >
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          <!-- 背景遮罩 -->
          <div 
            class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" 
            @click="closeFilePreview"
          ></div>

          <!-- 模态框内容 -->
          <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
          
          <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div class="sm:flex sm:items-start">
                <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                  <div class="flex justify-between items-start mb-4">
                    <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                      文件预览: {{ previewFile.name }}
                    </h3>
                    <button
                      @click="closeFilePreview"
                      class="text-gray-400 hover:text-gray-500"
                    >
                      <i class="fa-solid fa-times text-xl"></i>
                    </button>
                  </div>
                  <div class="mt-4 bg-gray-100 rounded-lg p-4 flex items-center justify-center min-h-[400px]">
                    <img
                      :src="previewFile.url"
                      :alt="previewFile.name"
                      class="max-w-full max-h-[500px] object-contain"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="button"
                @click="closeFilePreview"
                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
              >
                关闭预览
              </button>
              <button
                type="button"
                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              >
                <i class="fa-solid fa-download mr-2"></i>
                下载文件
              </button>
            </div>
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useToast } from '@/composables/useToast';
import { TenantService } from '@/services';
// 导入类型定义
import type { AuthApplicationDetail } from '@/services/types';
// 路由和导航
const route = useRoute();
const router = useRouter();
const toast = useToast();

// 获取申请ID
const applicationId = computed(() => route.params.id as string);

// 响应式状态 - 与React版本完全对应
const approvalComment = ref('');
const isProcessing = ref(false);
const applicationDetails = ref({
  id: '',
  applicationId: '',
  status: '',
  applyTime: '',
  serviceInfo: {
    typeName: '',
    authPeriodName: '',
    permissionScopeName: '',
    description: '',
  },
  tenantInfo: {
    name: '',
    id: '',
    contactPerson: '',
    contactPhone: '',
    contactEmail: '',
  },
  documents: [],
  approvalHistory: [],
} as AuthApplicationDetail);


// 申请详情数据 - 从API获取
console.log('当前申请详情id:', route.params.id as string);

const loadApplicationDetails  = async () => {

    try {
    const result = await TenantService.getAuthApplicationDetail(route.params.id as string);
    applicationDetails.value = result; // 使用 .value 来修改 ref 的值
  } catch (error) {
    console.error('获取申请详情失败:', error);
  }
};
console.log('✅ 申请提交成功:', applicationDetails);
const previewFile = ref<typeof applicationDetails.documents[0] | null>(null); 


// 模拟申请详情数据 - 与React版本完全一致
// const mockApplicationDetails = {
//   id: 'TA-20250801',
//   applyTime: '2025-08-01 09:23:45',
//   status: 'pending',
//   tenantInfo: {
//     name: '智慧城市科技有限公司',
//     id: 'TENANT-2025001',
//     contactPerson: '张三',
//     contactEmail: '<EMAIL>',
//     contactPhone: '13800138000',
//     address: '北京市海淀区中关村科技园区8号楼',
//     industry: '信息技术',
//     scale: '500-1000人',
//   },
//   serviceInfo: {
//     type: 'enterprise',
//     typeName: '企业级地图服务',
//     authPeriod: '1year',
//     authPeriodName: '1年',
//     permissionScope: 'admin',
//     permissionScopeName: '管理员权限',
//     description: '为支持我司智慧城市项目建设，需要接入地图工作室企业级地图服务，用于城市交通流量分析和城市规划决策支持系统。',
//   },
//   documents: [
//     {
//       id: 'doc-1',
//       name: '企业营业执照.pdf',
//       type: 'businessLicense',
//       typeName: '企业营业执照',
//       size: '2.4 MB',
//       uploadTime: '2025-08-01 09:15:22',
//       url: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=business%20license%2C%20official%20document%2C%20company%20certificate&sign=ae093cfedc803d385895e3650cac460a',
//     },
//     {
//       id: 'doc-2',
//       name: '组织机构代码证.pdf',
//       type: 'organizationCode',
//       typeName: '组织机构代码证',
//       size: '1.8 MB',
//       uploadTime: '2025-08-01 09:18:45',
//       url: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=organization%20code%20certificate%2C%20official%20document&sign=40c663eb47537146e86050b6b65213e3',
//     },
//     {
//       id: 'doc-3',
//       name: '项目合作意向书.pdf',
//       type: 'other',
//       typeName: '其他附件',
//       size: '3.2 MB',
//       uploadTime: '2025-08-01 09:20:11',
//       url: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=project%20cooperation%20agreement%2C%20official%20document&sign=1a89cf2979019cbdbfeb6db09706184f',
//     },
//   ],
//   approvalHistory: [
//     {
//       step: 'submit',
//       stepName: '申请提交',
//       time: '2025-08-01 09:23:45',
//       operator: '张三 (智慧城市科技有限公司)',
//       comment: '提交租户授权申请',
//     },
//     {
//       step: 'receive',
//       stepName: '申请接收',
//       time: '2025-08-01 09:30:12',
//       operator: '系统自动处理',
//       comment: '系统已接收申请，等待管理员审批',
//     },
//   ],
// };

// const applicationDetails = ref(mockApplicationDetails);

// 方法定义 - 与React版本完全对应
const handleGoBack = () => {
  router.push('/tenant/auth-approval-list');
};

const handleApproval = (action: 'approve' | 'reject') => {
  if (action === 'reject' && !approvalComment.value.trim()) {
    toast.error('请输入驳回理由', {
      description: '驳回申请时必须填写驳回理由'
    });
    return;
  }
  
  isProcessing.value = true;
  

  const approvalRequest = {
    action,
    comments: approvalComment.value.trim()
  };
  TenantService.approveAuthApplication(applicationId.value, approvalRequest);
  // 模拟API请求延迟 - 与React版本一致
  setTimeout(() => {
    isProcessing.value = false;
    
    if (action === 'approve') {
      toast.success('审批通过', {
        description: `申请单 ${applicationId.value} 已成功审批通过。`
      });
    } else {
      toast.success('已驳回', {
        description: `申请单 ${applicationId.value} 已成功驳回。`
      });
    }
    
    // 返回列表页
    router.push('/tenant/auth-approval-list');
  }, 1500);
};

const handleFilePreview = (doc: typeof applicationDetails.documents[0]) => {
  previewFile.value = doc;
};

const closeFilePreview = () => {
  previewFile.value = null;
};

// 获取状态样式 - 与React版本完全一致
const getStatusBadgeClass = (status: string) => {
  const baseClasses = 'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium';
  switch (status) {
    case 'pending':
      return `${baseClasses} bg-yellow-100 text-yellow-800`;
    case 'approved':
      return `${baseClasses} bg-green-100 text-green-800`;
    case 'rejected':
      return `${baseClasses} bg-red-100 text-red-800`;
    case 'processing':
      return `${baseClasses} bg-blue-100 text-blue-800`;
    default:
      return `${baseClasses} bg-gray-100 text-gray-800`;
  }
};

// 获取状态文本 - 与React版本完全一致
const getStatusText = (status: string) => {
  switch (status) {
    case 'pending':
      return '待审批';
    case 'approved':
      return '已通过';
    case 'rejected':
      return '已驳回';
    case 'processing':
      return '审批中';
    default:
      return '未知状态';
  }
};

// 组件挂载时的逻辑
onMounted(() => {
  // 这里可以根据路由参数加载具体的申请详情
  console.log('Loading application details for ID:', applicationId.value);
   // 调用加载申请详情的函数
  loadApplicationDetails();
});
</script>

<style scoped>
/* 与React版本完全一致的样式 */
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transform {
  transform: translateZ(0);
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* 悬停效果 */
button:hover,
.hover\:bg-gray-50:hover {
  background-color: #f9fafb;
}

.hover\:bg-gray-500:hover {
  background-color: #6b7280;
}

.hover\:bg-blue-700:hover {
  background-color: #1d4ed8;
}

.hover\:bg-green-700:hover {
  background-color: #15803d;
}

/* 焦点样式 */
button:focus,
textarea:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 禁用状态 */
.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-1.md\:grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .grid-cols-1.md\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

/* 表格样式 */
.divide-y > :not([hidden]) ~ :not([hidden]) {
  border-top-width: 1px;
  border-color: #e5e7eb;
}

.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  border-color: #e5e7eb;
}

/* 时间线样式 */
.absolute.left-4.top-0.bottom-0.w-0\.5.bg-gray-200 {
  left: 1rem;
  top: 0;
  bottom: 0;
  width: 0.125rem;
  background-color: #e5e7eb;
}
</style>