using MapStudio.Api.Attributes;
using MapStudio.Api.Models.DTOs;
using MapStudio.Api.Models.Entities;
using MapStudio.Api.Models.ViewModels;
using MapStudio.Api.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace MapStudio.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[ConditionalAuthorize]
public class OperationController : ControllerBase
{
    private readonly IOperationService _operationService;
    private readonly ILogger<OperationController> _logger;

    public OperationController(IOperationService operationService, ILogger<OperationController> logger)
    {
        _operationService = operationService;
        _logger = logger;
    }

    /// <summary>
    /// 获取日统计数据
    /// </summary>
    [HttpGet("daily-stats")]
    public async Task<ActionResult<ApiResponse<DailyStatsResponse>>> GetDailyStats(
        [FromQuery] string? selectedTenant = null,
        [FromQuery] string? selectedServiceType = null,
        [FromQuery] string? selectedSize = null,
        [FromQuery] string? viewMode = null)
    {
        try
        {
            var result = await _operationService.GetDailyStatsAsync(selectedTenant, selectedServiceType, selectedSize, viewMode);
            return Ok(ApiResponse<DailyStatsResponse>.SuccessResult(result, "获取日统计成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取日统计失败");
            return StatusCode(500, ApiResponse<DailyStatsResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取月统计数据
    /// </summary>
    [HttpGet("monthly-stats")]
    public async Task<ActionResult<ApiResponse<MonthlyStatsResponse>>> GetMonthlyStats(
        [FromQuery] string? selectedTenant = null,
        [FromQuery] string? comparisonType = null)
    {
        try
        {
            var result = await _operationService.GetMonthlyStatsAsync(selectedTenant, comparisonType);
            return Ok(ApiResponse<MonthlyStatsResponse>.SuccessResult(result, "获取月统计成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取月统计失败");
            return StatusCode(500, ApiResponse<MonthlyStatsResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取访问统计
    /// </summary>
    [HttpGet("access-stats")]
    public async Task<ActionResult<ApiResponse<AccessStatsResponse>>> GetAccessStats(
        [FromQuery] string? timeRange = null,
        [FromQuery] string? chartType = null)
    {
        try
        {
            var result = await _operationService.GetAccessStatsAsync(timeRange, chartType);
            return Ok(ApiResponse<AccessStatsResponse>.SuccessResult(result, "获取访问统计成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取访问统计失败");
            return StatusCode(500, ApiResponse<AccessStatsResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取操作类型排行
    /// </summary>
    [HttpGet("action-type-rank")]
    public async Task<ActionResult<ApiResponse<ActionTypeRankResponse>>> GetActionTypeRank(
        [FromQuery] string selectedOperationType,
        [FromQuery] string? tenantSize = null,
        [FromQuery] string? industry = null,
        [FromQuery] string? timeRange = null)
    {
        try
        {
            var result = await _operationService.GetActionTypeRankAsync(selectedOperationType, tenantSize, industry, timeRange);
            return Ok(ApiResponse<ActionTypeRankResponse>.SuccessResult(result, "获取操作类型排行成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取操作类型排行失败");
            return StatusCode(500, ApiResponse<ActionTypeRankResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取租户操作排行
    /// </summary>
    [HttpGet("tenant-action-rank")]
    public async Task<ActionResult<ApiResponse<TenantActionRankResponse>>> GetTenantActionRank()
    {
        try
        {
            var result = await _operationService.GetTenantActionRankAsync();
            return Ok(ApiResponse<TenantActionRankResponse>.SuccessResult(result, "获取租户排行成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取租户排行失败");
            return StatusCode(500, ApiResponse<TenantActionRankResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取用户使用情况
    /// </summary>
    [HttpGet("user-usage")]
    public async Task<ActionResult<ApiResponse<UserUsageResponse>>> GetUserUsage(
        [FromQuery] string? selectedTenant = null,
        [FromQuery] string? timeRange = null,
        [FromQuery] string? timeGranularity = null)
    {
        try
        {
            var result = await _operationService.GetUserUsageAsync(selectedTenant, timeRange, timeGranularity);
            return Ok(ApiResponse<UserUsageResponse>.SuccessResult(result, "获取用户使用情况成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户使用情况失败");
            return StatusCode(500, ApiResponse<UserUsageResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取操作日志
    /// </summary>
    [HttpGet("logs")]
    public async Task<ActionResult<ApiResponse<OperationLogListResponse>>> GetOperationLogs(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string? tenantName = null,
        [FromQuery] string? actionType = null,
        [FromQuery] string? userName = null,
        [FromQuery] string? operationDate = null)
    {
        try
        {
            var result = await _operationService.GetOperationLogsAsync(page, pageSize, tenantName, actionType, userName, operationDate);
            return Ok(ApiResponse<OperationLogListResponse>.SuccessResult(result, "获取操作日志成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取操作日志失败");
            return StatusCode(500, ApiResponse<OperationLogListResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 埋点插入操作日志
    /// </summary>
    [HttpPost("addOperationlogs")]
    public async Task<ActionResult<ApiResponse>> AddOperationLogs(
        OperationLog operationLog)
    {
        try
        {
            if(operationLog == null || operationLog.TenantId == null)
            {
                return StatusCode(500, ApiResponse<OperationLogListResponse>.ErrorResult("插入日志信息为空"));
            }

            await _operationService.LogOperationAsync(operationLog.TenantId, operationLog.UserId, operationLog.ActionType,operationLog.Description);
            return Ok(ApiResponse<OperationLog>.SuccessResult(operationLog, "插入操作日志成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "插入操作日志失败");
            return StatusCode(500, ApiResponse<OperationLogListResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取日志详情
    /// </summary>
    [HttpGet("logs/{id}")]
    public async Task<ActionResult<ApiResponse<OperationLogListResponse>>> GetOperationLogs(string id)
    {
        try
        {
            var result = await _operationService.GetOperationLogByIdAsync(id);
            return Ok(ApiResponse<OperationLog>.SuccessResult(result, "获取日志详情成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取日志详情失败");
            return StatusCode(500, ApiResponse<OperationLogListResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取告警设置
    /// </summary>
    [HttpGet("alarm-settings")]
    public async Task<ActionResult<ApiResponse<AlarmSettingsResponse>>> GetAlarmSettings()
    {
        try
        {
            var result = await _operationService.GetAlarmSettingsAsync();
            return Ok(ApiResponse<AlarmSettingsResponse>.SuccessResult(result, "获取告警设置成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取告警设置失败");
            return StatusCode(500, ApiResponse<AlarmSettingsResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 更新告警模板
    /// </summary>
    [HttpPut("alarm-settings/template")]
    public async Task<ActionResult<ApiResponse<AlarmTemplateUpdateResponse>>> UpdateAlarmTemplate([FromBody] string emailTemplate)
    {
        try
        {
            var result = await _operationService.UpdateAlarmTemplateAsync(emailTemplate);
            return Ok(ApiResponse<AlarmTemplateUpdateResponse>.SuccessResult(result, "更新告警模板成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新告警模板失败");
            return StatusCode(500, ApiResponse<AlarmTemplateUpdateResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }
    /// <summary>
    /// 获取服务状态统计数据
    /// </summary>
    /// <remarks>
    /// 该接口返回服务状态统计数据，包含四个关键指标：
    ///
    /// 返回数据包含：
    /// - TotalUsers: 总用户数，统计ms_auth_applications表的数据量
    /// - ActiveUsers: 活跃用户，统计ms_operation_logs表的数据，按TenantId字段去重，统计数据量
    /// - TodayVisits: 今日访问量，统计ms_operation_logs表的数据，查询当天的总数据量
    /// - PendingApplications: 待审批申请，统计ms_auth_applications表的数据，按照status=Pending过滤，统计总数据量
    /// </remarks>
    [HttpGet("service-status-stats")]
    public async Task<ActionResult<ApiResponse<ServiceStatusStatsDto>>> GetServiceStatusStats()
    {
        try
        {
            var result = await _operationService.GetServiceStatusStatsAsync();
            return Ok(ApiResponse<ServiceStatusStatsDto>.SuccessResult(result, "获取服务状态统计数据成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取服务状态统计数据失败");
            return StatusCode(500, ApiResponse<ServiceStatusStatsDto>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取近6个月使用率统计数据
    /// </summary>
    /// <remarks>
    /// 该接口返回近6个月的使用率统计数据，横坐标为当前月份往前推5个月（包括本月），纵坐标为各月份的操作总量。
    /// 数据包括每个月的操作量、活跃租户数，以及近6个月的总操作量、平均月操作量和增长率。
    /// </remarks>
    [HttpGet("six-month-usage-stats")]
    public async Task<ActionResult<ApiResponse<SixMonthUsageStatsResponseDto>>> GetSixMonthUsageStats()
    {
        try
        {
            var result = await _operationService.GetSixMonthUsageStatsAsync();
            return Ok(ApiResponse<SixMonthUsageStatsResponseDto>.SuccessResult(result, "获取近6个月使用率统计数据成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取近6个月使用率统计数据失败");
            return StatusCode(500, ApiResponse<SixMonthUsageStatsResponseDto>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取运行状况统计数据
    /// </summary>
    /// <remarks>
    /// 该接口返回运行状况统计数据，通过分析ms_operation_logs表中的Description字段，
    /// 提取HTTP状态码并统计各个状态码的出现次数和百分比。
    ///
    /// 返回数据包含：
    /// - TotalLogs: ms_operation_logs表的总数据量
    /// - StatusCodeStats: 各个状态码的统计数据列表，包含状态码、状态名称、出现次数和百分比
    /// - LastUpdated: 数据最后更新时间
    /// </remarks>
    [HttpGet("service-status-code-stats")]
    public async Task<ActionResult<ApiResponse<ServiceStatusStatsResponseDto>>> GetServiceStatusCodeStats()
    {
        try
        {
            var result = await _operationService.GetServiceStatusCodeStatsAsync();
            return Ok(ApiResponse<ServiceStatusStatsResponseDto>.SuccessResult(result, "获取运行状况统计数据成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取运行状况统计数据失败");
            return StatusCode(500, ApiResponse<ServiceStatusStatsResponseDto>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取所有租户信息
    /// </summary>
    /// <remarks>
    /// 该接口返回所有租户的基本信息，包括租户ID和租户名称。
    /// 数据来源于ms_auth_applications表。
    /// </remarks>
    [HttpGet("tenants")]
    public async Task<ActionResult<ApiResponse<List<TenantDataDto>>>> GetAllTenants()
    {
        try
        {
            var result = await _operationService.GetAllTenantsAsync();
            return Ok(ApiResponse<List<TenantDataDto>>.SuccessResult(result, "获取所有租户信息成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有租户信息失败");
            return StatusCode(500, ApiResponse<List<TenantDataDto>>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取日统计数据
    /// </summary>
    /// <remarks>
    /// 该接口返回日统计数据，包含四个关键指标：
    ///
    /// 返回数据包含：
    /// - TotalTenants: 租户总数，统计ms_auth_applications表的数据量
    /// - AverageDailyOperations: 平均日操作次数，按天汇聚统计操作数，然后求平均值
    /// - MaxDailyOperations: 最大日操作数，按天汇聚统计，找出最大值
    /// - TotalOperations: 操作总量，统计ms_operation_logs表当天的总数据量
    /// </remarks>
    [HttpGet("daily-stats/summary")]
    public async Task<ActionResult<DailyStatsDTO>> GetDailyStats(
        [FromQuery] string? tenantName = null)
    {
        try
        {
            var result = await _operationService.GetDailyStatsAsync(tenantName);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取日统计数据失败");
            return StatusCode(500, "服务器内部错误");
        }
    }
}