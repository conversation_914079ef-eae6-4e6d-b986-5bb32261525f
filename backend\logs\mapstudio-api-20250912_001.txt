2025-09-12 11:57:08.249 +08:00 [INF] 开始初始化数据库...
2025-09-12 11:57:14.202 +08:00 [INF] 数据库表创建完成
2025-09-12 11:57:14.683 +08:00 [INF] 数据库初始化完成
2025-09-12 11:57:14.707 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-12 11:57:14.845 +08:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5172: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
 ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-09-12 14:18:49.701 +08:00 [INF] 开始初始化数据库...
2025-09-12 14:18:55.748 +08:00 [INF] 数据库表创建完成
2025-09-12 14:18:56.198 +08:00 [INF] 数据库初始化完成
2025-09-12 14:18:56.217 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-12 14:18:56.428 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-12 14:18:56.429 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-12 14:18:56.484 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-12 14:18:56.486 +08:00 [INF] Hosting environment: Development
2025-09-12 14:18:56.487 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-12 14:19:51.186 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-12 14:19:51.252 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:19:51.257 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 72.3793ms
2025-09-12 14:19:51.261 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-12 14:19:51.275 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:19:51.280 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-12 14:19:51.298 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-12 14:19:51.405 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 14:19:51.422 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 119.1371ms
2025-09-12 14:19:51.423 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-12 14:19:51.692 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 430.8893ms
2025-09-12 14:19:53.463 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 14:19:53.463 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 14:19:53.463 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 14:19:53.468 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:19:53.470 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:19:53.473 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 9.1748ms
2025-09-12 14:19:53.476 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 10.3474ms
2025-09-12 14:19:53.470 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:19:53.480 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 14:19:53.488 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 14:19:53.496 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 32.2874ms
2025-09-12 14:19:53.498 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 14:19:53.498 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:19:53.499 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:19:53.503 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:19:53.504 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 14:19:53.505 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 14:19:53.505 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 14:19:53.511 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 14:19:53.511 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 14:19:53.512 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 14:19:53.627 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 14:19:53.811 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 14:19:53.832 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 14:19:54.134 +08:00 [INF] 查询到操作日志总数: 1221
2025-09-12 14:19:54.141 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1221, 状态码种类数=5
2025-09-12 14:19:54.143 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 14:19:54.151 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 635.1016ms
2025-09-12 14:19:54.152 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 14:19:54.246 +08:00 [INF] 成功获取服务状态统计数据：总用户数=5, 活跃用户=5, 今日访问量=456, 待审批申请=1
2025-09-12 14:19:54.248 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 14:19:54.252 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 739.6282ms
2025-09-12 14:19:54.254 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 14:19:54.296 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 808.388ms
2025-09-12 14:19:54.432 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 951.652ms
2025-09-12 14:19:55.132 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1223, 平均月操作量=203.83333333333334, 增长率=null%
2025-09-12 14:19:55.134 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 14:19:55.148 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1629.1604ms
2025-09-12 14:19:55.149 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 14:19:55.279 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1781.6729ms
2025-09-12 14:20:00.158 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-12 14:20:00.158 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-12 14:20:00.161 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:20:00.164 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 6.0345ms
2025-09-12 14:20:00.164 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:20:00.166 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-12 14:20:00.169 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 10.5706ms
2025-09-12 14:20:00.170 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:20:00.171 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-12 14:20:00.173 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-12 14:20:00.174 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:20:00.175 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-12 14:20:00.178 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-12 14:20:00.179 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-12 14:20:00.314 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 14:20:00.316 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 14:20:00.318 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 138.5938ms
2025-09-12 14:20:00.320 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-12 14:20:00.322 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 141.2205ms
2025-09-12 14:20:00.323 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-12 14:20:00.504 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 337.4947ms
2025-09-12 14:20:00.579 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 408.5492ms
2025-09-12 14:20:03.953 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones/2 - null null
2025-09-12 14:20:03.955 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:20:03.956 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones/2 - 204 null null 2.6799ms
2025-09-12 14:20:03.958 +08:00 [INF] Request starting HTTP/2 PUT https://localhost:7007/api/SendMsg/phones/2 - application/json 106
2025-09-12 14:20:03.960 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:20:03.960 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.UpdateSmsPhoneStatus (MapStudio.Api)'
2025-09-12 14:20:03.971 +08:00 [INF] Route matched with {action = "UpdateSmsPhoneStatus", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]] UpdateSmsPhoneStatus(Int32, MapStudio.Api.Models.DTOs.UpdateSmsPhoneSettingRequest) on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-12 14:20:04.350 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 14:20:04.353 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.UpdateSmsPhoneStatus (MapStudio.Api) in 380.3717ms
2025-09-12 14:20:04.354 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.UpdateSmsPhoneStatus (MapStudio.Api)'
2025-09-12 14:20:04.506 +08:00 [INF] Request finished HTTP/2 PUT https://localhost:7007/api/SendMsg/phones/2 - 200 null application/json; charset=utf-8 548.4322ms
2025-09-12 14:20:11.094 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 14:20:11.097 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:20:11.098 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 3.9354ms
2025-09-12 14:20:11.100 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 14:20:11.103 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:20:11.104 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 14:20:11.109 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 14:20:11.182 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 14:20:11.188 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 76.8625ms
2025-09-12 14:20:11.190 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 14:20:11.339 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 239.3892ms
2025-09-12 14:21:30.554 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 14:21:30.562 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:21:30.563 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 8.3393ms
2025-09-12 14:21:30.564 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 14:21:30.568 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:21:30.569 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 14:21:30.570 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 14:21:30.672 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 14:21:30.673 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 100.1348ms
2025-09-12 14:21:30.674 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 14:21:30.809 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 244.6152ms
2025-09-12 14:22:14.061 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?selectedTenant=TENANT-2025001&viewMode=bar - null null
2025-09-12 14:22:14.069 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:22:14.069 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?selectedTenant=TENANT-2025001&viewMode=bar - 204 null null 8.4259ms
2025-09-12 14:22:14.071 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?selectedTenant=TENANT-2025001&viewMode=bar - null null
2025-09-12 14:22:14.073 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:22:14.074 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 14:22:14.075 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 14:22:14.133 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 14:22:14.134 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 57.9681ms
2025-09-12 14:22:14.135 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 14:22:14.276 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?selectedTenant=TENANT-2025001&viewMode=bar - 200 null application/json; charset=utf-8 204.3002ms
2025-09-12 14:25:54.504 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 14:25:54.507 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:25:54.508 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 3.7557ms
2025-09-12 14:25:54.509 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 14:25:54.512 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:25:54.513 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 14:25:54.514 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 14:25:54.582 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 14:25:54.589 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 71.4558ms
2025-09-12 14:25:54.594 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 14:25:54.736 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 227.3157ms
2025-09-12 14:44:54.123 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-12 14:44:54.128 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:44:54.129 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 6.6739ms
2025-09-12 14:44:54.134 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-12 14:44:54.138 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:44:54.139 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-12 14:44:54.141 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-12 14:44:54.424 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 14:44:54.427 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 282.7292ms
2025-09-12 14:44:54.428 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-12 14:44:54.547 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 412.5321ms
2025-09-12 14:45:41.813 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-12 14:45:41.826 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:45:41.830 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 17.0097ms
2025-09-12 14:45:41.832 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-12 14:45:41.845 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:45:41.847 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-12 14:45:41.848 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-12 14:45:41.907 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 14:45:41.908 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 58.4232ms
2025-09-12 14:45:41.909 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-12 14:45:42.042 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 210.1596ms
2025-09-12 14:45:42.895 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-12 14:45:42.895 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-12 14:45:42.897 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:45:42.899 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:45:42.900 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 4.9527ms
2025-09-12 14:45:42.907 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 12.0662ms
2025-09-12 14:45:42.907 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-12 14:45:42.913 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-12 14:45:42.917 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:45:42.919 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:45:42.919 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-12 14:45:42.920 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-12 14:45:42.921 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-12 14:45:42.922 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-12 14:45:43.064 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 14:45:43.065 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 141.4296ms
2025-09-12 14:45:43.066 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-12 14:45:43.192 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 14:45:43.193 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 267.6335ms
2025-09-12 14:45:43.194 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-12 14:45:43.307 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 400.7912ms
2025-09-12 14:45:43.324 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 411.0825ms
2025-09-12 14:45:45.288 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 14:45:45.288 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 14:45:45.288 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 14:45:45.292 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:45:45.294 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:45:45.298 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:45:45.299 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 11.0045ms
2025-09-12 14:45:45.301 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 12.6895ms
2025-09-12 14:45:45.300 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 11.7851ms
2025-09-12 14:45:45.306 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 14:45:45.307 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 14:45:45.302 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 14:45:45.317 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:45:45.319 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:45:45.320 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:45:45.321 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 14:45:45.321 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 14:45:45.322 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 14:45:45.322 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 14:45:45.323 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 14:45:45.324 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 14:45:45.399 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 14:45:45.434 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 14:45:45.719 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 14:45:45.740 +08:00 [INF] 查询到操作日志总数: 1251
2025-09-12 14:45:45.759 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1251, 状态码种类数=5
2025-09-12 14:45:45.762 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 14:45:45.772 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 442.2408ms
2025-09-12 14:45:45.774 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 14:45:45.951 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 643.9895ms
2025-09-12 14:45:46.032 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=5, 今日访问量=486, 待审批申请=1
2025-09-12 14:45:46.033 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 14:45:46.035 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 702.9732ms
2025-09-12 14:45:46.036 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 14:45:46.203 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 900.225ms
2025-09-12 14:45:46.763 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1253, 平均月操作量=208.83333333333334, 增长率=null%
2025-09-12 14:45:46.765 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 14:45:46.766 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1439.4034ms
2025-09-12 14:45:46.766 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 14:45:46.897 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1593.0516ms
2025-09-12 14:47:05.271 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 14:47:05.272 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 14:47:05.276 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 14:47:05.278 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:47:05.281 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:47:05.284 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 12.4325ms
2025-09-12 14:47:05.290 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 15.7413ms
2025-09-12 14:47:05.283 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:47:05.299 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 14:47:05.298 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 14:47:05.310 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 34.2533ms
2025-09-12 14:47:05.312 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 14:47:05.313 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:47:05.315 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:47:05.323 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:47:05.324 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 14:47:05.326 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 14:47:05.327 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 14:47:05.328 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 14:47:05.328 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 14:47:05.329 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 14:47:05.402 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 14:47:05.402 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 14:47:05.416 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 14:47:05.596 +08:00 [INF] 查询到操作日志总数: 1254
2025-09-12 14:47:05.598 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1254, 状态码种类数=5
2025-09-12 14:47:05.599 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 14:47:05.601 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 268.8669ms
2025-09-12 14:47:05.602 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 14:47:05.663 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=5, 今日访问量=489, 待审批申请=1
2025-09-12 14:47:05.664 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 14:47:05.666 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 329.0169ms
2025-09-12 14:47:05.668 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 14:47:05.751 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 451.9496ms
2025-09-12 14:47:05.872 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 574.7286ms
2025-09-12 14:47:06.709 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1256, 平均月操作量=209.33333333333334, 增长率=null%
2025-09-12 14:47:06.711 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 14:47:06.712 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1372.564ms
2025-09-12 14:47:06.713 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 14:47:06.851 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1538.9415ms
2025-09-12 15:02:22.916 +08:00 [INF] 开始初始化数据库...
2025-09-12 15:02:31.350 +08:00 [INF] 数据库表创建完成
2025-09-12 15:04:58.219 +08:00 [INF] 开始初始化数据库...
2025-09-12 15:05:04.076 +08:00 [INF] 数据库表创建完成
2025-09-12 15:05:04.545 +08:00 [INF] 数据库初始化完成
2025-09-12 15:05:04.572 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-12 15:05:04.828 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-12 15:05:04.829 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-12 15:05:04.896 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-12 15:05:04.897 +08:00 [INF] Hosting environment: Development
2025-09-12 15:05:04.898 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-12 15:08:34.301 +08:00 [INF] 开始初始化数据库...
2025-09-12 15:08:39.363 +08:00 [INF] 数据库表创建完成
2025-09-12 15:08:39.811 +08:00 [INF] 数据库初始化完成
2025-09-12 15:08:39.830 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-12 15:08:40.014 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-12 15:08:40.015 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-12 15:08:40.073 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-12 15:08:40.075 +08:00 [INF] Hosting environment: Development
2025-09-12 15:08:40.076 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-12 15:09:06.864 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 15:09:06.864 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 15:09:06.864 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 15:09:06.944 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:09:06.944 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:09:06.944 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:09:06.950 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 87.2285ms
2025-09-12 15:09:06.950 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 87.2307ms
2025-09-12 15:09:06.950 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 87.2001ms
2025-09-12 15:09:06.957 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 15:09:06.957 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 15:09:06.957 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 15:09:06.982 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:09:06.982 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:09:06.982 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:09:06.989 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 15:09:06.989 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 15:09:06.989 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 15:09:07.015 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:09:07.015 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:09:07.015 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:09:07.137 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 15:09:07.306 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 15:09:07.313 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 15:09:07.600 +08:00 [INF] 查询到操作日志总数: 1257
2025-09-12 15:09:07.616 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=5, 今日访问量=492, 待审批申请=1
2025-09-12 15:09:07.617 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1257, 状态码种类数=5
2025-09-12 15:09:07.627 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 15:09:07.627 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 15:09:07.646 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 625.2163ms
2025-09-12 15:09:07.646 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 621.8267ms
2025-09-12 15:09:07.648 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 15:09:07.648 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 15:09:07.846 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 888.8145ms
2025-09-12 15:09:07.886 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 928.7088ms
2025-09-12 15:09:08.520 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1259, 平均月操作量=209.83333333333334, 增长率=null%
2025-09-12 15:09:08.522 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 15:09:08.534 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1514.1253ms
2025-09-12 15:09:08.535 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 15:09:08.661 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1704.1666ms
2025-09-12 15:09:11.283 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/tenants - null null
2025-09-12 15:09:11.287 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:09:11.289 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/tenants - 204 null null 5.4474ms
2025-09-12 15:09:11.290 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/tenants - null null
2025-09-12 15:09:11.294 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:09:11.295 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api)'
2025-09-12 15:09:11.300 +08:00 [INF] Route matched with {action = "GetAllTenants", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.TenantDataDto]]]] GetAllTenants() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:09:15.610 +08:00 [INF] 开始获取所有租户信息
2025-09-12 15:09:25.404 +08:00 [INF] 成功获取所有租户信息，共4个租户
2025-09-12 15:09:25.405 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/tenants - null null
2025-09-12 15:09:25.405 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.TenantDataDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 15:09:25.407 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:09:25.409 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/tenants - 204 null null 4.3694ms
2025-09-12 15:09:25.411 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/tenants - null null
2025-09-12 15:09:25.413 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:09:25.467 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api)'
2025-09-12 15:09:25.508 +08:00 [INF] Route matched with {action = "GetAllTenants", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.TenantDataDto]]]] GetAllTenants() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:09:27.268 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api) in 15964.0358ms
2025-09-12 15:09:28.145 +08:00 [INF] 开始获取所有租户信息
2025-09-12 15:09:28.147 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api)'
2025-09-12 15:09:28.206 +08:00 [INF] 成功获取所有租户信息，共4个租户
2025-09-12 15:09:28.208 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.TenantDataDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 15:09:28.209 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api) in 2657.3733ms
2025-09-12 15:09:28.210 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api)'
2025-09-12 15:09:28.279 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/tenants - 499 null application/json; charset=utf-8 16988.5439ms
2025-09-12 15:09:28.315 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/tenants - 200 null application/json; charset=utf-8 2904.6121ms
2025-09-12 15:09:28.322 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 15:09:28.325 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:09:28.326 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 3.3846ms
2025-09-12 15:09:28.328 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 15:09:28.330 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:09:28.331 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:09:28.341 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:09:29.534 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 15:09:29.541 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 1198.0534ms
2025-09-12 15:09:29.542 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:09:29.639 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 1311.2819ms
2025-09-12 15:44:07.706 +08:00 [INF] 开始初始化数据库...
2025-09-12 15:44:13.534 +08:00 [INF] 数据库表创建完成
2025-09-12 15:44:14.060 +08:00 [INF] 数据库初始化完成
2025-09-12 15:44:14.090 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-12 15:44:14.376 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-12 15:44:14.378 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-12 15:44:14.454 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-12 15:44:14.455 +08:00 [INF] Hosting environment: Development
2025-09-12 15:44:14.456 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-12 15:45:41.485 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-12 15:45:41.566 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:45:41.571 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 88.3839ms
2025-09-12 15:45:41.575 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-12 15:45:41.592 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:45:41.599 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-12 15:45:41.621 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-12 15:45:41.752 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 15:45:41.770 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 144.5163ms
2025-09-12 15:45:41.773 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-12 15:45:41.960 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 384.2595ms
2025-09-12 15:45:48.799 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 15:45:48.799 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 15:45:48.799 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 15:45:48.808 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:45:48.813 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:45:48.814 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:45:48.815 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 16.1775ms
2025-09-12 15:45:48.816 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 17.5111ms
2025-09-12 15:45:48.818 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 18.6467ms
2025-09-12 15:45:48.818 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 15:45:48.825 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 15:45:48.824 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 15:45:48.837 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:45:48.839 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:45:48.840 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:45:48.841 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 15:45:48.842 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 15:45:48.842 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 15:45:48.848 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:45:48.848 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:45:48.850 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:45:48.935 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 15:45:49.151 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 15:45:49.159 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 15:45:49.322 +08:00 [INF] 查询到操作日志总数: 1285
2025-09-12 15:45:49.331 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1285, 状态码种类数=5
2025-09-12 15:45:49.333 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 15:45:49.341 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 490.6815ms
2025-09-12 15:45:49.343 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 15:45:49.481 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=5, 今日访问量=520, 待审批申请=1
2025-09-12 15:45:49.483 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 15:45:49.487 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 633.5625ms
2025-09-12 15:45:49.489 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 15:45:49.529 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 710.6661ms
2025-09-12 15:45:49.608 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 783.947ms
2025-09-12 15:45:50.512 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1287, 平均月操作量=214.5, 增长率=null%
2025-09-12 15:45:50.514 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 15:45:50.526 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1674.4993ms
2025-09-12 15:45:50.527 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 15:45:50.651 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1826.5971ms
2025-09-12 15:45:54.835 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/tenants - null null
2025-09-12 15:45:54.838 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:45:54.840 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/tenants - 204 null null 4.5177ms
2025-09-12 15:45:54.842 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/tenants - null null
2025-09-12 15:45:54.846 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:45:54.846 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api)'
2025-09-12 15:45:54.850 +08:00 [INF] Route matched with {action = "GetAllTenants", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.TenantDataDto]]]] GetAllTenants() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:46:05.511 +08:00 [INF] 开始获取所有租户信息
2025-09-12 15:46:05.570 +08:00 [INF] 成功获取所有租户信息，共4个租户
2025-09-12 15:46:05.571 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.TenantDataDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 15:46:05.813 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api) in 10960.6996ms
2025-09-12 15:46:05.815 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api)'
2025-09-12 15:46:05.941 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/tenants - 499 null application/json; charset=utf-8 11098.3017ms
2025-09-12 15:46:06.517 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/tenants - null null
2025-09-12 15:46:06.522 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:46:06.528 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/tenants - 204 null null 10.601ms
2025-09-12 15:46:06.530 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/tenants - null null
2025-09-12 15:46:06.536 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:46:06.537 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api)'
2025-09-12 15:46:06.538 +08:00 [INF] Route matched with {action = "GetAllTenants", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.TenantDataDto]]]] GetAllTenants() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:46:06.607 +08:00 [INF] 开始获取所有租户信息
2025-09-12 15:46:06.694 +08:00 [INF] 成功获取所有租户信息，共4个租户
2025-09-12 15:46:06.695 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.TenantDataDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 15:46:06.696 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api) in 155.8451ms
2025-09-12 15:46:06.697 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api)'
2025-09-12 15:46:06.834 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/tenants - 200 null application/json; charset=utf-8 304.1044ms
2025-09-12 15:46:06.843 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:46:06.843 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 15:46:06.845 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:46:06.847 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:46:06.848 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - 204 null null 4.6555ms
2025-09-12 15:46:06.849 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 5.4995ms
2025-09-12 15:46:06.850 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:46:06.851 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 15:46:06.856 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:46:06.858 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:46:06.858 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:46:06.859 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:46:06.865 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.DTOs.DailyStatsDTO]] GetDailyStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:46:06.873 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:46:11.666 +08:00 [INF] 开始获取日统计数据
2025-09-12 15:46:45.357 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 15:46:45.414 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 15:46:45.444 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:46:45.469 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 15:46:45.487 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:46:45.468 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:46:45.515 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:46:45.517 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:46:45.523 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - 499 null null 80.4705ms
2025-09-12 15:46:45.524 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 499 null null 55.5444ms
2025-09-12 15:46:45.589 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:46:45.709 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 38833.8974ms
2025-09-12 15:46:45.711 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:46:45.712 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:46:45.713 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - 204 null null 124.0757ms
2025-09-12 15:46:45.709 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 15:46:45.518 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 15:46:45.716 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:46:45.718 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:46:51.487 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 499 null application/json; charset=utf-8 44636.1496ms
2025-09-12 15:46:51.487 +08:00 [ERR] 获取日统计数据失败
SqlSugar.SqlSugarException: SqlSugarException.NotSupportedException：NewExpression
   at SqlSugar.Check.ThrowNotSupportedException(String message)
   at SqlSugar.BaseResolve.ResolveNewExpressions(ExpressionParameter parameter, Expression item, String asName)
   at SqlSugar.NewExpressionResolve.Select(NewExpression expression, ExpressionParameter parameter, Boolean isSingle)
   at SqlSugar.NewExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.LambdaExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.ExpressionContext.Resolve(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetExpressionValue(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetSelectValueByExpression()
   at SqlSugar.MySqlQueryBuilder.get_GetSelectValue()
   at SqlSugar.MySqlQueryBuilder.ToSqlString()
   at SqlSugar.QueryableProvider`1._ToSql()
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.OperationService.GetDailyStatsAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\OperationService.cs:line 574
2025-09-12 15:46:51.488 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:46:45.750 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:46:51.509 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - 204 null null 5792.8728ms
2025-09-12 15:46:51.492 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 5782.615ms
2025-09-12 15:46:51.522 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 6004.6444ms
2025-09-12 15:46:45.518 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 499 null null 133.7746ms
2025-09-12 15:46:45.518 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:46:45.522 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:46:45.518 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 15:46:51.493 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 15:46:51.572 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:46:51.573 +08:00 [ERR] 获取日统计数据失败
SqlSugar.SqlSugarException: SqlSugarException.NotSupportedException：NewExpression
   at SqlSugar.Check.ThrowNotSupportedException(String message)
   at SqlSugar.BaseResolve.ResolveNewExpressions(ExpressionParameter parameter, Expression item, String asName)
   at SqlSugar.NewExpressionResolve.Select(NewExpression expression, ExpressionParameter parameter, Boolean isSingle)
   at SqlSugar.NewExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.LambdaExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.ExpressionContext.Resolve(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetExpressionValue(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetSelectValueByExpression()
   at SqlSugar.MySqlQueryBuilder.get_GetSelectValue()
   at SqlSugar.MySqlQueryBuilder.ToSqlString()
   at SqlSugar.QueryableProvider`1._ToSql()
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.OperationService.GetDailyStatsAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\OperationService.cs:line 574
   at MapStudio.Api.Controllers.OperationController.GetDailyStats() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Controllers\OperationController.cs:line 364
2025-09-12 15:46:51.581 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:46:51.582 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - 499 null null 6113.3771ms
2025-09-12 15:46:51.583 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:46:51.584 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:46:51.585 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:46:51.590 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - 204 null null 6072.6855ms
2025-09-12 15:46:51.591 +08:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-09-12 15:46:51.593 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 6075.0992ms
2025-09-12 15:46:51.593 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:46:51.594 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:46:51.598 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 44731.0621ms
2025-09-12 15:46:51.599 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:46:51.600 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.DTOs.DailyStatsDTO]] GetDailyStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:46:51.601 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:46:53.918 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - 500 null text/plain; charset=utf-8 46128.5321ms
2025-09-12 15:46:52.979 +08:00 [INF] 开始获取日统计数据
2025-09-12 15:46:53.918 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 15:46:53.922 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 2320.3012ms
2025-09-12 15:46:53.923 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:46:54.069 +08:00 [ERR] 获取日统计数据失败
SqlSugar.SqlSugarException: SqlSugarException.NotSupportedException：NewExpression
   at SqlSugar.Check.ThrowNotSupportedException(String message)
   at SqlSugar.BaseResolve.ResolveNewExpressions(ExpressionParameter parameter, Expression item, String asName)
   at SqlSugar.NewExpressionResolve.Select(NewExpression expression, ExpressionParameter parameter, Boolean isSingle)
   at SqlSugar.NewExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.LambdaExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.ExpressionContext.Resolve(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetExpressionValue(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetSelectValueByExpression()
   at SqlSugar.MySqlQueryBuilder.get_GetSelectValue()
   at SqlSugar.MySqlQueryBuilder.ToSqlString()
   at SqlSugar.QueryableProvider`1._ToSql()
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.OperationService.GetDailyStatsAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\OperationService.cs:line 574
2025-09-12 15:46:54.070 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 2576.2392ms
2025-09-12 15:46:54.175 +08:00 [ERR] 获取日统计数据失败
SqlSugar.SqlSugarException: SqlSugarException.NotSupportedException：NewExpression
   at SqlSugar.Check.ThrowNotSupportedException(String message)
   at SqlSugar.BaseResolve.ResolveNewExpressions(ExpressionParameter parameter, Expression item, String asName)
   at SqlSugar.NewExpressionResolve.Select(NewExpression expression, ExpressionParameter parameter, Boolean isSingle)
   at SqlSugar.NewExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.LambdaExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.ExpressionContext.Resolve(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetExpressionValue(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetSelectValueByExpression()
   at SqlSugar.MySqlQueryBuilder.get_GetSelectValue()
   at SqlSugar.MySqlQueryBuilder.ToSqlString()
   at SqlSugar.QueryableProvider`1._ToSql()
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.OperationService.GetDailyStatsAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\OperationService.cs:line 574
   at MapStudio.Api.Controllers.OperationController.GetDailyStats() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Controllers\OperationController.cs:line 364
2025-09-12 15:46:54.181 +08:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-09-12 15:46:54.182 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 2578.8605ms
2025-09-12 15:46:54.184 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:46:54.302 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - 500 null text/plain; charset=utf-8 2736.9437ms
2025-09-12 15:47:16.775 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:47:16.777 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:47:16.778 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - 204 null null 3.2311ms
2025-09-12 15:47:16.780 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:47:16.786 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:47:16.786 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:47:16.787 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.DTOs.DailyStatsDTO]] GetDailyStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:47:18.791 +08:00 [INF] 开始获取日统计数据
2025-09-12 15:47:18.949 +08:00 [ERR] 获取日统计数据失败
SqlSugar.SqlSugarException: SqlSugarException.NotSupportedException：NewExpression
   at SqlSugar.Check.ThrowNotSupportedException(String message)
   at SqlSugar.BaseResolve.ResolveNewExpressions(ExpressionParameter parameter, Expression item, String asName)
   at SqlSugar.NewExpressionResolve.Select(NewExpression expression, ExpressionParameter parameter, Boolean isSingle)
   at SqlSugar.NewExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.LambdaExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.ExpressionContext.Resolve(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetExpressionValue(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetSelectValueByExpression()
   at SqlSugar.MySqlQueryBuilder.get_GetSelectValue()
   at SqlSugar.MySqlQueryBuilder.ToSqlString()
   at SqlSugar.QueryableProvider`1._ToSql()
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.OperationService.GetDailyStatsAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\OperationService.cs:line 574
2025-09-12 15:47:21.766 +08:00 [ERR] 获取日统计数据失败
SqlSugar.SqlSugarException: SqlSugarException.NotSupportedException：NewExpression
   at SqlSugar.Check.ThrowNotSupportedException(String message)
   at SqlSugar.BaseResolve.ResolveNewExpressions(ExpressionParameter parameter, Expression item, String asName)
   at SqlSugar.NewExpressionResolve.Select(NewExpression expression, ExpressionParameter parameter, Boolean isSingle)
   at SqlSugar.NewExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.LambdaExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.ExpressionContext.Resolve(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetExpressionValue(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetSelectValueByExpression()
   at SqlSugar.MySqlQueryBuilder.get_GetSelectValue()
   at SqlSugar.MySqlQueryBuilder.ToSqlString()
   at SqlSugar.QueryableProvider`1._ToSql()
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.OperationService.GetDailyStatsAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\OperationService.cs:line 574
   at MapStudio.Api.Controllers.OperationController.GetDailyStats() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Controllers\OperationController.cs:line 364
2025-09-12 15:47:21.773 +08:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-09-12 15:47:21.774 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 4985.899ms
2025-09-12 15:47:21.775 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:47:21.898 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - 500 null text/plain; charset=utf-8 5117.4087ms
2025-09-12 15:47:37.006 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:47:37.008 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:47:37.008 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - 204 null null 2.2976ms
2025-09-12 15:47:37.010 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:47:37.013 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:47:37.014 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:47:37.015 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.DTOs.DailyStatsDTO]] GetDailyStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:47:39.731 +08:00 [INF] 开始获取日统计数据
2025-09-12 15:47:46.953 +08:00 [ERR] 获取日统计数据失败
SqlSugar.SqlSugarException: SqlSugarException.NotSupportedException：NewExpression
   at SqlSugar.Check.ThrowNotSupportedException(String message)
   at SqlSugar.BaseResolve.ResolveNewExpressions(ExpressionParameter parameter, Expression item, String asName)
   at SqlSugar.NewExpressionResolve.Select(NewExpression expression, ExpressionParameter parameter, Boolean isSingle)
   at SqlSugar.NewExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.LambdaExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.ExpressionContext.Resolve(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetExpressionValue(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetSelectValueByExpression()
   at SqlSugar.MySqlQueryBuilder.get_GetSelectValue()
   at SqlSugar.MySqlQueryBuilder.ToSqlString()
   at SqlSugar.QueryableProvider`1._ToSql()
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.OperationService.GetDailyStatsAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\OperationService.cs:line 574
2025-09-12 15:47:47.040 +08:00 [ERR] 获取日统计数据失败
SqlSugar.SqlSugarException: SqlSugarException.NotSupportedException：NewExpression
   at SqlSugar.Check.ThrowNotSupportedException(String message)
   at SqlSugar.BaseResolve.ResolveNewExpressions(ExpressionParameter parameter, Expression item, String asName)
   at SqlSugar.NewExpressionResolve.Select(NewExpression expression, ExpressionParameter parameter, Boolean isSingle)
   at SqlSugar.NewExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.LambdaExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.ExpressionContext.Resolve(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetExpressionValue(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetSelectValueByExpression()
   at SqlSugar.MySqlQueryBuilder.get_GetSelectValue()
   at SqlSugar.MySqlQueryBuilder.ToSqlString()
   at SqlSugar.QueryableProvider`1._ToSql()
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.OperationService.GetDailyStatsAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\OperationService.cs:line 574
   at MapStudio.Api.Controllers.OperationController.GetDailyStats() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Controllers\OperationController.cs:line 364
2025-09-12 15:47:47.048 +08:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-09-12 15:47:47.049 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 10031.6538ms
2025-09-12 15:47:47.051 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:47:47.231 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - 500 null text/plain; charset=utf-8 10220.7803ms
2025-09-12 15:47:48.012 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:47:48.014 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:47:48.016 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - 204 null null 4.1111ms
2025-09-12 15:47:48.017 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:47:48.019 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:47:48.020 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:47:48.020 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.DTOs.DailyStatsDTO]] GetDailyStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:47:50.273 +08:00 [INF] 开始获取日统计数据
2025-09-12 15:47:50.400 +08:00 [ERR] 获取日统计数据失败
SqlSugar.SqlSugarException: SqlSugarException.NotSupportedException：NewExpression
   at SqlSugar.Check.ThrowNotSupportedException(String message)
   at SqlSugar.BaseResolve.ResolveNewExpressions(ExpressionParameter parameter, Expression item, String asName)
   at SqlSugar.NewExpressionResolve.Select(NewExpression expression, ExpressionParameter parameter, Boolean isSingle)
   at SqlSugar.NewExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.LambdaExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.ExpressionContext.Resolve(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetExpressionValue(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetSelectValueByExpression()
   at SqlSugar.MySqlQueryBuilder.get_GetSelectValue()
   at SqlSugar.MySqlQueryBuilder.ToSqlString()
   at SqlSugar.QueryableProvider`1._ToSql()
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.OperationService.GetDailyStatsAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\OperationService.cs:line 574
2025-09-12 15:47:50.525 +08:00 [ERR] 获取日统计数据失败
SqlSugar.SqlSugarException: SqlSugarException.NotSupportedException：NewExpression
   at SqlSugar.Check.ThrowNotSupportedException(String message)
   at SqlSugar.BaseResolve.ResolveNewExpressions(ExpressionParameter parameter, Expression item, String asName)
   at SqlSugar.NewExpressionResolve.Select(NewExpression expression, ExpressionParameter parameter, Boolean isSingle)
   at SqlSugar.NewExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.LambdaExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.ExpressionContext.Resolve(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetExpressionValue(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetSelectValueByExpression()
   at SqlSugar.MySqlQueryBuilder.get_GetSelectValue()
   at SqlSugar.MySqlQueryBuilder.ToSqlString()
   at SqlSugar.QueryableProvider`1._ToSql()
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.OperationService.GetDailyStatsAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\OperationService.cs:line 574
   at MapStudio.Api.Controllers.OperationController.GetDailyStats() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Controllers\OperationController.cs:line 364
2025-09-12 15:47:50.531 +08:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-09-12 15:47:50.532 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 2510.7208ms
2025-09-12 15:47:50.533 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:47:50.726 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - 500 null text/plain; charset=utf-8 2709.4101ms
2025-09-12 15:47:52.264 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:47:52.267 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:47:52.268 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:47:52.269 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.DTOs.DailyStatsDTO]] GetDailyStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:47:53.530 +08:00 [INF] 开始获取日统计数据
2025-09-12 15:47:53.718 +08:00 [ERR] 获取日统计数据失败
SqlSugar.SqlSugarException: SqlSugarException.NotSupportedException：NewExpression
   at SqlSugar.Check.ThrowNotSupportedException(String message)
   at SqlSugar.BaseResolve.ResolveNewExpressions(ExpressionParameter parameter, Expression item, String asName)
   at SqlSugar.NewExpressionResolve.Select(NewExpression expression, ExpressionParameter parameter, Boolean isSingle)
   at SqlSugar.NewExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.LambdaExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.ExpressionContext.Resolve(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetExpressionValue(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetSelectValueByExpression()
   at SqlSugar.MySqlQueryBuilder.get_GetSelectValue()
   at SqlSugar.MySqlQueryBuilder.ToSqlString()
   at SqlSugar.QueryableProvider`1._ToSql()
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.OperationService.GetDailyStatsAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\OperationService.cs:line 574
2025-09-12 15:47:53.839 +08:00 [ERR] 获取日统计数据失败
SqlSugar.SqlSugarException: SqlSugarException.NotSupportedException：NewExpression
   at SqlSugar.Check.ThrowNotSupportedException(String message)
   at SqlSugar.BaseResolve.ResolveNewExpressions(ExpressionParameter parameter, Expression item, String asName)
   at SqlSugar.NewExpressionResolve.Select(NewExpression expression, ExpressionParameter parameter, Boolean isSingle)
   at SqlSugar.NewExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.LambdaExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.ExpressionContext.Resolve(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetExpressionValue(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetSelectValueByExpression()
   at SqlSugar.MySqlQueryBuilder.get_GetSelectValue()
   at SqlSugar.MySqlQueryBuilder.ToSqlString()
   at SqlSugar.QueryableProvider`1._ToSql()
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.OperationService.GetDailyStatsAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\OperationService.cs:line 574
   at MapStudio.Api.Controllers.OperationController.GetDailyStats() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Controllers\OperationController.cs:line 364
2025-09-12 15:47:53.845 +08:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-09-12 15:47:53.846 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 1575.8086ms
2025-09-12 15:47:53.847 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:47:53.987 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - 500 null text/plain; charset=utf-8 1723.0261ms
2025-09-12 15:47:55.857 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:47:55.863 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:47:55.864 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - 204 null null 7.3613ms
2025-09-12 15:47:55.866 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:47:55.871 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:47:55.872 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:47:55.875 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.DTOs.DailyStatsDTO]] GetDailyStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:47:57.939 +08:00 [INF] 开始获取日统计数据
2025-09-12 15:47:58.090 +08:00 [ERR] 获取日统计数据失败
SqlSugar.SqlSugarException: SqlSugarException.NotSupportedException：NewExpression
   at SqlSugar.Check.ThrowNotSupportedException(String message)
   at SqlSugar.BaseResolve.ResolveNewExpressions(ExpressionParameter parameter, Expression item, String asName)
   at SqlSugar.NewExpressionResolve.Select(NewExpression expression, ExpressionParameter parameter, Boolean isSingle)
   at SqlSugar.NewExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.LambdaExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.ExpressionContext.Resolve(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetExpressionValue(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetSelectValueByExpression()
   at SqlSugar.MySqlQueryBuilder.get_GetSelectValue()
   at SqlSugar.MySqlQueryBuilder.ToSqlString()
   at SqlSugar.QueryableProvider`1._ToSql()
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.OperationService.GetDailyStatsAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\OperationService.cs:line 574
2025-09-12 15:47:58.181 +08:00 [ERR] 获取日统计数据失败
SqlSugar.SqlSugarException: SqlSugarException.NotSupportedException：NewExpression
   at SqlSugar.Check.ThrowNotSupportedException(String message)
   at SqlSugar.BaseResolve.ResolveNewExpressions(ExpressionParameter parameter, Expression item, String asName)
   at SqlSugar.NewExpressionResolve.Select(NewExpression expression, ExpressionParameter parameter, Boolean isSingle)
   at SqlSugar.NewExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.LambdaExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.ExpressionContext.Resolve(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetExpressionValue(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetSelectValueByExpression()
   at SqlSugar.MySqlQueryBuilder.get_GetSelectValue()
   at SqlSugar.MySqlQueryBuilder.ToSqlString()
   at SqlSugar.QueryableProvider`1._ToSql()
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at MapStudio.Api.Services.Implementations.OperationService.GetDailyStatsAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Services\Implementations\OperationService.cs:line 574
   at MapStudio.Api.Controllers.OperationController.GetDailyStats() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Controllers\OperationController.cs:line 364
2025-09-12 15:47:58.187 +08:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-09-12 15:47:58.188 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 2311.0547ms
2025-09-12 15:47:58.189 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:47:58.366 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - 500 null text/plain; charset=utf-8 2499.7216ms
2025-09-12 15:48:09.078 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:48:09.080 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:48:09.081 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - 204 null null 3.0281ms
2025-09-12 15:48:09.083 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:48:09.086 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:48:09.087 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:48:09.088 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.DTOs.DailyStatsDTO]] GetDailyStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:48:11.973 +08:00 [INF] 开始获取日统计数据
2025-09-12 15:52:09.696 +08:00 [INF] 开始初始化数据库...
2025-09-12 15:52:15.466 +08:00 [INF] 数据库表创建完成
2025-09-12 15:52:15.921 +08:00 [INF] 数据库初始化完成
2025-09-12 15:52:15.946 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-12 15:52:16.182 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-12 15:52:16.182 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-12 15:52:16.242 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-12 15:52:16.243 +08:00 [INF] Hosting environment: Development
2025-09-12 15:52:16.243 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-12 15:52:34.728 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:52:34.780 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:52:34.785 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - 204 null null 58.39ms
2025-09-12 15:52:34.788 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:52:34.805 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:52:34.810 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:52:34.826 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.DTOs.DailyStatsDTO]] GetDailyStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:52:38.795 +08:00 [INF] 开始获取日统计数据
2025-09-12 15:52:39.889 +08:00 [INF] 租户总数统计完成: 4
2025-09-12 15:52:40.118 +08:00 [INF] 开始统计每日操作数据
2025-09-12 15:52:41.718 +08:00 [INF] 查询到原始操作日志数据: 1301条
2025-09-12 15:52:52.104 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:52:52.134 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:52:52.135 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - 204 null null 30.7477ms
2025-09-12 15:52:52.136 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:52:53.198 +08:00 [INF] 按日期分组后的统计数据: 3天
2025-09-12 15:52:53.199 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:52:59.280 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:53:00.122 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.DTOs.DailyStatsDTO]] GetDailyStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:53:00.125 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:53:00.127 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:53:00.140 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - 204 null null 15.1255ms
2025-09-12 15:53:08.183 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:53:08.184 +08:00 [INF] 平均日操作次数: 433.666666666667
2025-09-12 15:53:08.188 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:53:08.191 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:54:04.894 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.DTOs.DailyStatsDTO]] GetDailyStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:54:07.880 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:54:09.657 +08:00 [INF] 开始获取日统计数据
2025-09-12 15:54:13.301 +08:00 [INF] 最大日操作数: 536
2025-09-12 15:54:09.665 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:54:16.250 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - 499 null null 6979.0708ms
2025-09-12 15:54:16.250 +08:00 [INF] 开始获取日统计数据
2025-09-12 15:54:16.270 +08:00 [INF] 租户总数统计完成: 4
2025-09-12 15:54:16.271 +08:00 [INF] 开始统计每日操作数据
2025-09-12 15:54:16.500 +08:00 [INF] 租户总数统计完成: 4
2025-09-12 15:54:16.500 +08:00 [INF] 开始统计每日操作数据
2025-09-12 15:54:16.504 +08:00 [INF] 今日操作总量: 536
2025-09-12 15:54:16.505 +08:00 [INF] 成功获取日统计数据：租户总数=4, 平均日操作次数=433.666666666667, 最大日操作数=536, 操作总量=536
2025-09-12 15:54:41.166 +08:00 [INF] 查询到原始操作日志数据: 1301条
2025-09-12 15:54:41.168 +08:00 [INF] 按日期分组后的统计数据: 3天
2025-09-12 15:54:41.802 +08:00 [INF] 平均日操作次数: 433.666666666667
2025-09-12 15:54:41.803 +08:00 [INF] 最大日操作数: 536
2025-09-12 15:54:41.804 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.DTOs.DailyStatsDTO'.
2025-09-12 15:54:41.914 +08:00 [INF] 今日操作总量: 536
2025-09-12 15:54:41.960 +08:00 [INF] 成功获取日统计数据：租户总数=4, 平均日操作次数=433.666666666667, 最大日操作数=536, 操作总量=536
2025-09-12 15:54:47.586 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.DTOs.DailyStatsDTO'.
2025-09-12 15:54:47.910 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 107774.4099ms
2025-09-12 15:54:47.910 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 132908.6624ms
2025-09-12 15:54:47.912 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:54:47.912 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:54:48.000 +08:00 [INF] 查询到原始操作日志数据: 1301条
2025-09-12 15:54:48.002 +08:00 [INF] 按日期分组后的统计数据: 3天
2025-09-12 15:54:48.002 +08:00 [INF] 平均日操作次数: 433.666666666667
2025-09-12 15:54:48.003 +08:00 [INF] 最大日操作数: 536
2025-09-12 15:54:48.074 +08:00 [INF] 今日操作总量: 536
2025-09-12 15:54:48.074 +08:00 [INF] 成功获取日统计数据：租户总数=4, 平均日操作次数=433.666666666667, 最大日操作数=536, 操作总量=536
2025-09-12 15:54:48.076 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.DTOs.DailyStatsDTO'.
2025-09-12 15:54:48.355 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 38730.6273ms
2025-09-12 15:54:48.356 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:54:48.358 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - 499 null application/json; charset=utf-8 116221.8122ms
2025-09-12 15:54:48.358 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - 499 null application/json; charset=utf-8 133569.9071ms
2025-09-12 15:54:48.489 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - 499 null application/json; charset=utf-8 100306.6635ms
2025-09-12 15:54:50.880 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:54:50.882 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:54:50.883 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - 204 null null 2.5215ms
2025-09-12 15:54:50.884 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:54:50.887 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:54:50.888 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:54:50.890 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.DTOs.DailyStatsDTO]] GetDailyStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:54:51.435 +08:00 [INF] 开始获取日统计数据
2025-09-12 15:54:51.520 +08:00 [INF] 租户总数统计完成: 4
2025-09-12 15:54:51.520 +08:00 [INF] 开始统计每日操作数据
2025-09-12 15:54:51.966 +08:00 [INF] 查询到原始操作日志数据: 1304条
2025-09-12 15:54:51.968 +08:00 [INF] 按日期分组后的统计数据: 3天
2025-09-12 15:54:51.969 +08:00 [INF] 平均日操作次数: 434.666666666667
2025-09-12 15:54:51.970 +08:00 [INF] 最大日操作数: 539
2025-09-12 15:54:52.045 +08:00 [INF] 今日操作总量: 539
2025-09-12 15:54:52.047 +08:00 [INF] 成功获取日统计数据：租户总数=4, 平均日操作次数=434.666666666667, 最大日操作数=539, 操作总量=539
2025-09-12 15:54:52.048 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.DTOs.DailyStatsDTO'.
2025-09-12 15:54:52.049 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 1158.495ms
2025-09-12 15:54:52.050 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:54:52.191 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - 200 null application/json; charset=utf-8 1307.6649ms
2025-09-12 15:56:30.090 +08:00 [INF] 开始初始化数据库...
2025-09-12 15:56:35.950 +08:00 [INF] 数据库表创建完成
2025-09-12 15:56:36.522 +08:00 [INF] 数据库初始化完成
2025-09-12 15:56:36.553 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-12 15:56:36.839 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-12 15:56:36.839 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-12 15:56:36.907 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-12 15:56:36.908 +08:00 [INF] Hosting environment: Development
2025-09-12 15:56:36.909 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-12 15:57:27.079 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/tenants - null null
2025-09-12 15:57:27.205 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:57:27.210 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/tenants - 204 null null 132.2128ms
2025-09-12 15:57:27.214 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/tenants - null null
2025-09-12 15:57:27.229 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:57:27.234 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api)'
2025-09-12 15:57:27.250 +08:00 [INF] Route matched with {action = "GetAllTenants", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.TenantDataDto]]]] GetAllTenants() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:57:27.374 +08:00 [INF] 开始获取所有租户信息
2025-09-12 15:57:27.472 +08:00 [INF] 成功获取所有租户信息，共4个租户
2025-09-12 15:57:27.481 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.TenantDataDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 15:57:27.500 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api) in 244.5964ms
2025-09-12 15:57:27.503 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api)'
2025-09-12 15:57:27.793 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/tenants - 200 null application/json; charset=utf-8 579.3682ms
2025-09-12 15:57:27.800 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:57:27.800 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 15:57:27.805 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:57:27.806 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:57:27.807 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - 204 null null 7.0091ms
2025-09-12 15:57:27.808 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 7.8172ms
2025-09-12 15:57:27.810 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 15:57:27.858 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 15:57:27.865 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:57:27.867 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:57:27.867 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:57:27.868 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:57:27.873 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.DTOs.DailyStatsDTO]] GetDailyStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:57:27.880 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:57:29.990 +08:00 [INF] 开始获取日统计数据
2025-09-12 15:57:30.079 +08:00 [INF] 租户总数统计完成: 4
2025-09-12 15:57:30.080 +08:00 [INF] 开始统计每日操作数据
2025-09-12 15:57:34.765 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 15:57:34.772 +08:00 [INF] 查询到原始操作日志数据: 1310条
2025-09-12 15:57:34.773 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 6890.4502ms
2025-09-12 15:57:34.776 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:57:34.777 +08:00 [INF] 按日期分组后的统计数据: 3天
2025-09-12 15:57:34.780 +08:00 [INF] 平均日操作次数: 436
2025-09-12 15:57:34.782 +08:00 [INF] 最大日操作数: 545
2025-09-12 15:57:34.853 +08:00 [INF] 今日操作总量: 545
2025-09-12 15:57:34.854 +08:00 [INF] 成功获取日统计数据：租户总数=4, 平均日操作次数=436, 最大日操作数=545, 操作总量=545
2025-09-12 15:57:34.855 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.DTOs.DailyStatsDTO'.
2025-09-12 15:57:34.863 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 6987.6626ms
2025-09-12 15:57:34.864 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:57:34.932 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 7084.8208ms
2025-09-12 15:57:35.016 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - 200 null application/json; charset=utf-8 7206.2072ms
2025-09-12 15:57:39.926 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?selectedTenant=250912&viewMode=bar - null null
2025-09-12 15:57:39.930 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:57:39.940 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?selectedTenant=250912&viewMode=bar - 204 null null 13.7634ms
2025-09-12 15:57:39.942 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?selectedTenant=250912&viewMode=bar - null null
2025-09-12 15:57:39.953 +08:00 [INF] CORS policy execution successful.
2025-09-12 15:57:39.960 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:57:39.961 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 15:57:40.023 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 15:57:40.025 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 60.029ms
2025-09-12 15:57:40.026 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 15:57:40.148 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?selectedTenant=250912&viewMode=bar - 200 null application/json; charset=utf-8 206.0852ms
2025-09-12 16:34:09.981 +08:00 [INF] 开始初始化数据库...
2025-09-12 16:34:16.498 +08:00 [INF] 数据库表创建完成
2025-09-12 16:34:16.958 +08:00 [INF] 数据库初始化完成
2025-09-12 16:34:16.996 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-12 16:34:17.277 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-12 16:34:17.279 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-12 16:34:17.337 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-12 16:34:17.338 +08:00 [INF] Hosting environment: Development
2025-09-12 16:34:17.339 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-12 16:34:20.833 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 16:34:20.833 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 16:34:20.833 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 16:34:20.908 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:34:20.908 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:34:20.908 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:34:20.915 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 85.2551ms
2025-09-12 16:34:20.919 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 16:34:20.915 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 85.3054ms
2025-09-12 16:34:20.915 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 85.2258ms
2025-09-12 16:34:20.919 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 16:34:20.919 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 16:34:20.939 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:34:20.939 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:34:20.939 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:34:20.944 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 16:34:20.944 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 16:34:20.944 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 16:34:20.963 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:34:20.963 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:34:20.963 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:34:21.102 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 16:34:21.300 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 16:34:21.306 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 16:34:21.610 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=5, 今日访问量=602, 待审批申请=1
2025-09-12 16:34:21.622 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 16:34:21.644 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 674.9282ms
2025-09-12 16:34:21.647 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 16:34:21.821 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 901.0206ms
2025-09-12 16:34:21.869 +08:00 [INF] 查询到操作日志总数: 1367
2025-09-12 16:34:21.877 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1367, 状态码种类数=5
2025-09-12 16:34:21.878 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 16:34:21.885 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 917.0448ms
2025-09-12 16:34:21.886 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 16:34:22.054 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 1134.7942ms
2025-09-12 16:34:22.490 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1368, 平均月操作量=228, 增长率=null%
2025-09-12 16:34:22.492 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 16:34:22.504 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1538.7388ms
2025-09-12 16:34:22.505 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 16:34:22.639 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1719.11ms
2025-09-12 16:34:32.962 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/tenants - null null
2025-09-12 16:34:32.965 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:34:32.967 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/tenants - 204 null null 4.4583ms
2025-09-12 16:34:32.969 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/tenants - null null
2025-09-12 16:34:32.971 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:34:32.972 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api)'
2025-09-12 16:34:32.977 +08:00 [INF] Route matched with {action = "GetAllTenants", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.TenantDataDto]]]] GetAllTenants() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:34:33.047 +08:00 [INF] 开始获取所有租户信息
2025-09-12 16:34:33.113 +08:00 [INF] 成功获取所有租户信息，共4个租户
2025-09-12 16:34:33.114 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.TenantDataDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 16:34:33.117 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api) in 138.4298ms
2025-09-12 16:34:33.118 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api)'
2025-09-12 16:34:33.321 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/tenants - 200 null application/json; charset=utf-8 352.5185ms
2025-09-12 16:34:33.327 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 16:34:33.327 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 16:34:33.329 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:34:33.332 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:34:33.332 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - 204 null null 5.2035ms
2025-09-12 16:34:33.334 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 16:34:33.334 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 6.5977ms
2025-09-12 16:34:33.335 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 16:34:33.337 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:34:33.340 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:34:33.341 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:34:33.342 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:34:33.351 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.DTOs.DailyStatsDTO]] GetDailyStats(System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:34:33.351 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:34:33.440 +08:00 [INF] 开始获取日统计数据
2025-09-12 16:34:33.441 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 16:34:33.447 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 93.5413ms
2025-09-12 16:34:33.448 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:34:33.504 +08:00 [INF] 租户总数统计完成: 4
2025-09-12 16:34:33.505 +08:00 [INF] 开始统计每日操作数据
2025-09-12 16:34:33.571 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 235.9653ms
2025-09-12 16:34:33.935 +08:00 [INF] 查询到原始操作日志数据: 1372条
2025-09-12 16:34:33.940 +08:00 [INF] 按日期分组后的统计数据: 3天
2025-09-12 16:34:33.944 +08:00 [INF] 平均日操作次数: 457.333333333333
2025-09-12 16:34:33.946 +08:00 [INF] 最大日操作数: 607
2025-09-12 16:34:34.006 +08:00 [INF] 今日操作总量: 607
2025-09-12 16:34:34.008 +08:00 [INF] 成功获取日统计数据：租户总数=4, 平均日操作次数=457.333333333333, 最大日操作数=607, 操作总量=607
2025-09-12 16:34:34.009 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.DTOs.DailyStatsDTO'.
2025-09-12 16:34:34.016 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 663.0709ms
2025-09-12 16:34:34.017 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:34:34.177 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - 200 null application/json; charset=utf-8 843.3998ms
2025-09-12 16:35:16.476 +08:00 [INF] 开始初始化数据库...
2025-09-12 16:35:22.429 +08:00 [INF] 数据库表创建完成
2025-09-12 16:35:22.876 +08:00 [INF] 数据库初始化完成
2025-09-12 16:35:22.903 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-12 16:35:23.192 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-12 16:35:23.192 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-12 16:35:23.262 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-12 16:35:23.263 +08:00 [INF] Hosting environment: Development
2025-09-12 16:35:23.264 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-12 16:37:08.317 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/tenants - null null
2025-09-12 16:37:08.379 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:08.384 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/tenants - 204 null null 68.1943ms
2025-09-12 16:37:08.387 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/tenants - null null
2025-09-12 16:37:08.402 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:08.406 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api)'
2025-09-12 16:37:08.422 +08:00 [INF] Route matched with {action = "GetAllTenants", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.TenantDataDto]]]] GetAllTenants() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:37:08.535 +08:00 [INF] 开始获取所有租户信息
2025-09-12 16:37:08.671 +08:00 [INF] 成功获取所有租户信息，共4个租户
2025-09-12 16:37:08.680 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.TenantDataDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 16:37:08.694 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api) in 267.9831ms
2025-09-12 16:37:08.696 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api)'
2025-09-12 16:37:08.904 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/tenants - 200 null application/json; charset=utf-8 516.8652ms
2025-09-12 16:37:08.909 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 16:37:08.909 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 16:37:08.913 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:08.913 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:08.914 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - 204 null null 5.2298ms
2025-09-12 16:37:08.915 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 6.2354ms
2025-09-12 16:37:08.916 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 16:37:08.917 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 16:37:08.924 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:08.926 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:08.927 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:08.928 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:08.937 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.DTOs.DailyStatsDTO]] GetDailyStats(System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:37:08.937 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:37:09.031 +08:00 [INF] 开始获取日统计数据
2025-09-12 16:37:09.115 +08:00 [INF] 租户总数统计完成: 4
2025-09-12 16:37:09.116 +08:00 [INF] 开始统计每日操作数据
2025-09-12 16:37:09.242 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 16:37:09.250 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 310.0988ms
2025-09-12 16:37:09.251 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:09.434 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 517.1251ms
2025-09-12 16:37:09.624 +08:00 [INF] 查询到原始操作日志数据: 1378条
2025-09-12 16:37:09.629 +08:00 [INF] 按日期分组后的统计数据: 3天
2025-09-12 16:37:09.633 +08:00 [INF] 平均日操作次数: 459
2025-09-12 16:37:09.634 +08:00 [INF] 最大日操作数: 613
2025-09-12 16:37:09.706 +08:00 [INF] 今日操作总量: 614
2025-09-12 16:37:09.708 +08:00 [INF] 成功获取日统计数据：租户总数=4, 平均日操作次数=459, 最大日操作数=613, 操作总量=614
2025-09-12 16:37:09.710 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.DTOs.DailyStatsDTO'.
2025-09-12 16:37:09.717 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 778.0494ms
2025-09-12 16:37:09.718 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:09.862 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - 200 null application/json; charset=utf-8 945.9286ms
2025-09-12 16:37:14.772 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?selectedTenant=250912&viewMode=bar - null null
2025-09-12 16:37:14.772 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary?tenantName=%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7250912 - null null
2025-09-12 16:37:14.776 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:14.778 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:14.780 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?selectedTenant=250912&viewMode=bar - 204 null null 7.7987ms
2025-09-12 16:37:14.781 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary?tenantName=%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7250912 - 204 null null 8.7016ms
2025-09-12 16:37:14.782 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?selectedTenant=250912&viewMode=bar - null null
2025-09-12 16:37:14.783 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary?tenantName=%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7250912 - null null
2025-09-12 16:37:14.789 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:14.791 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:14.791 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:14.792 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:14.793 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:37:14.794 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.DTOs.DailyStatsDTO]] GetDailyStats(System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:37:14.857 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 16:37:14.858 +08:00 [INF] 开始获取日统计数据
2025-09-12 16:37:14.859 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 63.2588ms
2025-09-12 16:37:14.861 +08:00 [INF] 指定租户名称，租户总数设为1
2025-09-12 16:37:14.862 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:14.863 +08:00 [INF] 开始统计每日操作数据
2025-09-12 16:37:14.865 +08:00 [INF] 按租户名称过滤，使用UserId字段: 测试用户250912
2025-09-12 16:37:15.008 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?selectedTenant=250912&viewMode=bar - 200 null application/json; charset=utf-8 225.6501ms
2025-09-12 16:37:15.156 +08:00 [INF] 查询到原始操作日志数据: 994条
2025-09-12 16:37:15.158 +08:00 [INF] 按日期分组后的统计数据: 3天
2025-09-12 16:37:15.159 +08:00 [INF] 平均日操作次数: 331
2025-09-12 16:37:15.160 +08:00 [INF] 最大日操作数: 408
2025-09-12 16:37:15.162 +08:00 [INF] 按租户名称过滤今日操作总量，使用UserId字段: 测试用户250912
2025-09-12 16:37:15.316 +08:00 [INF] 今日操作总量: 389
2025-09-12 16:37:15.317 +08:00 [INF] 成功获取日统计数据：租户总数=1, 平均日操作次数=331, 最大日操作数=408, 操作总量=389
2025-09-12 16:37:15.318 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.DTOs.DailyStatsDTO'.
2025-09-12 16:37:15.319 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 521.2974ms
2025-09-12 16:37:15.320 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:15.473 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary?tenantName=%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7250912 - 200 null application/json; charset=utf-8 690.599ms
2025-09-12 16:37:18.964 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?selectedTenant=250911&viewMode=bar - null null
2025-09-12 16:37:18.964 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary?tenantName=%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7250911 - null null
2025-09-12 16:37:18.968 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:18.970 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:18.970 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?selectedTenant=250911&viewMode=bar - 204 null null 5.7513ms
2025-09-12 16:37:18.971 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary?tenantName=%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7250911 - 204 null null 6.2893ms
2025-09-12 16:37:18.982 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?selectedTenant=250911&viewMode=bar - null null
2025-09-12 16:37:18.982 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary?tenantName=%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7250911 - null null
2025-09-12 16:37:18.984 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:18.986 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:18.986 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:18.986 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:18.987 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:37:18.988 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.DTOs.DailyStatsDTO]] GetDailyStats(System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:37:19.103 +08:00 [INF] 开始获取日统计数据
2025-09-12 16:37:19.104 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 16:37:19.104 +08:00 [INF] 指定租户名称，租户总数设为1
2025-09-12 16:37:19.105 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 115.9254ms
2025-09-12 16:37:19.105 +08:00 [INF] 开始统计每日操作数据
2025-09-12 16:37:19.106 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:19.107 +08:00 [INF] 按租户名称过滤，使用UserId字段: 测试用户250911
2025-09-12 16:37:19.197 +08:00 [INF] 查询到原始操作日志数据: 0条
2025-09-12 16:37:19.198 +08:00 [INF] 按日期分组后的统计数据: 0天
2025-09-12 16:37:19.200 +08:00 [INF] 按租户名称过滤今日操作总量，使用UserId字段: 测试用户250911
2025-09-12 16:37:19.263 +08:00 [INF] 今日操作总量: 0
2025-09-12 16:37:19.264 +08:00 [INF] 成功获取日统计数据：租户总数=1, 平均日操作次数=0, 最大日操作数=0, 操作总量=0
2025-09-12 16:37:19.265 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.DTOs.DailyStatsDTO'.
2025-09-12 16:37:19.265 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 268.8524ms
2025-09-12 16:37:19.266 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:19.304 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?selectedTenant=250911&viewMode=bar - 200 null application/json; charset=utf-8 322.9148ms
2025-09-12 16:37:19.412 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary?tenantName=%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7250911 - 200 null application/json; charset=utf-8 430.2942ms
2025-09-12 16:37:21.330 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary?tenantName=%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7250908 - null null
2025-09-12 16:37:21.330 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?selectedTenant=250908&viewMode=bar - null null
2025-09-12 16:37:21.333 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:21.336 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:21.337 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary?tenantName=%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7250908 - 204 null null 6.1341ms
2025-09-12 16:37:21.337 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?selectedTenant=250908&viewMode=bar - 204 null null 6.6379ms
2025-09-12 16:37:21.338 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary?tenantName=%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7250908 - null null
2025-09-12 16:37:21.338 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?selectedTenant=250908&viewMode=bar - null null
2025-09-12 16:37:21.346 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:21.348 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:21.348 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:21.349 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:21.350 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.DTOs.DailyStatsDTO]] GetDailyStats(System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:37:21.350 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:37:21.438 +08:00 [INF] 开始获取日统计数据
2025-09-12 16:37:21.439 +08:00 [INF] 指定租户名称，租户总数设为1
2025-09-12 16:37:21.439 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 16:37:21.440 +08:00 [INF] 开始统计每日操作数据
2025-09-12 16:37:21.441 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 88.2439ms
2025-09-12 16:37:21.443 +08:00 [INF] 按租户名称过滤，使用UserId字段: 测试用户250908
2025-09-12 16:37:21.443 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:21.507 +08:00 [INF] 查询到原始操作日志数据: 0条
2025-09-12 16:37:21.508 +08:00 [INF] 按日期分组后的统计数据: 0天
2025-09-12 16:37:21.511 +08:00 [INF] 按租户名称过滤今日操作总量，使用UserId字段: 测试用户250908
2025-09-12 16:37:21.572 +08:00 [INF] 今日操作总量: 0
2025-09-12 16:37:21.573 +08:00 [INF] 成功获取日统计数据：租户总数=1, 平均日操作次数=0, 最大日操作数=0, 操作总量=0
2025-09-12 16:37:21.575 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.DTOs.DailyStatsDTO'.
2025-09-12 16:37:21.577 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 224.935ms
2025-09-12 16:37:21.578 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:21.628 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?selectedTenant=250908&viewMode=bar - 200 null application/json; charset=utf-8 289.6949ms
2025-09-12 16:37:21.712 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary?tenantName=%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7250908 - 200 null application/json; charset=utf-8 373.7138ms
2025-09-12 16:37:23.369 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?selectedTenant=test_My_45&viewMode=bar - null null
2025-09-12 16:37:23.369 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary?tenantName=%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7250945 - null null
2025-09-12 16:37:23.372 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:23.377 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:23.378 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?selectedTenant=test_My_45&viewMode=bar - 204 null null 8.3131ms
2025-09-12 16:37:23.378 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary?tenantName=%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7250945 - 204 null null 8.9288ms
2025-09-12 16:37:23.380 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?selectedTenant=test_My_45&viewMode=bar - null null
2025-09-12 16:37:23.380 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary?tenantName=%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7250945 - null null
2025-09-12 16:37:23.387 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:23.389 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:23.390 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:23.390 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:23.391 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:37:23.392 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.DTOs.DailyStatsDTO]] GetDailyStats(System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:37:23.463 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 16:37:23.465 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 71.0829ms
2025-09-12 16:37:23.466 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:23.494 +08:00 [INF] 开始获取日统计数据
2025-09-12 16:37:23.495 +08:00 [INF] 指定租户名称，租户总数设为1
2025-09-12 16:37:23.496 +08:00 [INF] 开始统计每日操作数据
2025-09-12 16:37:23.497 +08:00 [INF] 按租户名称过滤，使用UserId字段: 测试用户250945
2025-09-12 16:37:23.567 +08:00 [INF] 查询到原始操作日志数据: 30条
2025-09-12 16:37:23.568 +08:00 [INF] 按日期分组后的统计数据: 1天
2025-09-12 16:37:23.568 +08:00 [INF] 平均日操作次数: 30
2025-09-12 16:37:23.569 +08:00 [INF] 最大日操作数: 30
2025-09-12 16:37:23.570 +08:00 [INF] 按租户名称过滤今日操作总量，使用UserId字段: 测试用户250945
2025-09-12 16:37:23.627 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?selectedTenant=test_My_45&viewMode=bar - 200 null application/json; charset=utf-8 247.4235ms
2025-09-12 16:37:23.632 +08:00 [INF] 今日操作总量: 30
2025-09-12 16:37:23.632 +08:00 [INF] 成功获取日统计数据：租户总数=1, 平均日操作次数=30, 最大日操作数=30, 操作总量=30
2025-09-12 16:37:23.633 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.DTOs.DailyStatsDTO'.
2025-09-12 16:37:23.634 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 238.9829ms
2025-09-12 16:37:23.635 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:23.823 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary?tenantName=%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7250945 - 200 null application/json; charset=utf-8 443.3611ms
2025-09-12 16:37:30.122 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?selectedTenant=250912&viewMode=bar - null null
2025-09-12 16:37:30.122 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary?tenantName=%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7250912 - null null
2025-09-12 16:37:30.130 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:30.133 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:30.134 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?selectedTenant=250912&viewMode=bar - 204 null null 12.0471ms
2025-09-12 16:37:30.136 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?selectedTenant=250912&viewMode=bar - null null
2025-09-12 16:37:30.140 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary?tenantName=%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7250912 - 204 null null 18.3058ms
2025-09-12 16:37:30.143 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary?tenantName=%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7250912 - null null
2025-09-12 16:37:30.153 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:30.158 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:30.157 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:37:30.161 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:37:30.162 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:30.164 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.DTOs.DailyStatsDTO]] GetDailyStats(System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:37:30.253 +08:00 [INF] 开始获取日统计数据
2025-09-12 16:37:30.254 +08:00 [INF] 指定租户名称，租户总数设为1
2025-09-12 16:37:30.254 +08:00 [INF] 开始统计每日操作数据
2025-09-12 16:37:30.254 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 16:37:30.256 +08:00 [INF] 按租户名称过滤，使用UserId字段: 测试用户250912
2025-09-12 16:37:30.256 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 92.912ms
2025-09-12 16:37:30.257 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:30.448 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?selectedTenant=250912&viewMode=bar - 200 null application/json; charset=utf-8 311.7303ms
2025-09-12 16:37:30.564 +08:00 [INF] 查询到原始操作日志数据: 994条
2025-09-12 16:37:30.566 +08:00 [INF] 按日期分组后的统计数据: 3天
2025-09-12 16:37:30.566 +08:00 [INF] 平均日操作次数: 331
2025-09-12 16:37:30.567 +08:00 [INF] 最大日操作数: 408
2025-09-12 16:37:30.569 +08:00 [INF] 按租户名称过滤今日操作总量，使用UserId字段: 测试用户250912
2025-09-12 16:37:30.631 +08:00 [INF] 今日操作总量: 389
2025-09-12 16:37:30.632 +08:00 [INF] 成功获取日统计数据：租户总数=1, 平均日操作次数=331, 最大日操作数=408, 操作总量=389
2025-09-12 16:37:30.633 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.DTOs.DailyStatsDTO'.
2025-09-12 16:37:30.634 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 468.7887ms
2025-09-12 16:37:30.635 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:37:30.780 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary?tenantName=%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7250912 - 200 null application/json; charset=utf-8 636.9264ms
2025-09-12 16:39:37.805 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 16:39:37.806 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 16:39:37.820 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:39:37.821 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:39:37.822 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 17.2806ms
2025-09-12 16:39:37.823 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - 204 null null 17.0254ms
2025-09-12 16:39:37.825 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 16:39:37.825 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 16:39:37.829 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:39:37.830 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:39:37.831 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:39:37.831 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:39:37.832 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:39:37.833 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.DTOs.DailyStatsDTO]] GetDailyStats(System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:39:37.928 +08:00 [INF] 开始获取日统计数据
2025-09-12 16:39:37.928 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 16:39:37.929 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 95.5116ms
2025-09-12 16:39:37.930 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:39:37.992 +08:00 [INF] 租户总数统计完成: 4
2025-09-12 16:39:37.992 +08:00 [INF] 开始统计每日操作数据
2025-09-12 16:39:38.105 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 279.6112ms
2025-09-12 16:39:38.412 +08:00 [INF] 查询到原始操作日志数据: 1390条
2025-09-12 16:39:38.414 +08:00 [INF] 按日期分组后的统计数据: 3天
2025-09-12 16:39:38.415 +08:00 [INF] 平均日操作次数: 463
2025-09-12 16:39:38.415 +08:00 [INF] 最大日操作数: 625
2025-09-12 16:39:38.479 +08:00 [INF] 今日操作总量: 626
2025-09-12 16:39:38.480 +08:00 [INF] 成功获取日统计数据：租户总数=4, 平均日操作次数=463, 最大日操作数=625, 操作总量=626
2025-09-12 16:39:38.481 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.DTOs.DailyStatsDTO'.
2025-09-12 16:39:38.482 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 646.5654ms
2025-09-12 16:39:38.483 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:39:38.665 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - 200 null application/json; charset=utf-8 839.9483ms
2025-09-12 16:46:52.113 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/tenants - null null
2025-09-12 16:46:52.120 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:46:52.121 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/tenants - 204 null null 8.6187ms
2025-09-12 16:46:52.123 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/tenants - null null
2025-09-12 16:46:52.125 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:46:52.126 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api)'
2025-09-12 16:46:52.127 +08:00 [INF] Route matched with {action = "GetAllTenants", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.TenantDataDto]]]] GetAllTenants() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:46:52.385 +08:00 [INF] 开始获取所有租户信息
2025-09-12 16:46:52.441 +08:00 [INF] 成功获取所有租户信息，共4个租户
2025-09-12 16:46:52.442 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.TenantDataDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 16:46:52.444 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api) in 315.4601ms
2025-09-12 16:46:52.444 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetAllTenants (MapStudio.Api)'
2025-09-12 16:46:52.593 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/tenants - 200 null application/json; charset=utf-8 470.3234ms
2025-09-12 16:46:52.598 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 16:46:52.598 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 16:46:52.600 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:46:52.603 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:46:52.604 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats/summary - 204 null null 5.7694ms
2025-09-12 16:46:52.610 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 11.9511ms
2025-09-12 16:46:52.610 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - null null
2025-09-12 16:46:52.616 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 16:46:52.622 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:46:52.624 +08:00 [INF] CORS policy execution successful.
2025-09-12 16:46:52.625 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:46:52.625 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:46:52.626 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.DTOs.DailyStatsDTO]] GetDailyStats(System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:46:52.626 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 16:46:52.687 +08:00 [INF] 开始获取日统计数据
2025-09-12 16:46:52.750 +08:00 [INF] 租户总数统计完成: 4
2025-09-12 16:46:52.750 +08:00 [INF] 开始统计每日操作数据
2025-09-12 16:46:52.880 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 16:46:52.882 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 251.8514ms
2025-09-12 16:46:52.883 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:46:53.049 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 432.6949ms
2025-09-12 16:46:53.092 +08:00 [INF] 查询到原始操作日志数据: 1393条
2025-09-12 16:46:53.093 +08:00 [INF] 按日期分组后的统计数据: 3天
2025-09-12 16:46:53.094 +08:00 [INF] 平均日操作次数: 464
2025-09-12 16:46:53.095 +08:00 [INF] 最大日操作数: 628
2025-09-12 16:46:53.162 +08:00 [INF] 今日操作总量: 629
2025-09-12 16:46:53.163 +08:00 [INF] 成功获取日统计数据：租户总数=4, 平均日操作次数=464, 最大日操作数=628, 操作总量=629
2025-09-12 16:46:53.165 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.DTOs.DailyStatsDTO'.
2025-09-12 16:46:53.167 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 539.4892ms
2025-09-12 16:46:53.168 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 16:46:53.296 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats/summary - 200 null application/json; charset=utf-8 686.2163ms
