<template>
  <aside 
    :class="sidebarClasses"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- 侧边栏头部 -->
    <div class="sidebar-header">
      <div :class="logoContainerClasses">
        <i class="fa-solid fa-map-marked-alt text-blue-600 text-xl"></i>
        <Transition name="fade-slide">
          <span v-if="sidebarOpen" class="ml-2 font-bold text-gray-900">地图工作室</span>
        </Transition>
      </div>
      <Transition name="fade">
        <button 
          v-if="sidebarOpen"
          @click="toggleSidebar"
          class="sidebar-toggle-btn"
        >
          <i class="fa-solid fa-angle-left"></i>
        </button>
      </Transition>
    </div>

    <!-- 导航菜单 -->
    <nav class="sidebar-nav">
      <div v-for="item in navItems" :key="item.title" class="nav-group">
        <button
          @click="toggleMenu(item.title)"
          :class="menuButtonClasses(item.title)"
          :title="!sidebarOpen ? item.title : ''"
        >
            <i :class="menuIconClasses(item.icon)"></i>
            <Transition name="fade-slide">
              <div v-if="sidebarOpen" class="flex items-center justify-between flex-1 ml-3">
                <span class="menu-title">{{ item.title }}</span>
                <i :class="expandIconClasses(item.title)"></i>
              </div>
            </Transition>
        </button>

        <!-- 子菜单 -->
        <Transition name="slide-down">
          <div 
            v-if="expandedMenus.includes(item.title) && sidebarOpen" 
            class="submenu-container"
          >
            <router-link
              v-for="child in item.children"
              :key="child.title"
              :to="child.path"
              :class="submenuItemClasses(child.path)"
              @click="handleItemClick(child.path)"
            >
              <span class="submenu-item-text">{{ child.title }}</span>
              <div v-if="$route.path === child.path" class="active-indicator"></div>
            </router-link>
          </div>
        </Transition>
      </div>
    </nav>

    <!-- 侧边栏底部（可选） -->
    <div v-if="sidebarOpen" class="sidebar-footer">
      <div class="text-xs text-gray-400 text-center">
        © 2024 地图工作室
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRoute } from 'vue-router';

// Props
interface Props {
  modelValue?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: true
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'menu-toggle': [title: string, expanded: boolean];
  'item-click': [path: string];
}>();

// 导航菜单数据
const navItems = [
  {
    title: '租户管理',
    icon: 'fa-building',
    children: [
      { title: '租户授权申请', path: '/tenant/auth-apply' },
      { title: '待审批清单', path: '/tenant/auth-approval-list' },
      { title: '授权进度查询', path: '/tenant/auth-progress' },
      { title: '授权销毁', path: '/tenant/auth-destroy' },
      { title: '服务状态查询', path: '/tenant/service-status' },
    ],
  },
  {
    title: '运营管理',
    icon: 'fa-line-chart',
    children: [
      { title: '运营日志清单', path: '/operation/log-list' },
      { title: '租户操作排行', path: '/operation/tenant-action-rank' },
      { title: '操作类型排行', path: '/operation/action-type-rank' },
      { title: '用户使用量查询', path: '/operation/user-usage' },
      { title: '告警设置', path: '/operation/alarm-setting' },
      { title: '访问统计报表', path: '/operation/access-stats' },
      { title: '日操作统计', path: '/operation/daily-stats' },
      { title: '月操作统计', path: '/operation/monthly-stats' },
    ],
  },
];

// 响应式状态
const sidebarOpen = ref(props.modelValue);
const expandedMenus = ref<string[]>(['租户管理', '运营管理']);
const isHovering = ref(false);

const route = useRoute();

// 计算属性
const sidebarClasses = computed(() => [
  'sidebar-container',
  {
    'sidebar-open': sidebarOpen.value,
    'sidebar-closed': !sidebarOpen.value,
    'sidebar-hovering': isHovering.value
  }
]);

const logoContainerClasses = computed(() => [
  'logo-container',
  {
    'logo-centered': !sidebarOpen.value
  }
]);

const menuButtonClasses = (title: string) => [
  'menu-button',
  {
    'menu-button-expanded': expandedMenus.value.includes(title),
    'menu-button-collapsed': !sidebarOpen.value
  }
];

const menuIconClasses = (icon: string) => [
  'fa-solid',
  icon,
  'menu-icon',
  {
    'menu-icon-centered': !sidebarOpen.value
  }
];

const expandIconClasses = (title: string) => [
  'fa-solid',
  'expand-icon',
  {
    'fa-angle-down': expandedMenus.value.includes(title),
    'fa-angle-right': !expandedMenus.value.includes(title)
  }
];

const submenuItemClasses = (path: string) => [
  'submenu-item',
  {
    'submenu-item-active': route.path === path,
    'submenu-item-inactive': route.path !== path
  }
];

// 方法
const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value;
  emit('update:modelValue', sidebarOpen.value);
};

const toggleMenu = (title: string) => {
  if (!sidebarOpen.value) {
    sidebarOpen.value = true;
    emit('update:modelValue', true);
  }
  
  const wasExpanded = expandedMenus.value.includes(title);
  expandedMenus.value = wasExpanded
    ? expandedMenus.value.filter(item => item !== title)
    : [...expandedMenus.value, title];
    
  emit('menu-toggle', title, !wasExpanded);
};

const handleItemClick = (path: string) => {
  emit('item-click', path);
  
  // 在移动端点击子菜单项后可以选择关闭侧边栏
  if (window.innerWidth < 768) {
    sidebarOpen.value = false;
    emit('update:modelValue', false);
  }
};

const handleMouseEnter = () => {
  isHovering.value = true;
};

const handleMouseLeave = () => {
  isHovering.value = false;
};

// 监听器
watch(() => props.modelValue, (newValue) => {
  sidebarOpen.value = newValue;
});

// 暴露方法给父组件
defineExpose({
  toggleSidebar,
  toggleMenu
});
</script>

<style scoped>
/* 侧边栏容器 */
.sidebar-container {
  @apply bg-white border-r border-gray-200 flex flex-col;
  width: 80px;
  transition: width 300ms cubic-bezier(0.4, 0, 0.2, 1);
  will-change: width;
}

.sidebar-open {
  width: 256px;
}

.sidebar-closed {
  width: 80px;
}

.sidebar-hovering {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 侧边栏头部 */
.sidebar-header {
  @apply flex items-center justify-between h-16 px-4 border-b border-gray-200;
}

.logo-container {
  @apply flex items-center;
  transition: justify-content 300ms ease;
}

.logo-centered {
  @apply justify-center w-full;
}

.sidebar-toggle-btn {
  @apply p-1 rounded-md text-gray-500 hover:text-gray-900 hover:bg-gray-100;
  transition: all 200ms ease;
}

.sidebar-toggle-btn:hover {
  transform: scale(1.1);
}

/* 导航区域 */
.sidebar-nav {
  @apply p-4 space-y-1 flex-1 overflow-y-auto;
}

.nav-group {
  @apply mb-2;
}

/* 菜单按钮 */
.menu-button {
  @apply flex items-center w-full px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-100;
  transition: all 200ms ease;
  position: relative;
}

.menu-button:hover {
  transform: translateX(2px);
  background-color: #f8fafc;
}

.menu-button-collapsed {
  @apply justify-center;
}

.menu-button::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 0 2px 2px 0;
  transform: scaleY(0);
  transition: transform 200ms ease;
}

.menu-button:hover::before {
  transform: scaleY(0.6);
}

.menu-button-expanded::before {
  transform: scaleY(1);
}

/* 菜单图标 */
.menu-icon {
  @apply text-gray-600;
  transition: all 200ms ease;
}

.menu-icon-centered {
  @apply mx-auto;
}

.menu-button:hover .menu-icon {
  @apply text-blue-600;
  transform: scale(1.1);
}

/* 菜单标题 */
.menu-title {
  @apply ml-3 flex-1;
}

/* 展开图标 */
.expand-icon {
  @apply ml-auto text-gray-400;
  transition: all 200ms ease;
}

.menu-button:hover .expand-icon {
  @apply text-blue-500;
}

/* 子菜单容器 */
.submenu-container {
  @apply mt-1 pl-10 space-y-1;
}

/* 子菜单项 */
.submenu-item {
  @apply block px-3 py-2 text-sm font-medium rounded-md relative;
  transition: all 200ms ease;
  position: relative;
}

.submenu-item-inactive {
  @apply text-gray-600 hover:text-blue-600 hover:bg-blue-50;
}

.submenu-item-active {
  @apply text-blue-600 bg-blue-50;
  box-shadow: inset 3px 0 0 #3b82f6;
}

.submenu-item:hover {
  transform: translateX(4px);
}

/* 子菜单指示器 */
.submenu-indicator {
  position: absolute;
  left: -20px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 1px;
  background-color: #d1d5db;
  transition: all 200ms ease;
}

.submenu-item-active .submenu-indicator {
  background-color: #3b82f6;
  width: 12px;
}

.submenu-item:hover .submenu-indicator {
  background-color: #3b82f6;
  width: 10px;
}

/* 子菜单标题 */
.submenu-title {
  @apply flex-1;
}

/* 激活状态点 */
.submenu-active-dot {
  @apply text-blue-600 text-xs ml-auto;
}

/* 侧边栏底部 */
.sidebar-footer {
  @apply p-4 border-t border-gray-200;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 200ms ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 200ms ease;
}

.fade-slide-enter-from,
.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-10px);
}

.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 300ms ease;
  overflow: hidden;
}

.slide-down-enter-from,
.slide-down-leave-to {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
}

.slide-down-enter-to,
.slide-down-leave-from {
  max-height: 500px;
  opacity: 1;
  transform: translateY(0);
}

.scale-enter-active,
.scale-leave-active {
  transition: all 150ms ease;
}

.scale-enter-from,
.scale-leave-to {
  opacity: 0;
  transform: scale(0);
}

/* 滚动条样式 */
.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: #e2e8f0;
  border-radius: 2px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: #cbd5e1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar-container {
    position: fixed;
    z-index: 40;
    height: 100vh;
    transform: translateX(-100%);
    transition: transform 300ms ease;
  }
  
  .sidebar-open {
    transform: translateX(0);
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .menu-button {
    border: 1px solid transparent;
  }
  
  .menu-button:hover,
  .menu-button:focus {
    border-color: #3b82f6;
  }
  
  .submenu-item-active {
    border: 2px solid #3b82f6;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  * {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
  }
}

/* 焦点样式 */
.menu-button:focus,
.submenu-item:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 键盘导航支持 */
.menu-button:focus-visible,
.submenu-item:focus-visible {
  box-shadow: 0 0 0 2px #3b82f6;
}
</style>