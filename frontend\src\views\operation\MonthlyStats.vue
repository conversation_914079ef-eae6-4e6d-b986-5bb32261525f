<template>
  <div class="p-6 space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">租户服务平均月操作统计</h1>
      <p class="mt-1 text-gray-500">展示各租户在过去12个月的平均月操作次数，支持同比、环比数据对比</p>
    </div>

    <!-- 筛选条件卡片 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            租户选择
          </label>
          <select
            v-model="selectedTenant"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option v-for="t in tenantOptions" :key="t.id" :value="t.id">{{ t.name }}</option>
          </select>
        </div>

        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            数据对比类型 <span class="text-red-500">*</span>
          </label>
          <div class="flex space-x-3">
            <button
              @click="comparisonType = 'yoy'"
              class="flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors"
              :class="comparisonType === 'yoy' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'"
            >
              <i class="fa-solid fa-chart-line mr-1"></i> 同比
            </button>
            <button
              @click="comparisonType = 'mom'"
              class="flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors"
              :class="comparisonType === 'mom' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'"
            >
              <i class="fa-solid fa-chart-column mr-1"></i> 环比
            </button>
          </div>
        </div>

        <div class="flex items-end">
          <button class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <i class="fa-solid fa-download mr-2"></i>
            导出报表
          </button>
        </div>
      </div>
    </div>

    <!-- 数据概览卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">年度总操作次数</p>
            <h3 class="mt-1 text-2xl font-bold text-gray-900">{{ totalOperations.toLocaleString() }}</h3>
            <p class="mt-1 text-xs text-green-500 flex items-center">
              <i class="fa-solid fa-arrow-up mr-1"></i> 15.8% 较上年
            </p>
          </div>
          <div class="p-3 bg-blue-100 rounded-lg">
            <i class="fa-solid fa-calculator text-blue-600 text-xl"></i>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">月均操作次数</p>
            <h3 class="mt-1 text-2xl font-bold text-gray-900">{{ monthlyAvg.toLocaleString() }}</h3>
            <p class="mt-1 text-xs text-green-500 flex items-center">
              <i class="fa-solid fa-arrow-up mr-1"></i> 12.3% 较上年同期
            </p>
          </div>
          <div class="p-3 bg-purple-100 rounded-lg">
            <i class="fa-solid fa-calendar-check text-purple-600 text-xl"></i>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">最高月操作次数</p>
            <h3 class="mt-1 text-2xl font-bold text-gray-900">{{ maxMonthOperations.toLocaleString() }}</h3>
            <p class="mt-1 text-xs text-gray-500 flex items-center">
              <span class="font-medium mr-1">{{ maxMonthName }}</span> 月
            </p>
          </div>
          <div class="p-3 bg-green-100 rounded-lg">
            <i class="fa-solid fa-trophy text-green-600 text-xl"></i>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">平均增长率</p>
            <h3 class="mt-1 text-2xl font-bold text-gray-900">{{ averageGrowth }}%</h3>
            <p class="mt-1 text-xs text-gray-500 flex items-center">
              <span class="font-medium mr-1">{{ comparisonType === 'yoy' ? '同比' : '环比' }}</span>平均增长率
            </p>
          </div>
          <div class="p-3 bg-orange-100 rounded-lg">
            <i class="fa-solid fa-chart-pie text-orange-600 text-xl"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- 月度趋势图表 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-gray-900">月度操作次数趋势</h2>
        <div class="flex space-x-2">
          <button class="text-sm text-gray-500 hover:text-gray-700">
            <i class="fa-solid fa-download mr-1"></i> 导出数据
          </button>
          <button class="text-sm text-gray-500 hover:text-gray-700">
            <i class="fa-solid fa-expand mr-1"></i> 全屏查看
          </button>
        </div>
      </div>

      <div class="h-96">
        <ChartBase :option="monthlyBarOptions" :loading="chartLoading" class="h-96" />
      </div>
    </div>

    <!-- 增长率趋势图表 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-gray-900">
          {{ comparisonType === 'yoy' ? '同比增长率' : '环比增长率' }} 趋势
        </h2>
        <div class="flex space-x-2">
          <button class="text-sm text-gray-500 hover:text-gray-700">
            <i class="fa-solid fa-download mr-1"></i> 导出数据
          </button>
        </div>
      </div>

      <div class="h-80">
        <ChartBase :option="growthLineOptions" :loading="chartLoading" class="h-80" />
      </div>
    </div>

    <!-- 租户月度对比图表 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-gray-900">租户月度操作次数对比</h2>
        <div class="flex space-x-2">
          <button class="text-sm text-gray-500 hover:text-gray-700">
            <i class="fa-solid fa-download mr-1"></i> 导出数据
          </button>
        </div>
      </div>

      <div class="h-96">
        <ChartBase :option="tenantCompareOptions" :loading="chartLoading" class="h-96" />
      </div>
    </div>

    <!-- 月度数据表格 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">月份</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作次数</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ comparisonType === 'yoy' ? '去年同期' : '上月' }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ comparisonType === 'yoy' ? '同比增长' : '环比增长' }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">环比增长</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">同比增长</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="m in monthlyData" :key="m.month" class="hover:bg-gray-50 transition-colors">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ m.month }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ m.operations.toLocaleString() }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ (comparisonType === 'yoy' ? m.prevYearOperations : m.prevMonthOperations).toLocaleString() }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="text-sm" :class="(comparisonType === 'yoy' ? m.yoyGrowth : m.momGrowth) >= 0 ? 'text-green-600' : 'text-red-600'">
                  {{ (comparisonType === 'yoy' ? m.yoyGrowth : m.momGrowth) >= 0 ? '+' : '' }}{{ comparisonType === 'yoy' ? m.yoyGrowth : m.momGrowth }}%
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="text-sm" :class="m.momGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                  {{ m.momGrowth >= 0 ? '+' : '' }}{{ m.momGrowth }}%
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="text-sm" :class="m.yoyGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                  {{ m.yoyGrowth >= 0 ? '+' : '' }}{{ m.yoyGrowth }}%
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import ChartBase from '../../components/ChartBase.vue'
import type { EChartsOption } from 'echarts'

type ComparisonType = 'yoy' | 'mom'

interface TenantOption { id: string; name: string }
interface MonthlyItem {
  month: string
  operations: number
  prevYearOperations: number
  prevMonthOperations: number
  yoyGrowth: number
  momGrowth: number
}
interface TenantSeries {
  id: string
  name: string
  color: string
  data: number[]
}

const tenantOptions: TenantOption[] = [
  { id: '', name: '全部租户' },
  { id: 'TENANT-2025001', name: '智慧城市科技有限公司' },
  { id: 'TENANT-2025002', name: '未来交通研究院' },
  { id: 'TENANT-2025003', name: '绿色能源集团' },
  { id: 'TENANT-2025004', name: '数字农业科技有限公司' },
  { id: 'TENANT-2025005', name: '智慧医疗系统集成商' }
]

const months = ['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月']

function generateMonthlyData(): MonthlyItem[] {
  const operationsData = months.map(() => Math.floor(Math.random() * 5000) + 5000)
  return months.map((month, index) => {
    const operations = operationsData[index]
    const prevYearOperations = Math.floor(operations * (0.8 + Math.random() * 0.4))
    const prevMonthOperations = index === 0
      ? Math.floor(operations * (0.8 + Math.random() * 0.4))
      : Math.floor(operationsData[index - 1] * (0.8 + Math.random() * 0.4))
    const yoyGrowth = parseFloat((((operations - prevYearOperations) / prevYearOperations) * 100).toFixed(2))
    const momGrowth = parseFloat((((operations - prevMonthOperations) / prevMonthOperations) * 100).toFixed(2))
    return {
      month,
      operations,
      prevYearOperations,
      prevMonthOperations,
      yoyGrowth,
      momGrowth
    }
  })
}

function generateTenantComparisonData(): TenantSeries[] {
  const top = tenantOptions.slice(1, 6)
  const colors = ['#165DFF', '#36CFC9', '#722ED1', '#FF7D00', '#F53F3F']
  return top.map((t, idx) => ({
    id: t.id,
    name: t.name,
    color: colors[idx % colors.length],
    data: months.map(() => Math.floor(Math.random() * 1000) + 500)
  }))
}

const selectedTenant = ref<string>('')
const comparisonType = ref<ComparisonType>('yoy')
const chartLoading = ref(true)

// 数据
const monthlyData = generateMonthlyData()
const tenantComparisonData = generateTenantComparisonData()

// 组件挂载
onMounted(() => {
  // 模拟数据加载
  setTimeout(() => {
    chartLoading.value = false
  }, 500)
})

// 概览指标
const totalOperations = computed(() => monthlyData.reduce((s, it) => s + it.operations, 0))
const monthlyAvg = computed(() => Math.round(totalOperations.value / monthlyData.length))
const maxMonthOperations = computed(() => Math.max(...monthlyData.map(m => m.operations)))
const maxMonthName = computed(() => {
  const max = maxMonthOperations.value
  return monthlyData.find(m => m.operations === max)?.month ?? ''
})
const averageGrowth = computed(() => {
  const sum = monthlyData.reduce((s, it) => s + (comparisonType.value === 'yoy' ? it.yoyGrowth : it.momGrowth), 0)
  return Math.floor(sum / monthlyData.length)
})

// 图表：月度操作次数 双柱
const monthlyBarOptions = computed<EChartsOption>(() => {
  return {
    color: ['#165DFF', '#86909C'],
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: (params: any[]) => {
        const cur = params.find(p => p.seriesName === '当月操作次数')
        const cmp = params.find(p => p.seriesName === (comparisonType.value === 'yoy' ? '去年同期操作次数' : '上月操作次数'))
        const lines = [
          `${params[0].axisValue}`,
          cur ? `${cur.marker} ${cur.seriesName}：${cur.value} 次` : '',
          cmp ? `${cmp.marker} ${cmp.seriesName}：${cmp.value} 次` : ''
        ].filter(Boolean)
        return lines.join('<br/>')
      }
    },
    legend: { bottom: 0 },
    grid: { top: 30, left: 40, right: 40, bottom: 50, containLabel: true },
    xAxis: {
      type: 'category',
      data: months,
      axisTick: { show: false },
      axisLine: { lineStyle: { color: '#e5e7eb' } },
      axisLabel: { color: '#6b7280' }
    },
    yAxis: {
      type: 'value',
      axisTick: { show: false },
      splitLine: { lineStyle: { type: 'dashed', color: '#e5e7eb' } },
      axisLabel: { color: '#6b7280' }
    },
    series: [
      {
        name: '当月操作次数',
        type: 'bar',
        data: monthlyData.map(m => m.operations),
        barMaxWidth: 22,
        itemStyle: { borderRadius: [4, 4, 0, 0] }
      },
      {
        name: comparisonType.value === 'yoy' ? '去年同期操作次数' : '上月操作次数',
        type: 'bar',
        data: monthlyData.map(m => comparisonType.value === 'yoy' ? m.prevYearOperations : m.prevMonthOperations),
        barMaxWidth: 22,
        itemStyle: { borderRadius: [4, 4, 0, 0] }
      }
    ]
  }
})

// 图表：增长率 折线 + 基准线
const growthLineOptions = computed<EChartsOption>(() => {
  const seriesData = monthlyData.map(m => (comparisonType.value === 'yoy' ? m.yoyGrowth : m.momGrowth))
  return {
    color: [comparisonType.value === 'yoy' ? '#165DFF' : '#722ED1'],
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'line' },
      formatter: (params: any[]) => {
        const p = params[0]
        return `${p.axisValue}<br/>${p.marker} ${p.seriesName}：${p.value}%`
      }
    },
    grid: { top: 30, left: 40, right: 40, bottom: 40, containLabel: true },
    xAxis: {
      type: 'category',
      data: months,
      axisTick: { show: false },
      axisLine: { lineStyle: { color: '#e5e7eb' } },
      axisLabel: { color: '#6b7280' }
    },
    yAxis: {
      type: 'value',
      axisLabel: { formatter: '{value}%', color: '#6b7280' },
      splitLine: { lineStyle: { type: 'dashed', color: '#e5e7eb' } }
    },
    series: [
      {
        name: comparisonType.value === 'yoy' ? '同比增长率' : '环比增长率',
        type: 'line',
        smooth: true,
        data: seriesData,
        symbolSize: 4,
        lineStyle: { width: 2 },
        emphasis: { focus: 'series' },
        markLine: {
          symbol: 'none',
          data: [{ yAxis: 0 }],
          lineStyle: { type: 'dashed', color: '#e5e7eb' }
        }
      }
    ]
  }
})

// 图表：租户月度对比 多折线
const tenantCompareOptions = computed<EChartsOption>(() => {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'line' },
      formatter: (params: any[]) => {
        const lines = [`${params[0].axisValue}`]
        params.forEach(p => {
          lines.push(`${p.marker} ${p.seriesName}：${p.value} 次`)
        })
        return lines.join('<br/>')
      }
    },
    legend: { bottom: 0 },
    grid: { top: 30, left: 40, right: 40, bottom: 50, containLabel: true },
    xAxis: {
      type: 'category',
      data: months,
      axisTick: { show: false },
      axisLine: { lineStyle: { color: '#e5e7eb' } },
      axisLabel: { color: '#6b7280' }
    },
    yAxis: {
      type: 'value',
      axisLabel: { color: '#6b7280' },
      splitLine: { lineStyle: { type: 'dashed', color: '#e5e7eb' } }
    },
    series: tenantComparisonData.map(t => ({
      name: t.name,
      type: 'line',
      smooth: true,
      data: t.data,
      symbolSize: 4,
      lineStyle: { width: 2, color: t.color },
      emphasis: { focus: 'series' }
    }))
  }
})
</script>