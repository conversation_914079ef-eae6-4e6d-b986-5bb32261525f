using SqlSugar;
using MapStudio.Api.Services.Interfaces;
using MapStudio.Api.Models.DTOs;
using MapStudio.Api.Models.Entities;

namespace MapStudio.Api.Services.Implementations
{
    /// <summary>
    /// 短信模板服务实现
    /// </summary>
    public class SmsTemplateService : ISmsTemplateService
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<SmsTemplateService> _logger;

        public SmsTemplateService(ISqlSugarClient db, ILogger<SmsTemplateService> logger)
        {
            _db = db;
            _logger = logger;
        }

        /// <summary>
        /// 获取短信模板
        /// </summary>
        /// <returns>短信模板设置</returns>
        public async Task<SmsTemplateSettingDto> GetSmsTemplateAsync()
        {
            try
            {
                var template = await _db.Queryable<SmsTemplateSetting>()
                    .FirstAsync();

                if (template == null)
                {
                    return new SmsTemplateSettingDto
                    {
                        TempId = 0,
                        TemplateName = string.Empty,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };
                }

                return new SmsTemplateSettingDto
                {
                    TempId = template.TempId,
                    TemplateName = template.TemplateName,
                    CreatedAt = template.CreatedAt,
                    UpdatedAt = template.UpdatedAt
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取短信模板失败");
                throw;
            }
        }

        /// <summary>
        /// 保存短信模板（新增或修改）
        /// </summary>
        /// <param name="request">保存短信模板请求</param>
        /// <returns>保存后的短信模板设置</returns>
        public async Task<SmsTemplateSettingDto> SaveSmsTemplateAsync(SaveSmsTemplateRequestDto request)
        {
            try
            {
                // 检查表中是否有数据
                var existingTemplate = await _db.Queryable<SmsTemplateSetting>()
                    .FirstAsync();

                if (existingTemplate != null)
                {
                    // 修改现有数据
                    existingTemplate.TemplateName = request.TemplateName;
                    existingTemplate.UpdatedAt = DateTime.Now;

                    await _db.Updateable(existingTemplate).ExecuteCommandAsync();

                    return new SmsTemplateSettingDto
                    {
                        TempId = existingTemplate.TempId,
                        TemplateName = existingTemplate.TemplateName,
                        CreatedAt = existingTemplate.CreatedAt,
                        UpdatedAt = existingTemplate.UpdatedAt
                    };
                }
                else
                {
                    // 插入新数据
                    var newTemplate = new SmsTemplateSetting
                    {
                        TemplateName = request.TemplateName,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };

                    var insertedId = await _db.Insertable(newTemplate).ExecuteReturnIdentityAsync();
                    newTemplate.TempId = insertedId;

                    return new SmsTemplateSettingDto
                    {
                        TempId = newTemplate.TempId,
                        TemplateName = newTemplate.TemplateName,
                        CreatedAt = newTemplate.CreatedAt,
                        UpdatedAt = newTemplate.UpdatedAt
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存短信模板失败");
                throw;
            }
        }
    }
}