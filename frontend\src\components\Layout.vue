<template>
  <div class="flex h-screen bg-gray-50 overflow-hidden">
    <!-- 侧边栏 -->
    <aside 
      :class="[
        'bg-white border-r border-gray-200 transition-all duration-300 ease-in-out',
        sidebarOpen ? 'w-64' : 'w-20'
      ]"
    >
      <div class="flex items-center justify-between h-16 px-4 border-b border-gray-200">
        <div :class="['flex items-center', !sidebarOpen && 'justify-center w-full']">
          <i class="fa-solid fa-map-marked-alt text-blue-600 text-xl"></i>
          <span v-if="sidebarOpen" class="ml-2 font-bold text-gray-900">地图工作室</span>
        </div>
        <button 
          @click="toggleSidebar"
          :class="['p-1 rounded-md text-gray-500 hover:text-gray-900 hover:bg-gray-100', !sidebarOpen && 'hidden']"
        >
          <i class="fa-solid fa-angle-left"></i>
        </button>
      </div>

      <nav class="p-4 space-y-1">
        <div v-for="item in navItems" :key="item.title" class="mb-2">
          <button
            @click="toggleMenu(item.title)"
            class="flex items-center w-full px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-100"
          >
            <i :class="`fa-solid ${item.icon} ${!sidebarOpen && 'mx-auto'}`"></i>
            <template v-if="sidebarOpen">
              <span class="ml-3">{{ item.title }}</span>
              <i :class="`fa-solid ${expandedMenus.includes(item.title) ? 'fa-angle-down' : 'fa-angle-right'} ml-auto`"></i>
            </template>
          </button>

          <div v-if="expandedMenus.includes(item.title) && sidebarOpen" class="mt-1 pl-10 space-y-1">
            <router-link
              v-for="child in item.children"
              :key="child.title"
              :to="child.path"
              :class="[
                'block px-3 py-2 text-sm font-medium rounded-md',
                $route.path === child.path
                  ? 'text-blue-600 bg-blue-50'
                  : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
              ]"
            >
              {{ child.title }}
            </router-link>
          </div>
        </div>
      </nav>
    </aside>

    <!-- 主内容区 -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- 顶部导航栏 -->
      <header class="bg-white border-b border-gray-200 shadow-sm z-10">
        <div class="flex items-center justify-between h-16 px-6">
          <div class="flex items-center">
            <button 
              @click="toggleSidebar"
              class="p-1 rounded-md text-gray-500 hover:text-gray-900 hover:bg-gray-100 mr-4"
            >
              <i class="fa-solid fa-bars"></i>
            </button>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <i class="fa-solid fa-search text-gray-400"></i>
              </div>
              <input
                type="text"
                class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="搜索..."
                v-model="searchQuery"
              />
            </div>
          </div>

          <div class="flex items-center space-x-4">
            <button class="p-1 rounded-full text-gray-500 hover:text-gray-900 hover:bg-gray-100 relative">
              <i class="fa-solid fa-bell"></i>
              <span class="absolute top-0 right-0 block w-2 h-2 rounded-full bg-red-500"></span>
            </button>
            
            <div class="relative group">
              <button class="flex items-center text-sm focus:outline-none">
                <img
                  class="h-8 w-8 rounded-full object-cover"
                  src="https://space.coze.cn/api/coze_space/gen_image?image_size=square&prompt=user%20avatar%2C%20professional%20business%20person&sign=d2a2062106902f7ca3c28e345df86321"
                  alt="User avatar"
                />
                <span class="ml-2 hidden md:block text-sm font-medium text-gray-700">管理员</span>
                <i class="fa-solid fa-angle-down ml-1 hidden md:block"></i>
              </button>
              
              <div class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 py-1 z-10 hidden group-hover:block">
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  <i class="fa-solid fa-user mr-2"></i>个人资料
                </a>
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  <i class="fa-solid fa-cog mr-2"></i>设置
                </a>
                <button
                  @click="handleLogout"
                  class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <i class="fa-solid fa-sign-out-alt mr-2"></i>退出登录
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="flex-1 overflow-y-auto bg-gray-50">
        <div class="p-4">
          <router-view />
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// 导航菜单数据
const navItems = [
  {
    title: '租户管理',
    icon: 'fa-building',
    children: [
      { title: '租户授权申请', path: '/tenant/auth-apply' },
      { title: '待审批清单', path: '/tenant/auth-approval-list' },
      { title: '授权进度查询', path: '/tenant/auth-progress' },
      { title: '授权销毁', path: '/tenant/auth-destroy' },
      { title: '服务状态监控', path: '/tenant/service-status' },
    ],
  },
  {
    title: '运营管理',
    icon: 'fa-line-chart',
    children: [
      { title: '运营日志清单', path: '/operation/log-list' },
      { title: '租户操作排行', path: '/operation/tenant-action-rank' },
      { title: '操作类型排行', path: '/operation/action-type-rank' },
      { title: '用户使用量查询', path: '/operation/user-usage' },
      { title: '告警设置', path: '/operation/alarm-setting' },
      { title: '访问统计报表', path: '/operation/access-stats' },
      { title: '日操作统计', path: '/operation/daily-stats' },
      { title: '月操作统计', path: '/operation/monthly-stats' },
    ],
  },
]

// 响应式数据
const sidebarOpen = ref(true)
const expandedMenus = ref(['租户管理', '运营管理'])
const isLoading = ref(false)
const searchQuery = ref('')

// 使用 router
const route = useRoute()
const router = useRouter()

// 切换菜单展开/折叠
const toggleMenu = (title: string) => {
  const index = expandedMenus.value.indexOf(title)
  if (index > -1) {
    expandedMenus.value.splice(index, 1)
  } else {
    expandedMenus.value.push(title)
  }
}

// 切换侧边栏显示/隐藏
const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value
}

// 处理登出
const handleLogout = () => {
  // 简化登出逻辑，直接跳转到首页
  router.push('/')
}

// 监听路由变化，显示加载状态
watch(() => route.path, () => {
  isLoading.value = true
  
  // 设置一个短暂延迟确保加载状态显示
  setTimeout(() => {
    isLoading.value = false
  }, 300)
})
</script>

<style scoped>
/* 组件特定样式 */
.group:hover .group-hover\:block {
  display: block;
}
</style>
