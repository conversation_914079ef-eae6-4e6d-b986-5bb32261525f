using MapStudio.Api.Models.DTOs;

namespace MapStudio.Api.Services.Interfaces;

public interface IMetadataService
{
    Task<List<ServiceTypeOption>> GetServiceTypesAsync();
    Task<List<PermissionScopeOption>> GetPermissionScopesAsync();
    Task<List<AuthPeriodOption>> GetAuthPeriodsAsync();
    Task<List<TenantSizeOption>> GetTenantSizesAsync();
    Task<List<IndustryOption>> GetIndustriesAsync();
}