{"name": "map-studio-saas", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite --host --port 3001", "build": "vue-tsc -b && vite build", "preview": "vite preview", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "playwright:install": "playwright install"}, "dependencies": {"@fortawesome/fontawesome-free": "^7.0.0", "axios": "^1.11.0", "clsx": "^2.1.1", "echarts": "^5.5.0", "markdown-it": "^14.1.0", "pinia": "^2.2.8", "tailwind-merge": "^3.0.2", "vue": "^3.5.18", "vue-router": "^4.5.1", "zod": "^3.24.2"}, "devDependencies": {"@playwright/test": "^1.55.0", "@types/node": "^22.10.5", "@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "~5.7.2", "vite": "^6.2.0", "vue-tsc": "^3.0.5"}}