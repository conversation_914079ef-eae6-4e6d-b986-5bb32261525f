# 短信告警功能架构设计文档

## 1. 需求分析

根据前端`AlarmSetting.vue`组件的需求，需要实现短信告警配置功能，包括：
1. 短信模板编辑和保存
2. 接收短信手机号的添加、编辑、删除和保存
3. 短信告警级别的配置

## 2. 系统架构设计

### 2.1 整体架构

```
┌─────────────────┐    HTTP API    ┌──────────────────┐    Service Layer    ┌──────────────────┐
│  Vue Frontend   │───────────────→│ OperationController│───────────────────→│ OperationService │
└─────────────────┘                └──────────────────┘                     └──────────────────┘
                                             │                                       │
                                             │                                       │
                                             ↓                                       ↓
                                  ┌──────────────────┐                  ┌──────────────────┐
                                  │  SmsAlarmSetting │                  │  SmsAlarmSetting │
                                  │     Entity       │←────────────────→│     Entity       │
                                  └──────────────────┘    Repository    └──────────────────┘
                                             │
                                             │
                                             ↓
                                  ┌──────────────────┐
                                  │     Database     │
                                  │  (ms_sms_alarm_settings)│
                                  └──────────────────┘
```

### 2.2 数据库设计

#### 2.2.1 SmsAlarmSetting实体表 (ms_sms_alarm_settings)

| 字段名 | 类型 | 约束 | 描述 |
|-------|------|------|------|
| Id | VARCHAR(36) | PRIMARY KEY | 设置ID |
| SmsTemplate | TEXT | NOT NULL | 短信模板内容 |
| PhoneList | JSON | NULLABLE | 手机号列表 (JSON格式) |
| IsActive | BOOLEAN | NOT NULL | 是否激活 |
| CreatedAt | DATETIME | NOT NULL | 创建时间 |
| UpdatedAt | DATETIME | NOT NULL | 更新时间 |

#### 2.2.2 PhoneList JSON结构示例

```json
[
  {
    "id": "uuid-string",
    "phone": "13800138000",
    "enabled": true,
    "levels": ["system_failure", "service_exception"],
    "addedAt": "2025-01-01T12:00:00Z"
  }
]
```

### 2.3 DTO对象设计

#### 2.3.1 请求DTOs

1. `SmsTemplateUpdateRequest` - 短信模板更新请求
2. `SmsPhoneRequest` - 添加手机号请求
3. `SmsPhoneUpdateRequest` - 更新手机号请求

#### 2.3.2 响应DTOs

1. `AlarmSmsResponse` - 短信告警设置响应
2. `SmsTemplateUpdateResponse` - 短信模板更新响应
3. `SmsPhoneResponse` - 手机号添加响应
4. `SmsPhoneUpdateResponse` - 手机号更新响应

### 2.4 API接口设计

#### 2.4.1 短信告警设置相关接口

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/operation/alarm-settings/sms` | 获取短信告警设置 |
| PUT | `/api/operation/alarm-settings/sms-template` | 更新短信模板 |
| POST | `/api/operation/alarm-settings/sms-phones` | 添加接收手机号 |
| PUT | `/api/operation/alarm-settings/sms-phones/{id}` | 更新接收手机号 |
| DELETE | `/api/operation/alarm-settings/sms-phones/{id}` | 删除接收手机号 |

## 3. 实现计划

### 3.1 数据库实体扩展

创建新的`SmsAlarmSetting`实体类，对应数据库表`ms_sms_alarm_settings`。

### 3.2 DTO对象创建

在`backend/Models/DTOs/OperationDTOs.cs`中添加短信告警相关的DTO对象。

### 3.3 Service层扩展

扩展`IOperationService`接口和`OperationService`实现类，添加短信告警相关方法。

### 3.4 Controller层扩展

扩展`OperationController`，添加短信告警相关API接口。

### 3.5 测试验证

通过API测试工具验证短信告警功能接口的正确性。

## 4. 详细设计

### 4.1 SmsAlarmSetting实体类

```csharp
[SugarTable("ms_sms_alarm_settings")]
public class SmsAlarmSetting
{
    [SugarColumn(IsPrimaryKey = true, Length = 36)]
    public string Id { get; set; } = string.Empty;

    [SugarColumn(Length = 2000, IsNullable = false)]
    public string SmsTemplate { get; set; } = string.Empty;

    [SugarColumn(ColumnDataType = "json", IsNullable = true)]
    public string? PhoneList { get; set; }

    [SugarColumn(IsNullable = false)]
    public bool IsActive { get; set; } = true;

    [SugarColumn(IsNullable = false)]
    public DateTime CreatedAt { get; set; }

    [SugarColumn(IsNullable = false)]
    public DateTime UpdatedAt { get; set; }
}
```

### 4.2 DTO对象

#### 4.2.1 SmsPhoneDto

```csharp
public class SmsPhoneDto
{
    public string Id { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public bool Enabled { get; set; }
    public List<string> Levels { get; set; } = new();
    public DateTime AddedAt { get; set; }
}
```

#### 4.2.2 AlarmSmsResponse

```csharp
public class AlarmSmsResponse
{
    public string Id { get; set; } = string.Empty;
    public string SmsTemplate { get; set; } = string.Empty;
    public List<SmsPhoneDto> PhoneList { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime UpdatedAt { get; set; }
}
```

#### 4.2.3 请求DTOs

```csharp
public class SmsTemplateUpdateRequest
{
    public string SmsTemplate { get; set; } = string.Empty;
}

public class SmsPhoneRequest
{
    public string Phone { get; set; } = string.Empty;
    public List<string> Levels { get; set; } = new();
}

public class SmsPhoneUpdateRequest
{
    public bool Enabled { get; set; }
    public List<string> Levels { get; set; } = new();
}
```

#### 4.2.4 响应DTOs

```csharp
public class SmsTemplateUpdateResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime UpdatedAt { get; set; }
}

public class SmsPhoneResponse
{
    public string Id { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public bool Enabled { get; set; }
    public List<string> Levels { get; set; } = new();
    public string Message { get; set; } = string.Empty;
}

public class SmsPhoneUpdateResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
}
```

### 4.3 Service接口扩展

```csharp
// IOperationService接口扩展
Task<AlarmSmsResponse> GetAlarmSmsSettingsAsync();
Task<SmsTemplateUpdateResponse> UpdateSmsTemplateAsync(string smsTemplate);
Task<SmsPhoneResponse> AddSmsPhoneAsync(string phone, string[] levels);
Task<SmsPhoneUpdateResponse> UpdateSmsPhoneAsync(string id, bool enabled, string[] levels);
Task DeleteSmsPhoneAsync(string id);
```

### 4.4 Controller接口扩展

```csharp
// OperationController扩展
/// <summary>
/// 获取短信告警设置
/// </summary>
[HttpGet("alarm-settings/sms")]
public async Task<ActionResult<ApiResponse<AlarmSmsResponse>>> GetAlarmSmsSettings()

/// <summary>
/// 更新短信模板
/// </summary>
[HttpPut("alarm-settings/sms-template")]
public async Task<ActionResult<ApiResponse<SmsTemplateUpdateResponse>>> UpdateSmsTemplate([FromBody] string smsTemplate)

/// <summary>
/// 添加接收手机号
/// </summary>
[HttpPost("alarm-settings/sms-phones")]
public async Task<ActionResult<ApiResponse<SmsPhoneResponse>>> AddSmsPhone([FromBody] SmsPhoneRequest request)

/// <summary>
/// 更新接收手机号
/// </summary>
[HttpPut("alarm-settings/sms-phones/{id}")]
public async Task<ActionResult<ApiResponse<SmsPhoneUpdateResponse>>> UpdateSmsPhone(string id, [FromBody] SmsPhoneUpdateRequest request)

/// <summary>
/// 删除接收手机号
/// </summary>
[HttpDelete("alarm-settings/sms-phones/{id}")]
public async Task<ActionResult<ApiResponse>> DeleteSmsPhone(string id)
```

## 5. 安全考虑

1. 所有接口都使用`[ConditionalAuthorize]`属性进行身份验证
2. 对手机号格式进行验证
3. 对短信模板内容长度进行限制
4. 对JSON数据进行安全解析，防止注入攻击

## 6. 性能优化

1. 使用数据库索引优化查询性能
2. 对PhoneList JSON字段进行合理的大小限制
3. 实现缓存机制减少数据库访问频率

## 7. 测试计划

1. 单元测试：测试Service层各方法的正确性
2. 集成测试：测试Controller层API接口
3. 数据库测试：验证数据的正确存储和检索
4. 性能测试：测试高并发情况下的响应时间

## 8. 实现步骤

1. 创建`SmsAlarmSetting`实体类文件
2. 扩展`OperationDTOs.cs`文件，添加相关DTO对象
3. 扩展`IOperationService`接口
4. 实现`OperationService`中的短信告警相关方法
5. 扩展`OperationController`，添加相关API接口
6. 更新`DatabaseInitializer`，添加新表的初始化
7. 测试验证功能实现