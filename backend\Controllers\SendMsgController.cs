using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MapStudio.Api.Services.Interfaces;
using MapStudio.Api.Models.DTOs;
using MapStudio.Api.Models.ViewModels;
using MapStudio.Api.Attributes;
using System.Net.Http;
using System.Xml.Serialization;
using System.Text;
using System.Xml;
using Microsoft.Extensions.Configuration;

namespace MapStudio.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[ConditionalAuthorize]
public class SendMsgController : ControllerBase
{
    private readonly ISmsPhoneService _smsPhoneService;
    private readonly ISmsTemplateService _smsTemplateService;
    private readonly ILogger<SendMsgController> _logger;
    private readonly HttpClient _httpClient;
    private readonly string _smsServiceUrl;

    public SendMsgController(ISmsPhoneService smsPhoneService, ISmsTemplateService smsTemplateService, ILogger<SendMsgController> logger, HttpClient httpClient, IConfiguration configuration)
    {
        _smsPhoneService = smsPhoneService;
        _smsTemplateService = smsTemplateService;
        _logger = logger;
        _httpClient = httpClient;
        _smsServiceUrl = configuration["SmsService:ServiceUrl"];
    }

    /// <summary>
    /// 获取短信接收手机号列表，按创建时间倒序排序
    /// </summary>
    [HttpGet("phones")]
    public async Task<ActionResult<ApiResponse<List<SmsPhoneSettingDto>>>> GetSmsPhones()
    {
        try
        {
            var result = await _smsPhoneService.GetSmsPhoneSettingsAsync();
            if (!result.Success)
            {
                return BadRequest(new ApiResponse<List<SmsPhoneSettingDto>>
                {
                    Success = false,
                    Message = result.Message,
                    ErrorCode = result.ErrorCode
                });
            }

            return Ok(new ApiResponse<List<SmsPhoneSettingDto>>
            {
                Success = true,
                Data = result.Data,
                Message = "获取短信接收手机号列表成功"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取短信接收手机号列表失败");
            return StatusCode(500, new ApiResponse<List<SmsPhoneSettingDto>>
            {
                Success = false,
                Message = "服务器内部错误",
                ErrorCode = "INTERNAL_ERROR"
            });
        }
    }

    /// <summary>
    /// 添加短信接收手机号
    /// </summary>
    [HttpPost("phones")]
    public async Task<ActionResult<ApiResponse<SmsPhoneSettingDto>>> AddSmsPhone([FromBody] AddSmsPhoneSettingRequest request)
    {
        try
        {
            var result = await _smsPhoneService.AddSmsPhoneSettingAsync(request.Phone, request.Levels);
            if (!result.Success)
            {
                return BadRequest(new ApiResponse<SmsPhoneSettingDto>
                {
                    Success = false,
                    Message = result.Message,
                    ErrorCode = result.ErrorCode
                });
            }

            return Ok(new ApiResponse<SmsPhoneSettingDto>
            {
                Success = true,
                Data = result.Data,
                Message = "添加短信手机号成功"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加短信手机号失败");
            return StatusCode(500, new ApiResponse<SmsPhoneSettingDto>
            {
                Success = false,
                Message = "服务器内部错误",
                ErrorCode = "INTERNAL_ERROR"
            });
        }
    }

    /// <summary>
    /// 更新短信接收手机号状态
    /// </summary>
    [HttpPut("phones/{id}")]
    public async Task<ActionResult<ApiResponse<SmsPhoneSettingDto>>> UpdateSmsPhoneStatus(int id, UpdateSmsPhoneSettingRequest request)
    {
        try
        {
            var result = await _smsPhoneService.UpdateSmsPhoneSettingAsync(id, request.Status, request.Levels);
            if (!result.Success)
            {
                return BadRequest(new ApiResponse<SmsPhoneSettingDto>
                {
                    Success = false,
                    Message = result.Message,
                    ErrorCode = result.ErrorCode
                });
            }

            return Ok(new ApiResponse<SmsPhoneSettingDto>
            {
                Success = true,
                Data = result.Data,
                Message = "更新短信手机号状态成功"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新短信手机号状态失败");
            return StatusCode(500, new ApiResponse<SmsPhoneSettingDto>
            {
                Success = false,
                Message = "服务器内部错误",
                ErrorCode = "INTERNAL_ERROR"
            });
        }
    }

    /// <summary>
    /// 删除短信接收手机号
    /// </summary>
    [HttpDelete("phones/{id}")]
    public async Task<ActionResult<ApiResponse<bool>>> DeleteSmsPhone(int id)
    {
        try
        {
            var result = await _smsPhoneService.DeleteSmsPhoneSettingAsync(id);
            if (!result.Success)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Success = false,
                    Message = result.Message,
                    ErrorCode = result.ErrorCode
                });
            }

            return Ok(new ApiResponse<bool>
            {
                Success = true,
                Data = true,
                Message = "删除短信手机号成功"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除短信手机号失败");
            return StatusCode(500, new ApiResponse<bool>
            {
                Success = false,
                Message = "服务器内部错误",
                ErrorCode = "INTERNAL_ERROR"
            });
        }
    }

    /// <summary>
    /// 获取短信模板
    /// </summary>
    [HttpGet("sms-template")]
    public async Task<ActionResult<ApiResponse<List<SmsTemplateSettingDto>>>> GetSmsTemplate()
    {
        try
        {
            var result = await _smsTemplateService.GetSmsTemplateAsync();

            // 将单个对象放入数组中，以匹配前端期望的数据结构
            var templateList = new List<SmsTemplateSettingDto>();
            if (result != null && !string.IsNullOrEmpty(result.TemplateName))
            {
                templateList.Add(result);
            }

            return Ok(new ApiResponse<List<SmsTemplateSettingDto>>
            {
                Success = true,
                Data = templateList,
                Message = "获取短信模板成功"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取短信模板失败");
            return StatusCode(500, new ApiResponse<List<SmsTemplateSettingDto>>
            {
                Success = false,
                Message = "服务器内部错误",
                ErrorCode = "INTERNAL_ERROR"
            });
        }
    }

    /// <summary>
    /// 保存短信模板
    /// </summary>
    [HttpPost("sms-template")]
    public async Task<ActionResult<ApiResponse<SmsTemplateSettingDto>>> SaveSmsTemplate([FromBody] SaveSmsTemplateRequestDto request)
    {
        try
        {
            var result = await _smsTemplateService.SaveSmsTemplateAsync(request);

            return Ok(new ApiResponse<SmsTemplateSettingDto>
            {
                Success = true,
                Data = result,
                Message = "保存短信模板成功"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存短信模板失败");
            return StatusCode(500, new ApiResponse<SmsTemplateSettingDto>
            {
                Success = false,
                Message = "服务器内部错误",
                ErrorCode = "INTERNAL_ERROR"
            });
        }
    }

    /// <summary>
    /// 发送测试短信
    /// </summary>
    [HttpPost("send-test-sms")]
    public async Task<ActionResult<ApiResponse<SendSmsResponseDto>>> SendTestSms([FromBody] SendSmsRequestDto request)
    {
        try
        {
            _logger.LogInformation("开始发送测试短信: Type={Type}, Modular={Modular}, Function={Function}", request.Type, request.Modular, request.Function);

            // 将SendSmsRequestDto转换为XmlSmsRequest对象
            var xmlRequest = new XmlSmsRequest
            {
                Type = request.Type,
                Modular = request.Modular,
                Function = request.Function,
                ReceiveMans = request.ReceiveMans,
                ReceivePns = request.ReceivePns,
                CcMans = request.CcMans,
                CcPns = request.CcPns,
                SendMan = request.SendMan,
                Message = request.Message
            };

            // 序列化为XML格式
            string xmlContent;
            using (var stringWriter = new StringWriter())
            {
                var serializer = new XmlSerializer(typeof(XmlSmsRequest));
                serializer.Serialize(stringWriter, xmlRequest);
                xmlContent = stringWriter.ToString();
            }

            // 调用webservice发送短信
            var httpContent = new StringContent(xmlContent, Encoding.UTF8, "application/xml");
            var response = await _httpClient.PostAsync(_smsServiceUrl, httpContent);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                _logger.LogInformation("测试短信发送成功: {ResponseContent}", responseContent);

                return Ok(new ApiResponse<SendSmsResponseDto>
                {
                    Success = true,
                    Data = new SendSmsResponseDto
                    {
                        Success = true,
                        Message = "测试短信发送成功",
                        Data = responseContent
                    },
                    Message = "测试短信发送成功"
                });
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("测试短信发送失败: StatusCode={StatusCode}, Content={Content}", response.StatusCode, errorContent);

                return BadRequest(new ApiResponse<SendSmsResponseDto>
                {
                    Success = false,
                    Data = new SendSmsResponseDto
                    {
                        Success = false,
                        Message = $"测试短信发送失败: {response.StatusCode}",
                        Data = errorContent,
                        ErrorCode = "SMS_SEND_FAILED"
                    },
                    Message = $"测试短信发送失败: {response.StatusCode}",
                    ErrorCode = "SMS_SEND_FAILED"
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送测试短信时发生异常");
            return StatusCode(500, new ApiResponse<SendSmsResponseDto>
            {
                Success = false,
                Data = new SendSmsResponseDto
                {
                    Success = false,
                    Message = "服务器内部错误",
                    ErrorCode = "INTERNAL_ERROR"
                },
                Message = "服务器内部错误",
                ErrorCode = "INTERNAL_ERROR"
            });
        }
    }

    /// <summary>
    /// 发送告警短信
    /// </summary>
    [HttpPost("send-alert-sms")]
    public async Task<ActionResult<ApiResponse<SendSmsResponseDto>>> SendAlertSms([FromBody] SendAlertSmsRequestDto request)
    {
        try
        {
            _logger.LogInformation("开始发送告警短信到手机号: {PhoneNumber}, 级别: {Level}", request.PhoneNumber, request.Level);

            // 获取短信模板
            var templateResult = await _smsTemplateService.GetSmsTemplateAsync();
            if (templateResult == null || string.IsNullOrEmpty(templateResult.TemplateName))
            {
                _logger.LogWarning("短信模板未配置");
                return BadRequest(new ApiResponse<SendSmsResponseDto>
                {
                    Success = false,
                    Data = new SendSmsResponseDto
                    {
                        Success = false,
                        Message = "短信模板未配置",
                        ErrorCode = "TEMPLATE_NOT_CONFIGURED"
                    },
                    Message = "短信模板未配置",
                    ErrorCode = "TEMPLATE_NOT_CONFIGURED"
                });
            }

            // 替换模板中的变量
            var message = templateResult.TemplateName
                .Replace("{{type}}", "待办")
                .Replace("{{modular}}", "网优平台3.0")
                .Replace("{{function}}", request.Level.Replace("system_failure", "系统故障")
                    .Replace("service_exception", "服务异常")
                    .Replace("performance_warning", "性能警告")
                    .Replace("maintenance_notice", "维护通知") ?? "")
                .Replace("{{receiveMans}}", "")
                .Replace("{{receivePns}}", request.PhoneNumber ?? "")
                .Replace("{{ccMans}}", "")
                .Replace("{{ccPns}}", "")
                .Replace("{{sendMan}}", "")
                .Replace("{{message}}", "告警通知");

            // 构造华为短信接口所需的XML格式数据
            var xmlRequest = new XmlSmsRequest
            {
                Type = "待办",
                Modular = "网优平台3.0",
                Function = request.Level.Replace("system_failure", "系统故障")
                    .Replace("service_exception", "服务异常")
                    .Replace("performance_warning", "性能警告")
                    .Replace("maintenance_notice", "维护通知") ?? "",
                ReceiveMans = "",
                ReceivePns = request.PhoneNumber ?? "",
                CcMans = "",
                CcPns = "",
                SendMan = "",
                Message = message
            };

            // 序列化为XML格式
            string xmlContent;
            using (var stringWriter = new StringWriter())
            {
                var serializer = new XmlSerializer(typeof(XmlSmsRequest));
                serializer.Serialize(stringWriter, xmlRequest);
                xmlContent = stringWriter.ToString();
            }

            // 调用webservice发送短信
            var httpContent = new StringContent(xmlContent, Encoding.UTF8, "application/xml");
            var response = await _httpClient.PostAsync(_smsServiceUrl, httpContent);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                _logger.LogInformation("告警短信发送成功: {ResponseContent}", responseContent);

                return Ok(new ApiResponse<SendSmsResponseDto>
                {
                    Success = true,
                    Data = new SendSmsResponseDto
                    {
                        Success = true,
                        Message = "告警短信发送成功",
                        Data = responseContent
                    },
                    Message = "告警短信发送成功"
                });
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("告警短信发送失败: StatusCode={StatusCode}, Content={Content}", response.StatusCode, errorContent);

                return BadRequest(new ApiResponse<SendSmsResponseDto>
                {
                    Success = false,
                    Data = new SendSmsResponseDto
                    {
                        Success = false,
                        Message = $"告警短信发送失败: {response.StatusCode}",
                        Data = errorContent,
                        ErrorCode = "SMS_SEND_FAILED"
                    },
                    Message = $"告警短信发送失败: {response.StatusCode}",
                    ErrorCode = "SMS_SEND_FAILED"
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送告警短信时发生异常");
            return StatusCode(500, new ApiResponse<SendSmsResponseDto>
            {
                Success = false,
                Data = new SendSmsResponseDto
                {
                    Success = false,
                    Message = "服务器内部错误",
                    ErrorCode = "INTERNAL_ERROR"
                },
                Message = "服务器内部错误",
                ErrorCode = "INTERNAL_ERROR"
            });
        }
    }
}