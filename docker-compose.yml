# Map Studio 前后端联调测试 Docker Compose 配置
# 提供完整的开发和测试环境

version: '3.8'

services:
  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: mapstudio-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: mapstudio_password
      MYSQL_DATABASE: mapstudio_dev
      MYSQL_USER: mapstudio
      MYSQL_PASSWORD: mapstudio_user_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init.sql:ro
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - mapstudio-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 10s
      retries: 3
      start_period: 30s

  # 后端 API 服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: mapstudio-backend
    restart: unless-stopped
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:5000
      - ConnectionStrings__DefaultConnection=Server=*************;Port=3307;Database=MTNOH_AAA_Platform;Uid=dtauser;Pwd=dtauser;charset=utf8mb4;
    ports:
      - "5000:5000"
    volumes:
      - ./backend:/app
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
    # depends_on:
    #   mysql:
    #     condition: service_healthy
    networks:
      - mapstudio-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/system/health"]
      timeout: 10s
      retries: 3
      start_period: 45s

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    container_name: mapstudio-frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:5000/api
    ports:
      - "3001:3001"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - mapstudio-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001"]
      timeout: 10s
      retries: 3
      start_period: 30s

  # Redis 缓存 (可选)
  redis:
    image: redis:7-alpine
    container_name: mapstudio-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - mapstudio-network
    command: redis-server --appendonly yes

  # 测试数据库 (用于测试环境)
  mysql-test:
    image: mysql:8.0
    container_name: mapstudio-mysql-test
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: test_password
      MYSQL_DATABASE: mapstudio_test
      MYSQL_USER: test_user
      MYSQL_PASSWORD: test_password
    ports:
      - "3307:3306"
    volumes:
      - mysql_test_data:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - mapstudio-network
    profiles:
      - testing
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 10s
      retries: 3

  # 后端测试服务
  backend-test:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: testing
    container_name: mapstudio-backend-test
    restart: "no"
    environment:
      - ASPNETCORE_ENVIRONMENT=Testing
      - ASPNETCORE_URLS=http://+:5000
      - ConnectionStrings__DefaultConnection=Server=*************;Port=3307;Database=MTNOH_AAA_Platform_test;Uid=dtauser;Pwd=dtauser;charset=utf8mb4;
    ports:
      - "5001:5000"
    # depends_on:
    #   mysql-test:
    #     condition: service_healthy
    networks:
      - mapstudio-network
    profiles:
      - testing

  # Nginx 反向代理 (生产环境)
  nginx:
    image: nginx:alpine
    container_name: mapstudio-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - mapstudio-network
    profiles:
      - production

  # 监控和日志
  # Prometheus (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: mapstudio-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - mapstudio-network
    profiles:
      - monitoring

  # Grafana (可选)
  grafana:
    image: grafana/grafana:latest
    container_name: mapstudio-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - mapstudio-network
    profiles:
      - monitoring

# 网络配置
networks:
  mapstudio-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷
volumes:
  mysql_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/mysql
  mysql_test_data:
    driver: local
  redis_data:
    driver: local
  backend_uploads:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/uploads
  backend_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/logs
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local