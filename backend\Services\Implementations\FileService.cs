using SqlSugar;
using MapStudio.Api.Services.Interfaces;
using MapStudio.Api.Models.DTOs;
using MapStudio.Api.Models.Entities;
using FileEntity = MapStudio.Api.Models.Entities.File;
using SystemFile = System.IO.File;

namespace MapStudio.Api.Services.Implementations;

public class FileService : IFileService
{
    private readonly ISqlSugarClient _db;
    private readonly ILogger<FileService> _logger;
    private readonly IConfiguration _configuration;
    private readonly string _uploadPath;

    public FileService(ISqlSugarClient db, ILogger<FileService> logger, IConfiguration configuration)
    {
        _db = db;
        _logger = logger;
        _configuration = configuration;
        _uploadPath = _configuration.GetValue<string>("FileStorage:BasePath") ?? "./uploads";
        
        // 确保上传目录存在
        if (!Directory.Exists(_uploadPath))
        {
            Directory.CreateDirectory(_uploadPath);
        }
    }

    public async Task<FileUploadResponse> UploadFileAsync(IFormFile file, string fileType)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                throw new ArgumentException("文件不能为空");
            }

            // 验证文件大小 (5MB限制)
            var maxFileSize = _configuration.GetValue<long>("FileStorage:MaxFileSize");
            if (file.Length > maxFileSize)
            {
                throw new ArgumentException("文件大小超过限制");
            }

            // 验证文件类型
            var allowedExtensions = _configuration.GetSection("FileStorage:AllowedExtensions").Get<string[]>();
            var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
            if (!allowedExtensions.Contains(fileExtension))
            {
                throw new ArgumentException("不支持的文件类型");
            }

            var fileId = Guid.NewGuid().ToString();
            var fileName = $"{fileId}{fileExtension}";
            var filePath = Path.Combine(_uploadPath, fileName);

            // 保存文件
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            // 保存文件记录
            var fileRecord = new FileEntity
            {
                Id = fileId,
                FileName = fileName,
                OriginalFileName = file.FileName,
                FileType = fileType,
                FilePath = filePath,
                FileSize = file.Length,
                ContentType = file.ContentType,
                UploadTime = DateTime.Now
            };

            await _db.Insertable(fileRecord).ExecuteCommandAsync();

            _logger.LogInformation("文件上传成功: {FileId}, {FileName}", fileId, file.FileName);

            return new FileUploadResponse
            {
                FileId = fileId,
                FileName = fileRecord.OriginalFileName,
                FileSize = fileRecord.FileSize,
                FileType = fileType,
                UploadTime = fileRecord.UploadTime,
                DownloadUrl = $"/api/files/download/{fileId}"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "文件上传失败");
            throw;
        }
    }

    public async Task<Stream> DownloadFileAsync(string fileId)
    {
        try
        {
            var fileRecord = await _db.Queryable<FileEntity>()
                .Where(f => f.Id == fileId)
                .FirstAsync();

            if (fileRecord == null || !SystemFile.Exists(fileRecord.FilePath))
            {
                throw new FileNotFoundException("文件不存在");
            }

            return new FileStream(fileRecord.FilePath, FileMode.Open, FileAccess.Read);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "文件下载失败: {FileId}", fileId);
            throw;
        }
    }

    public async Task<FilePreviewResponse> GetFilePreviewAsync(string fileId)
    {
        try
        {
            var fileRecord = await _db.Queryable<FileEntity>()
                .Where(f => f.Id == fileId)
                .FirstAsync();

            if (fileRecord == null)
            {
                throw new FileNotFoundException("文件不存在");
            }

            return new FilePreviewResponse
            {
                FileId = fileId,
                FileName = fileRecord.OriginalFileName,
                ContentType = fileRecord.ContentType,
                PreviewUrl = $"/api/files/download/{fileId}"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取文件预览失败: {FileId}", fileId);
            throw;
        }
    }

    public async Task<bool> DeleteFileAsync(string fileId)
    {
        try
        {
            var fileRecord = await _db.Queryable<FileEntity>()
                .Where(f => f.Id == fileId)
                .FirstAsync();

            if (fileRecord == null)
            {
                return false;
            }

            // 删除物理文件
            if (SystemFile.Exists(fileRecord.FilePath))
            {
                SystemFile.Delete(fileRecord.FilePath);
            }

            // 删除数据库记录
            await _db.Deleteable<FileEntity>()
                .Where(f => f.Id == fileId)
                .ExecuteCommandAsync();

            _logger.LogInformation("文件删除成功: {FileId}", fileId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "文件删除失败: {FileId}", fileId);
            throw;
        }
    }
}