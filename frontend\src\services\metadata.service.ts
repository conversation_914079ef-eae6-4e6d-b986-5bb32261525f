/**
 * 基础数据服务
 * 处理服务类型、权限范围、授权期限等基础元数据
 */

import { httpClient } from '../utils/http.client';
import { API_ENDPOINTS } from '../config/endpoints';
import type {
  ServiceTypeOption,
  PermissionScopeOption,
  AuthPeriodOption,
  TenantSizeOption,
  IndustryOption
} from './types';

export class MetadataService {
  // 缓存基础数据
  private static cache = new Map<string, { data: any; timestamp: number }>();
  private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

  /**
   * 获取服务类型列表
   */
  static async getServiceTypes(): Promise<ServiceTypeOption[]> {
    try {
      console.log('🏷️ 获取服务类型列表...');

      // 尝试从缓存获取
      const cached = this.getCachedData('serviceTypes');
      if (cached) {
        console.log('✅ 从缓存获取服务类型列表');
        return cached;
      }

      const response = await httpClient.get<ServiceTypeOption[]>(
        API_ENDPOINTS.METADATA.SERVICE_TYPES
      );

      // 缓存数据
      this.setCachedData('serviceTypes', response);

      console.log(`✅ 获取服务类型列表成功: ${response.length} 项`);
      return response;
    } catch (error: any) {
      console.error('❌ 获取服务类型列表失败:', error.message);

      // 如果API失败，返回默认数据
      return this.getDefaultServiceTypes();
    }
  }

  /**
   * 获取权限范围列表
   */
  static async getPermissionScopes(): Promise<PermissionScopeOption[]> {
    try {
      console.log('🔐 获取权限范围列表...');

      const cached = this.getCachedData('permissionScopes');
      if (cached) {
        console.log('✅ 从缓存获取权限范围列表');
        return cached;
      }

      const response = await httpClient.get<PermissionScopeOption[]>(
        API_ENDPOINTS.METADATA.PERMISSION_SCOPES
      );

      this.setCachedData('permissionScopes', response);

      console.log(`✅ 获取权限范围列表成功: ${response.length} 项`);
      return response;
    } catch (error: any) {
      console.error('❌ 获取权限范围列表失败:', error.message);
      return this.getDefaultPermissionScopes();
    }
  }

  /**
   * 获取授权期限选项
   */
  static async getAuthPeriods(): Promise<AuthPeriodOption[]> {
    try {
      console.log('⏰ 获取授权期限选项...');

      const cached = this.getCachedData('authPeriods');
      if (cached) {
        console.log('✅ 从缓存获取授权期限选项');
        return cached;
      }

      const response = await httpClient.get<AuthPeriodOption[]>(
        API_ENDPOINTS.METADATA.AUTH_PERIODS
      );

      this.setCachedData('authPeriods', response);

      console.log(`✅ 获取授权期限选项成功: ${response.length} 项`);
      return response;
    } catch (error: any) {
      console.error('❌ 获取授权期限选项失败:', error.message);
      return this.getDefaultAuthPeriods();
    }
  }

  /**
   * 获取租户规模选项
   */
  static async getTenantSizes(): Promise<TenantSizeOption[]> {
    try {
      console.log('📏 获取租户规模选项...');

      const cached = this.getCachedData('tenantSizes');
      if (cached) {
        console.log('✅ 从缓存获取租户规模选项');
        return cached;
      }

      const response = await httpClient.get<TenantSizeOption[]>(
        API_ENDPOINTS.METADATA.TENANT_SIZES
      );

      this.setCachedData('tenantSizes', response);

      console.log(`✅ 获取租户规模选项成功: ${response.length} 项`);
      return response;
    } catch (error: any) {
      console.error('❌ 获取租户规模选项失败:', error.message);
      return this.getDefaultTenantSizes();
    }
  }

  /**
   * 获取行业分类选项
   */
  static async getIndustries(): Promise<IndustryOption[]> {
    try {
      console.log('🏭 获取行业分类选项...');

      const cached = this.getCachedData('industries');
      if (cached) {
        console.log('✅ 从缓存获取行业分类选项');
        return cached;
      }

      const response = await httpClient.get<IndustryOption[]>(
        API_ENDPOINTS.METADATA.INDUSTRIES
      );

      this.setCachedData('industries', response);

      console.log(`✅ 获取行业分类选项成功: ${response.length} 项`);
      return response;
    } catch (error: any) {
      console.error('❌ 获取行业分类选项失败:', error.message);
      return this.getDefaultIndustries();
    }
  }

  /**
   * 获取所有基础数据
   */
  static async getAllMetadata(): Promise<{
    serviceTypes: ServiceTypeOption[];
    permissionScopes: PermissionScopeOption[];
    authPeriods: AuthPeriodOption[];
    tenantSizes: TenantSizeOption[];
    industries: IndustryOption[];
  }> {
    try {
      console.log('📦 获取所有基础数据...');

      const [serviceTypes, permissionScopes, authPeriods, tenantSizes, industries] =
        await Promise.all([
          this.getServiceTypes(),
          this.getPermissionScopes(),
          this.getAuthPeriods(),
          this.getTenantSizes(),
          this.getIndustries()
        ]);

      console.log('✅ 获取所有基础数据成功');
      return {
        serviceTypes,
        permissionScopes,
        authPeriods,
        tenantSizes,
        industries
      };
    } catch (error: any) {
      console.error('❌ 获取基础数据失败:', error.message);
      throw new Error(error.message || '获取基础数据失败');
    }
  }

  /**
   * 清除缓存
   */
  static clearCache(): void {
    this.cache.clear();
    console.log('🧹 基础数据缓存已清除');
  }

  /**
   * 刷新所有基础数据
   */
  static async refreshAllMetadata(): Promise<void> {
    console.log('🔄 刷新所有基础数据...');
    this.clearCache();
    await this.getAllMetadata();
    console.log('✅ 基础数据刷新完成');
  }

  /**
   * 从缓存获取数据
   */
  private static getCachedData(key: string): any {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data;
    }
    return null;
  }

  /**
   * 设置缓存数据
   */
  private static setCachedData(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * 获取默认服务类型
   */
  private static getDefaultServiceTypes(): ServiceTypeOption[] {
    return [
      {
        id: 'basic',
        name: '基础地图服务',
        description: '提供基础地图展示、查询功能'
      },
      {
        id: 'advanced',
        name: '高级地图服务',
        description: '提供高级分析、编辑功能'
      },
      {
        id: 'enterprise',
        name: '企业级地图服务',
        description: '提供企业级功能和优先支持'
      },
      {
        id: 'custom',
        name: '定制化地图服务',
        description: '提供定制化开发和集成服务'
      }
    ];
  }

  /**
   * 获取默认权限范围
   */
  private static getDefaultPermissionScopes(): PermissionScopeOption[] {
    return [
      {
        id: 'read',
        name: '只读权限',
        description: '仅可查看和浏览地图数据',
        permissions: 'read'
      },
      {
        id: 'write',
        name: '读写权限',
        description: '可查看、编辑和创建地图数据',
        permissions: 'read,write'
      },
      {
        id: 'admin',
        name: '管理员权限',
        description: '具备完整的管理和配置权限',
        permissions: 'read,write,admin'
      }
    ];
  }

  /**
   * 获取默认授权期限
   */
  private static getDefaultAuthPeriods(): AuthPeriodOption[] {
    return [
      { id: '3months', name: '3个月', months: 3 },
      { id: '6months', name: '6个月', months: 6 },
      { id: '1year', name: '1年', months: 12 },
      { id: '2years', name: '2年', months: 24 },
      { id: '3years', name: '3年', months: 36 }
    ];
  }

  /**
   * 获取默认租户规模
   */
  private static getDefaultTenantSizes(): TenantSizeOption[] {
    return [
      {
        id: 'small',
        name: '小型企业',
        description: '员工数量1-50人'
      },
      {
        id: 'medium',
        name: '中型企业',
        description: '员工数量51-200人'
      },
      {
        id: 'large',
        name: '大型企业',
        description: '员工数量201-1000人'
      },
      {
        id: 'enterprise',
        name: '超大型企业',
        description: '员工数量1000人以上'
      }
    ];
  }

  /**
   * 获取默认行业分类
   */
  private static getDefaultIndustries(): IndustryOption[] {
    return [
      { id: 'tech', name: '科技互联网', category: '信息技术' },
      { id: 'finance', name: '金融保险', category: '金融服务' },
      { id: 'manufacturing', name: '制造业', category: '工业制造' },
      { id: 'logistics', name: '物流运输', category: '交通运输' },
      { id: 'energy', name: '能源电力', category: '能源工业' },
      { id: 'real_estate', name: '房地产建筑', category: '房地产' },
      { id: 'education', name: '教育培训', category: '教育文化' },
      { id: 'healthcare', name: '医疗健康', category: '医疗卫生' },
      { id: 'government', name: '政府机构', category: '公共服务' },
      { id: 'other', name: '其他行业', category: '其他' }
    ];
  }

  /**
   * 根据ID查找服务类型
   */
  static async findServiceTypeById(id: string): Promise<ServiceTypeOption | null> {
    const serviceTypes = await this.getServiceTypes();
    return serviceTypes.find(type => type.id === id) || null;
  }

  /**
   * 根据ID查找权限范围
   */
  static async findPermissionScopeById(id: string): Promise<PermissionScopeOption | null> {
    const permissionScopes = await this.getPermissionScopes();
    return permissionScopes.find(scope => scope.id === id) || null;
  }

  /**
   * 根据ID查找授权期限
   */
  static async findAuthPeriodById(id: string): Promise<AuthPeriodOption | null> {
    const authPeriods = await this.getAuthPeriods();
    return authPeriods.find(period => period.id === id) || null;
  }

  /**
   * 验证服务类型是否有效
   */
  static async isValidServiceType(id: string): Promise<boolean> {
    const serviceType = await this.findServiceTypeById(id);
    return serviceType !== null;
  }

  /**
   * 验证权限范围是否有效
   */
  static async isValidPermissionScope(id: string): Promise<boolean> {
    const permissionScope = await this.findPermissionScopeById(id);
    return permissionScope !== null;
  }

  /**
   * 验证授权期限是否有效
   */
  static async isValidAuthPeriod(id: string): Promise<boolean> {
    const authPeriod = await this.findAuthPeriodById(id);
    return authPeriod !== null;
  }
}
