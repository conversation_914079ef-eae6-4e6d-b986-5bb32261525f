import { test, expect } from '@playwright/test';

test.describe('导航功能测试', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('应该显示主导航菜单', async ({ page }) => {
    // 检查导航容器是否存在
    const navigation = page.locator('nav, .navigation, [role="navigation"]');

    // 如果有侧边栏导航
    const sidebar = page.locator('.sidebar, .nav-sidebar');
    if (await sidebar.count() > 0) {
      await expect(sidebar).toBeVisible();
    }

    // 检查是否有顶部导航
    const topNav = page.locator('.navbar, .top-nav, header nav');
    if (await topNav.count() > 0) {
      await expect(topNav).toBeVisible();
    }
  });

  test('租户管理导航应该工作', async ({ page }) => {
    // 查找并点击租户管理相关的导航项
    const tenantLinks = [
      'text=租户授权申请',
      'text=待审批清单',
      'text=服务状态监控'
    ];

    for (const linkText of tenantLinks) {
      const link = page.locator(linkText).first();
      if (await link.count() > 0) {
        await expect(link).toBeVisible();

        // 测试链接是否可点击
        await link.hover();

        // 检查链接是否有正确的href属性
        const href = await link.getAttribute('href');
        expect(href).toBeTruthy();
      }
    }
  });

  test('运营管理导航应该工作', async ({ page }) => {
    // 查找运营统计相关链接
    const operationLink = page.locator('text=访问统计报表').first();
    if (await operationLink.count() > 0) {
      await expect(operationLink).toBeVisible();

      const href = await operationLink.getAttribute('href');
      expect(href).toContain('/operation/');
    }
  });

  test('页面路由应该正确工作', async ({ page }) => {
    // 测试直接访问API测试页面
    await page.goto('/api-test');

    // 检查是否成功导航（没有404错误）
    const pageContent = page.locator('body');
    await expect(pageContent).toBeVisible();

    // 检查URL是否正确
    expect(page.url()).toContain('/api-test');
  });

  test('应该有面包屑导航或当前页面指示', async ({ page }) => {
    // 检查是否有活跃状态指示
    const activeIndicators = page.locator('.active, .current, [aria-current="page"]');

    // 或者检查页面标题变化
    const pageTitle = page.locator('h1, .page-title, .current-page');
    await expect(pageTitle).toBeVisible();
  });

  test('移动端导航应该工作', async ({ page }) => {
    // 设置移动端视口
    await page.setViewportSize({ width: 375, height: 667 });

    // 重新加载页面
    await page.reload();

    // 检查是否有移动端导航菜单（汉堡菜单）
    const mobileMenu = page.locator('.mobile-menu, .hamburger, [aria-label*="menu"]');

    if (await mobileMenu.count() > 0) {
      await expect(mobileMenu).toBeVisible();

      // 尝试点击打开菜单
      await mobileMenu.click();

      // 检查菜单是否展开
      await page.waitForTimeout(500); // 等待动画完成
    }
  });

  test('返回首页功能应该工作', async ({ page }) => {
    // 如果有logo或首页链接
    const homeLinks = page.locator('a[href="/"], a[href=""], .logo, .brand');

    if (await homeLinks.count() > 0) {
      const homeLink = homeLinks.first();
      await expect(homeLink).toBeVisible();

      // 导航到其他页面
      await page.goto('/api-test');

      // 点击回到首页
      await homeLink.click();

      // 验证回到首页
      await expect(page.locator('h1:has-text("地图工作室SaaS平台")')).toBeVisible();
    }
  });
});
