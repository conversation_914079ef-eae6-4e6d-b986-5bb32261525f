# 数据库连接测试脚本
# 测试与MCP MySQL服务器的连接

Write-Host "🔍 测试数据库连接..." -ForegroundColor Blue

# 数据库连接参数
$DB_HOST = "*************"
$DB_PORT = "3307"
$DB_USER = "dtauser"
$DB_PASSWORD = "dtauser"
$DB_NAME = "MTNOH_AAA_Platform"

Write-Host "📋 连接参数:" -ForegroundColor Yellow
Write-Host "  主机: $DB_HOST" -ForegroundColor White
Write-Host "  端口: $DB_PORT" -ForegroundColor White
Write-Host "  用户: $DB_USER" -ForegroundColor White
Write-Host "  数据库: $DB_NAME" -ForegroundColor White

# 测试网络连接
Write-Host "`n🌐 测试网络连接..." -ForegroundColor Blue
try {
    $result = Test-NetConnection -ComputerName $DB_HOST -Port $DB_PORT -WarningAction SilentlyContinue
    if ($result.TcpTestSucceeded) {
        Write-Host "✅ 网络连接成功" -ForegroundColor Green
    } else {
        Write-Host "❌ 网络连接失败" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ 网络测试出错: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 测试MySQL连接（如果安装了MySQL客户端）
Write-Host "`n🗄️  测试MySQL连接..." -ForegroundColor Blue
try {
    $mysqlExists = Get-Command mysql -ErrorAction SilentlyContinue
    if ($mysqlExists) {
        $connectionString = "mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASSWORD -e `"SELECT 1 as test`""
        $output = Invoke-Expression $connectionString 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ MySQL连接成功" -ForegroundColor Green
        } else {
            Write-Host "❌ MySQL连接失败: $output" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️  MySQL客户端未安装，跳过数据库连接测试" -ForegroundColor Yellow
        Write-Host "   可以通过以下命令安装MySQL客户端:" -ForegroundColor Gray
        Write-Host "   winget install Oracle.MySQL" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ MySQL连接测试出错: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试.NET应用配置
Write-Host "`n🔧 验证.NET配置..." -ForegroundColor Blue
$configFiles = @(
    "appsettings.json",
    "appsettings.Testing.json"
)

foreach ($configFile in $configFiles) {
    if (Test-Path $configFile) {
        try {
            $config = Get-Content $configFile | ConvertFrom-Json
            $connectionString = $config.ConnectionStrings.DefaultConnection
            if ($connectionString -match "192\.168\.2\.119.*3307.*MTNOH_AAA_Platform") {
                Write-Host "✅ $configFile 配置正确" -ForegroundColor Green
            } else {
                Write-Host "❌ $configFile 配置可能有误" -ForegroundColor Red
                Write-Host "   当前连接字符串: $connectionString" -ForegroundColor Gray
            }
        } catch {
            Write-Host "❌ 无法解析 $configFile : $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️  配置文件不存在: $configFile" -ForegroundColor Yellow
    }
}

Write-Host "`n📝 配置摘要:" -ForegroundColor Blue
Write-Host "  - 数据库已配置为连接到MCP MySQL服务器" -ForegroundColor White
Write-Host "  - 主配置文件: appsettings.json" -ForegroundColor White
Write-Host "  - 测试配置文件: appsettings.Testing.json" -ForegroundColor White
Write-Host "  - Docker配置已更新以使用外部数据库" -ForegroundColor White

Write-Host "`n🚀 接下来的步骤:" -ForegroundColor Blue
Write-Host "  1. 确保MCP MySQL服务器正在运行" -ForegroundColor White
Write-Host "  2. 验证网络连接和防火墙设置" -ForegroundColor White
Write-Host "  3. 运行后端应用测试数据库连接" -ForegroundColor White
Write-Host "  4. 检查数据库中是否存在所需的表结构" -ForegroundColor White

Write-Host "`n✨ 配置更新完成！" -ForegroundColor Green