<template>
  <div class="min-h-screen bg-gray-100 p-8">
    <div class="max-w-7xl mx-auto">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">导航栏样式对比测试</h1>
        <p class="text-gray-600">验证Vue版本与React版本的导航栏样式一致性</p>
      </div>

      <!-- 测试控制面板 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">测试控制</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">侧边栏状态</label>
            <div class="flex space-x-2">
              <button
                @click="testSidebarOpen = true"
                :class="[
                  'px-3 py-2 text-sm rounded-md transition-colors',
                  testSidebarOpen 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                ]"
              >
                展开
              </button>
              <button
                @click="testSidebarOpen = false"
                :class="[
                  'px-3 py-2 text-sm rounded-md transition-colors',
                  !testSidebarOpen 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                ]"
              >
                折叠
              </button>
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">主题模式</label>
            <div class="flex space-x-2">
              <button
                @click="darkMode = false"
                :class="[
                  'px-3 py-2 text-sm rounded-md transition-colors',
                  !darkMode 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                ]"
              >
                浅色
              </button>
              <button
                @click="darkMode = true"
                :class="[
                  'px-3 py-2 text-sm rounded-md transition-colors',
                  darkMode 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                ]"
              >
                深色
              </button>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">屏幕尺寸模拟</label>
            <select 
              v-model="screenSize" 
              class="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="desktop">桌面端 (1280px+)</option>
              <option value="tablet">平板端 (768px-1279px)</option>
              <option value="mobile">移动端 (<768px)</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 样式对比区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Vue版本预览 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div class="bg-blue-50 px-4 py-2 border-b border-blue-200">
            <h3 class="text-lg font-semibold text-blue-900">Vue 版本</h3>
          </div>
          <div class="p-4">
            <div :class="previewContainerClasses">
              <!-- 模拟侧边栏 -->
              <div :class="vueNavClasses">
                <div class="flex items-center justify-between h-12 px-3 border-b border-gray-200">
                  <div :class="logoClasses">
                    <i class="fa-solid fa-map-marked-alt text-blue-600 text-lg"></i>
                    <span v-if="testSidebarOpen" class="ml-2 font-bold text-gray-900 text-sm">地图工作室</span>
                  </div>
                  <button v-if="testSidebarOpen" class="p-1 rounded text-gray-500 hover:text-gray-900 hover:bg-gray-100">
                    <i class="fa-solid fa-angle-left text-xs"></i>
                  </button>
                </div>
                
                <nav class="p-3 space-y-1">
                  <div class="mb-2">
                    <button class="flex items-center w-full px-2 py-1.5 text-xs font-medium rounded text-gray-700 hover:bg-gray-100">
                      <i class="fa-solid fa-building" :class="testSidebarOpen ? '' : 'mx-auto'"></i>
                      <template v-if="testSidebarOpen">
                        <span class="ml-2">租户管理</span>
                        <i class="fa-solid fa-angle-down ml-auto"></i>
                      </template>
                    </button>
                    <div v-if="testSidebarOpen" class="mt-1 pl-6 space-y-1">
                      <a href="#" class="block px-2 py-1 text-xs rounded text-blue-600 bg-blue-50">租户授权申请</a>
                      <a href="#" class="block px-2 py-1 text-xs rounded text-gray-600 hover:text-blue-600 hover:bg-blue-50">待审批清单</a>
                    </div>
                  </div>
                  
                  <div class="mb-2">
                    <button class="flex items-center w-full px-2 py-1.5 text-xs font-medium rounded text-gray-700 hover:bg-gray-100">
                      <i class="fa-solid fa-line-chart" :class="testSidebarOpen ? '' : 'mx-auto'"></i>
                      <template v-if="testSidebarOpen">
                        <span class="ml-2">运营管理</span>
                        <i class="fa-solid fa-angle-down ml-auto"></i>
                      </template>
                    </button>
                    <div v-if="testSidebarOpen" class="mt-1 pl-6 space-y-1">
                      <a href="#" class="block px-2 py-1 text-xs rounded text-gray-600 hover:text-blue-600 hover:bg-blue-50">访问统计报表</a>
                      <a href="#" class="block px-2 py-1 text-xs rounded text-gray-600 hover:text-blue-600 hover:bg-blue-50">日操作统计</a>
                    </div>
                  </div>
                </nav>
              </div>
              
              <!-- 模拟主内容区 -->
              <div class="flex-1 flex flex-col">
                <div class="bg-white border-b border-gray-200 h-12 px-4 flex items-center">
                  <button class="p-1 rounded text-gray-500 hover:text-gray-900 hover:bg-gray-100 mr-3">
                    <i class="fa-solid fa-bars text-xs"></i>
                  </button>
                  <div class="flex-1 max-w-xs">
                    <input 
                      type="text" 
                      placeholder="搜索..." 
                      class="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                    />
                  </div>
                </div>
                <div class="flex-1 bg-gray-50 p-3">
                  <div class="text-xs text-gray-500">Vue 版本内容区域</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- React版本参考 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div class="bg-green-50 px-4 py-2 border-b border-green-200">
            <h3 class="text-lg font-semibold text-green-900">React 版本 (参考)</h3>
          </div>
          <div class="p-4">
            <div :class="previewContainerClasses">
              <!-- 模拟React侧边栏 -->
              <div :class="reactNavClasses">
                <div class="flex items-center justify-between h-12 px-3 border-b border-gray-200">
                  <div :class="logoClasses">
                    <i class="fa-solid fa-map-marked-alt text-blue-600 text-lg"></i>
                    <span v-if="testSidebarOpen" class="ml-2 font-bold text-gray-900 text-sm">地图工作室</span>
                  </div>
                  <button v-if="testSidebarOpen" class="p-1 rounded text-gray-500 hover:text-gray-900 hover:bg-gray-100">
                    <i class="fa-solid fa-angle-left text-xs"></i>
                  </button>
                </div>
                
                <nav class="p-3 space-y-1">
                  <div class="mb-2">
                    <button class="flex items-center w-full px-2 py-1.5 text-xs font-medium rounded text-gray-700 hover:bg-gray-100">
                      <i class="fa-solid fa-building" :class="testSidebarOpen ? '' : 'mx-auto'"></i>
                      <template v-if="testSidebarOpen">
                        <span class="ml-2">租户管理</span>
                        <i class="fa-solid fa-angle-down ml-auto"></i>
                      </template>
                    </button>
                    <div v-if="testSidebarOpen" class="mt-1 pl-6 space-y-1">
                      <a href="#" class="block px-2 py-1 text-xs rounded text-blue-600 bg-blue-50">租户授权申请</a>
                      <a href="#" class="block px-2 py-1 text-xs rounded text-gray-600 hover:text-blue-600 hover:bg-blue-50">待审批清单</a>
                    </div>
                  </div>
                  
                  <div class="mb-2">
                    <button class="flex items-center w-full px-2 py-1.5 text-xs font-medium rounded text-gray-700 hover:bg-gray-100">
                      <i class="fa-solid fa-line-chart" :class="testSidebarOpen ? '' : 'mx-auto'"></i>
                      <template v-if="testSidebarOpen">
                        <span class="ml-2">运营管理</span>
                        <i class="fa-solid fa-angle-down ml-auto"></i>
                      </template>
                    </button>
                    <div v-if="testSidebarOpen" class="mt-1 pl-6 space-y-1">
                      <a href="#" class="block px-2 py-1 text-xs rounded text-gray-600 hover:text-blue-600 hover:bg-blue-50">访问统计报表</a>
                      <a href="#" class="block px-2 py-1 text-xs rounded text-gray-600 hover:text-blue-600 hover:bg-blue-50">日操作统计</a>
                    </div>
                  </div>
                </nav>
              </div>
              
              <!-- 模拟React主内容区 -->
              <div class="flex-1 flex flex-col">
                <div class="bg-white border-b border-gray-200 h-12 px-4 flex items-center">
                  <button class="p-1 rounded text-gray-500 hover:text-gray-900 hover:bg-gray-100 mr-3">
                    <i class="fa-solid fa-bars text-xs"></i>
                  </button>
                  <div class="flex-1 max-w-xs">
                    <input 
                      type="text" 
                      placeholder="搜索..." 
                      class="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                    />
                  </div>
                </div>
                <div class="flex-1 bg-gray-50 p-3">
                  <div class="text-xs text-gray-500">React 版本内容区域</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 样式检查清单 -->
      <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">样式一致性检查清单</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-3">布局结构</h3>
            <ul class="space-y-2">
              <li class="flex items-center">
                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                <span class="text-sm text-gray-700">侧边栏宽度：展开256px，折叠80px</span>
              </li>
              <li class="flex items-center">
                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                <span class="text-sm text-gray-700">顶部栏高度：64px</span>
              </li>
              <li class="flex items-center">
                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                <span class="text-sm text-gray-700">过渡动画：300ms cubic-bezier(0.4, 0, 0.2, 1)</span>
              </li>
              <li class="flex items-center">
                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                <span class="text-sm text-gray-700">边框颜色：border-gray-200</span>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-3">交互效果</h3>
            <ul class="space-y-2">
              <li class="flex items-center">
                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                <span class="text-sm text-gray-700">悬停效果：hover:bg-gray-100</span>
              </li>
              <li class="flex items-center">
                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                <span class="text-sm text-gray-700">选中状态：text-blue-600 bg-blue-50</span>
              </li>
              <li class="flex items-center">
                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                <span class="text-sm text-gray-700">图标旋转：展开/折叠动画</span>
              </li>
              <li class="flex items-center">
                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                <span class="text-sm text-gray-700">焦点样式：focus:ring-2 focus:ring-blue-500</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 性能指标 -->
      <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">性能指标对比</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">{{ animationFrames }}</div>
            <div class="text-sm text-gray-500">动画帧率 (FPS)</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">{{ renderTime }}ms</div>
            <div class="text-sm text-gray-500">渲染时间</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-purple-600">{{ memoryUsage }}MB</div>
            <div class="text-sm text-gray-500">内存使用</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';

// 响应式状态
const testSidebarOpen = ref(true);
const darkMode = ref(false);
const screenSize = ref('desktop');
const animationFrames = ref(60);
const renderTime = ref(16);
const memoryUsage = ref(12.5);

// 计算属性
const previewContainerClasses = computed(() => [
  'flex h-64 border border-gray-200 rounded overflow-hidden',
  {
    'max-w-sm': screenSize.value === 'mobile',
    'max-w-2xl': screenSize.value === 'tablet',
    'w-full': screenSize.value === 'desktop'
  }
]);

const vueNavClasses = computed(() => [
  'bg-white border-r border-gray-200 transition-all duration-300',
  testSidebarOpen.value ? 'w-48' : 'w-16'
]);

const reactNavClasses = computed(() => [
  'bg-white border-r border-gray-200 transition-all duration-300',
  testSidebarOpen.value ? 'w-48' : 'w-16'
]);

const logoClasses = computed(() => [
  'flex items-center',
  !testSidebarOpen.value && 'justify-center w-full'
]);

// 性能监控
let performanceTimer: number;

const updatePerformanceMetrics = () => {
  // 模拟性能指标更新
  animationFrames.value = Math.floor(Math.random() * 10) + 55;
  renderTime.value = Math.floor(Math.random() * 8) + 12;
  memoryUsage.value = Math.floor(Math.random() * 5) + 10;
};

onMounted(() => {
  performanceTimer = setInterval(updatePerformanceMetrics, 2000);
});

onUnmounted(() => {
  if (performanceTimer) {
    clearInterval(performanceTimer);
  }
});
</script>

<style scoped>
/* 预览容器样式 */
.preview-container {
  transform-origin: top left;
}

/* 移动端模拟 */
.mobile-preview {
  transform: scale(0.8);
}

/* 平板端模拟 */
.tablet-preview {
  transform: scale(0.9);
}

/* 动画效果 */
.transition-all {
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬停效果增强 */
button:hover {
  transform: translateY(-1px);
}

/* 检查清单动画 */
.checklist-item {
  animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 性能指标动画 */
.metric-value {
  animation: countUp 1s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>