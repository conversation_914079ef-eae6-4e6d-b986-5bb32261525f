<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API模式切换与验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 10px 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .toggle-btn.mock { background-color: #ffc107; color: #212529; }
        .toggle-btn.api { background-color: #28a745; }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .test-results {
            margin-top: 20px;
        }
        .endpoint-test {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Map Studio API模式切换与验证</h1>
        
        <div class="status info" id="currentStatus">
            <strong>当前状态：</strong>检查中...
        </div>

        <div class="controls">
            <button id="toggleBtn" class="toggle-btn" onclick="toggleApiMode()">
                切换API模式
            </button>
            <button onclick="checkAllEndpoints()">测试所有端点</button>
            <button onclick="openApiTest()">打开API测试页</button>
            <button onclick="clearLogs()">清空日志</button>
        </div>

        <div class="test-results">
            <h3>📊 API端点测试结果</h3>
            <div id="testResults"></div>
        </div>

        <div class="test-results">
            <h3>📋 操作日志</h3>
            <pre id="logs"></pre>
        </div>
    </div>

    <script>
        let currentMockMode = false;
        let logs = [];

        // 日志函数
        function addLog(level, message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.push(`[${timestamp}] ${level}: ${message}`);
            document.getElementById('logs').textContent = logs.join('\n');
        }

        // 更新状态显示
        function updateStatus() {
            const statusEl = document.getElementById('currentStatus');
            const toggleBtn = document.getElementById('toggleBtn');
            
            // 从localStorage获取当前模式
            const savedMode = localStorage.getItem('api_mock_mode');
            currentMockMode = savedMode === 'true';
            
            if (currentMockMode) {
                statusEl.innerHTML = '<strong>当前模式：</strong>Mock数据模式 🎭';
                statusEl.className = 'status warning';
                toggleBtn.textContent = '切换到真实API';
                toggleBtn.className = 'toggle-btn mock';
            } else {
                statusEl.innerHTML = '<strong>当前模式：</strong>真实API模式 🌐';
                statusEl.className = 'status success';
                toggleBtn.textContent = '切换到Mock模式';
                toggleBtn.className = 'toggle-btn api';
            }
        }

        // 切换API模式
        function toggleApiMode() {
            const newMode = !currentMockMode;
            localStorage.setItem('api_mock_mode', newMode.toString());
            
            addLog('INFO', `切换到${newMode ? 'Mock数据' : '真实API'}模式`);
            
            if (confirm('API模式已切换，是否重新加载页面以应用更改？')) {
                window.location.reload();
            } else {
                updateStatus();
            }
        }

        // 测试单个端点
        async function testEndpoint(name, url, method = 'GET') {
            const resultDiv = document.createElement('div');
            resultDiv.className = 'endpoint-test';
            
            try {
                addLog('INFO', `测试${name}: ${method} ${url}`);
                
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 10000);
                
                const response = await fetch(url, {
                    method: method,
                    signal: controller.signal,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Client-Version': '1.0.0'
                    }
                });
                
                clearTimeout(timeoutId);
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="status success">
                            ✅ ${name} - 成功 (${response.status})
                        </div>
                        <small>响应数据: ${JSON.stringify(data).substring(0, 200)}...</small>
                    `;
                    addLog('SUCCESS', `${name}测试通过`);
                } else {
                    resultDiv.innerHTML = `
                        <div class="status error">
                            ❌ ${name} - 失败 (${response.status})
                        </div>
                    `;
                    addLog('ERROR', `${name}测试失败: HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="status error">
                        ❌ ${name} - 错误: ${error.message}
                    </div>
                `;
                addLog('ERROR', `${name}测试失败: ${error.message}`);
            }
            
            return resultDiv;
        }

        // 测试所有端点
        async function checkAllEndpoints() {
            const resultsEl = document.getElementById('testResults');
            resultsEl.innerHTML = '<div class="status info">正在测试API端点...</div>';
            
            addLog('INFO', '开始测试所有API端点...');
            
            const endpoints = [
                { name: '健康检查', url: 'http://localhost:5172/api/health' },
                { name: '服务类型', url: 'http://localhost:5172/api/Metadata/service-types' },
                { name: '权限范围', url: 'http://localhost:5172/api/Metadata/permission-scopes' },
                { name: '用户认证', url: 'http://localhost:5172/api/Auth/profile' },
                { name: '操作日志', url: 'http://localhost:5172/api/Operation/logs' }
            ];
            
            resultsEl.innerHTML = '';
            
            for (const endpoint of endpoints) {
                const resultDiv = await testEndpoint(endpoint.name, endpoint.url);
                resultsEl.appendChild(resultDiv);
            }
            
            addLog('INFO', '所有API端点测试完成');
        }

        // 打开API测试页面
        function openApiTest() {
            window.open('http://localhost:3001/api-test', '_blank');
        }

        // 清空日志
        function clearLogs() {
            logs = [];
            document.getElementById('logs').textContent = '';
            document.getElementById('testResults').innerHTML = '';
            addLog('INFO', '日志已清空');
        }

        // 页面加载时初始化
        window.onload = function() {
            updateStatus();
            addLog('INFO', '页面初始化完成');
            
            // 自动切换到真实API模式
            if (currentMockMode) {
                addLog('INFO', '检测到Mock模式，建议切换到真实API模式进行测试');
            } else {
                addLog('SUCCESS', '当前已是真实API模式');
                // 自动测试连接
                setTimeout(checkAllEndpoints, 1000);
            }
        };
    </script>
</body>
</html>