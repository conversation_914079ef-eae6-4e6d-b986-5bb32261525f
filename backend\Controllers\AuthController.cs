using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MapStudio.Api.Services.Interfaces;
using MapStudio.Api.Models.DTOs;
using MapStudio.Api.Models.ViewModels;
using MapStudio.Api.Attributes;
using System.Security.Claims;

namespace MapStudio.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly IAuthService _authService;
    private readonly ILogger<AuthController> _logger;

    public AuthController(IAuthService authService, ILogger<AuthController> logger)
    {
        _authService = authService;
        _logger = logger;
    }

    /// <summary>
    /// 用户登录
    /// </summary>
    /// <param name="request">登录请求</param>
    /// <returns>登录结果</returns>
    [HttpPost("login")]
    public async Task<ActionResult<ApiResponse<LoginResponse>>> Login([FromBody] LoginRequest request)
    {
        try
        {
            var result = await _authService.LoginAsync(request);
            
            if (result.Success)
            {
                return Ok(ApiResponse<LoginResponse>.SuccessResult(result, "登录成功"));
            }
            else
            {
                return BadRequest(ApiResponse<LoginResponse>.ErrorResult(result.Message, "LOGIN_FAILED"));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "登录失败: {Email}", request.Email);
            return StatusCode(500, ApiResponse<LoginResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 用户登出
    /// </summary>
    [HttpPost("logout")]
    [ConditionalAuthorize]
    public async Task<ActionResult<ApiResponse>> Logout()
    {
        try
        {
            await _authService.LogoutAsync();
            return Ok(ApiResponse.SuccessResult("登出成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "登出失败");
            return StatusCode(500, ApiResponse.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取当前用户信息
    /// </summary>
    [HttpGet("profile")]
    [ConditionalAuthorize]
    public async Task<ActionResult<ApiResponse<UserProfileResponse>>> GetProfile()
    {
        try
        {
            var userId = User.FindFirst("UserId")?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(ApiResponse<UserProfileResponse>.ErrorResult("用户身份验证失败", "UNAUTHORIZED"));
            }

            var profile = await _authService.GetUserProfileAsync(userId);
            return Ok(ApiResponse<UserProfileResponse>.SuccessResult(profile, "获取用户信息成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户信息失败");
            return StatusCode(500, ApiResponse<UserProfileResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }
}