<template>
  <div class="p-6 space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">单操作类型热点排行</h1>
      <p class="mt-1 text-gray-500">查看指定操作类型在选定时间范围内的租户使用频次排行</p>
    </div>

    <!-- 查询条件卡片 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            操作类型 <span class="text-red-500">*</span>
          </label>
          <select
            v-model="selectedOperationType"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option v-for="type in operationTypes" :key="type.id" :value="type.id">
              {{ type.name }}
            </option>
          </select>
        </div>

        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            租户规模
          </label>
          <select
            v-model="tenantSize"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option v-for="option in tenantSizeOptions" :key="option.id" :value="option.id">
              {{ option.name }}
            </option>
          </select>
        </div>

        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            行业类型
          </label>
          <select
            v-model="industry"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option v-for="option in industryOptions" :key="option.id" :value="option.id">
              {{ option.name }}
            </option>
          </select>
        </div>

        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            时间范围 <span class="text-red-500">*</span>
          </label>
          <div class="flex space-x-3">
            <button
              v-for="range in ['7d', '30d', '90d']"
              :key="range"
              @click="timeRange = range as TimeRange"
              class="flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors"
              :class="timeRange === range ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'"
            >
              {{ range === '7d' ? '近7天' : range === '30d' ? '近30天' : '近90天' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据概览卡片 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-gray-900">
          {{ selectedOperationTypeName }} - 租户使用频次排行
        </h2>
        <button class="text-blue-600 hover:text-blue-900 text-sm">
          <i class="fa-solid fa-download mr-1"></i> 导出数据
        </button>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- 柱状图 - 租户排行 -->
        <div class="lg:col-span-2">
          <h3 class="text-base font-medium text-gray-900 mb-4">租户使用频次TOP10</h3>
          <div class="h-80">
            <ChartBase :option="barOptions" :loading="chartLoading" class="h-80" />
          </div>
        </div>

        <!-- 饼图 - 行业分布 -->
        <div>
          <h3 class="text-base font-medium text-gray-900 mb-4">行业分布</h3>
          <div class="h-80">
            <ChartBase :option="pieOptions" :loading="chartLoading" class="h-80" />
          </div>
        </div>
      </div>
    </div>

    <!-- 详细数据表格 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                排名
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                租户ID
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                租户名称
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                行业
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                租户规模
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                使用频次
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                环比增长
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr
              v-for="(tenant, index) in tenantData"
              :key="tenant.id"
              class="hover:bg-gray-50 transition-colors"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <template v-if="index < 3">
                    <div
                      class="flex items-center justify-center w-6 h-6 rounded-full text-white text-xs font-bold mr-2"
                      :class="index === 0 ? 'bg-yellow-500' : index === 1 ? 'bg-gray-400' : 'bg-orange-600'"
                    >
                      {{ index + 1 }}
                    </div>
                    <div class="text-sm font-medium text-yellow-500">
                      <i class="fa-solid fa-crown"></i>
                    </div>
                  </template>
                  <template v-else>
                    <div class="w-6 h-6 flex items-center justify-center mr-2 text-gray-500">
                      {{ index + 1 }}
                    </div>
                  </template>
                </div>
              </td>

              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ tenant.id }}
              </td>

              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ tenant.name }}
              </td>

              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-3 h-3 rounded-full mr-2" :style="{ backgroundColor: tenant.color }"></div>
                  <span class="text-sm text-gray-900">{{ tenant.industryName }}</span>
                </div>
              </td>

              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ tenant.sizeName }}
              </td>

              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ tenant.count }}
              </td>

              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="text-sm"
                  :class="growthForId(tenant.id) >= 0 ? 'text-green-600' : 'text-red-600'"
                >
                  {{ growthForId(tenant.id) >= 0 ? '+' : '' }}{{ Math.abs(growthForId(tenant.id)).toFixed(2) }}%
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页控件简化版 -->
      <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-center">
        <button class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 mr-2">
          <i class="fa-solid fa-angle-left mr-1"></i> 上一页
        </button>
        <button class="px-4 py-2 border border-transparent rounded-md bg-blue-600 text-sm font-medium text-white hover:bg-blue-700">
          1
        </button>
        <button class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 mx-2">
          2
        </button>
        <button class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
          下一页 <i class="fa-solid fa-angle-right ml-1"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import ChartBase from '../../components/ChartBase.vue'
type EChartsOption = any

type TimeRange = '7d' | '30d' | '90d'

const operationTypes = [
  { id: 'map_query', name: '地图查询' },
  { id: 'data_upload', name: '数据上传' },
  { id: 'data_download', name: '数据下载' },
  { id: 'layer_edit', name: '图层编辑' },
  { id: 'permission_manage', name: '权限管理' },
  { id: 'service_config', name: '服务配置' },
  { id: 'report_export', name: '报表导出' },
  { id: 'log_view', name: '日志查看' }
]

const tenantSizeOptions = [
  { id: '', name: '全部规模' },
  { id: 'small', name: '小型企业 (<100人)' },
  { id: 'medium', name: '中型企业 (100-500人)' },
  { id: 'large', name: '大型企业 (500-1000人)' },
  { id: 'enterprise', name: 'enterprise (>1000人)' }
]

const industryOptions = [
  { id: '', name: '全部行业' },
  { id: 'it', name: '信息技术' },
  { id: 'transport', name: '交通运输' },
  { id: 'energy', name: '能源环保' },
  { id: 'agriculture', name: '农业农村' },
  { id: 'medical', name: '医疗卫生' },
  { id: 'education', name: '教育科研' },
  { id: 'government', name: '政府机构' }
]

const timeRange = ref<TimeRange>('30d')
const selectedOperationType = ref(operationTypes[0].id)
const tenantSize = ref('')
const industry = ref('')
const chartLoading = ref(true)

// 行业颜色映射
const industryColors: Record<string, string> = {
  it: '#165DFF',
  transport: '#36CFC9',
  energy: '#722ED1',
  agriculture: '#FF7D00',
  medical: '#F53F3F',
  education: '#00B42A',
  government: '#86909C'
}

// 规模名称映射
const sizeNames: Record<string, string> = {
  small: '小型企业',
  medium: '中型企业',
  large: '大型企业',
  enterprise: 'enterprise'
}

// 行业名称映射
const industryNameMap: Record<string, string> = industryOptions.reduce((acc, cur) => {
  if (cur.id) acc[cur.id] = cur.name
  return acc
}, {} as Record<string, string>)

// 组件挂载
onMounted(() => {
  // 模拟数据加载
  setTimeout(() => {
    chartLoading.value = false
  }, 500)
})

// 生成租户数据
interface TenantItem {
  id: string
  name: string
  industry: string
  industryName: string
  size: string
  sizeName: string
  count: number
  color: string
}

function generateTenantData(operationTypeId: string, count = 10): TenantItem[] {
  return Array.from({ length: count }, (_, i) => {
    const industries = Object.keys(industryColors)
    const randomIndustry = industries[Math.floor(Math.random() * industries.length)]
    const sizes = ['small', 'medium', 'large', 'enterprise']
    const randomSize = sizes[Math.floor(Math.random() * sizes.length)]
    return {
      id: `TENANT-${2025000 + i + 1}`,
      name: `租户企业${i + 1}`,
      industry: randomIndustry,
      industryName: industryNameMap[randomIndustry],
      size: randomSize,
      sizeName: sizeNames[randomSize],
      count: Math.floor(Math.random() * 1000) + 100,
      color: industryColors[randomIndustry]
    }
  }).sort((a, b) => b.count - a.count)
}

const tenantData = computed<TenantItem[]>(() => generateTenantData(selectedOperationType.value))

// 饼图数据
interface PieItem {
  name: string
  value: number
  color: string
}

const pieData = computed<PieItem[]>(() => {
  const agg: Record<string, PieItem> = {}
  tenantData.value.forEach(t => {
    if (!agg[t.industry]) {
      agg[t.industry] = { name: t.industryName, value: 0, color: t.color }
    }
    agg[t.industry].value += t.count
  })
  return Object.values(agg).sort((a, b) => b.value - a.value)
})

// 当前操作类型名称
const selectedOperationTypeName = computed(
  () => operationTypes.find(t => t.id === selectedOperationType.value)?.name || ''
)

// 柱状图（横向，TOP10）
const barOptions = computed<EChartsOption>(() => {
  const top10 = tenantData.value.slice(0, 10)
  return {
    grid: { top: 20, right: 20, bottom: 10, left: 110, containLabel: true },
    xAxis: {
      type: 'value',
      axisLabel: { color: '#6b7280' },
      splitLine: { lineStyle: { type: 'dashed', color: '#e5e7eb' } }
    },
    yAxis: {
      type: 'category',
      data: top10.map(t => t.name),
      axisTick: { show: false },
      axisLine: { show: false },
      axisLabel: { color: '#374151' }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: (params: any) => {
        const p = Array.isArray(params) ? params[0] : params
        return `${p.name}<br/>使用次数：${p.value} 次`
      }
    },
    series: [
      {
        type: 'bar',
        data: top10.map(t => ({ value: t.count, itemStyle: { color: t.color } })),
        barMaxWidth: 20,
        itemStyle: { borderRadius: [0, 4, 4, 0] }
      }
    ]
  }
})

// 饼图（行业分布，环形）
const pieOptions = computed<EChartsOption>(() => {
  return {
    tooltip: {
      trigger: 'item',
      formatter: (p: any) => `${p.name}<br/>使用次数：${p.value} 次（${p.percent}%）`
    },
    legend: { show: false },
    series: [
      {
        name: '行业分布',
        type: 'pie',
        radius: ['50%', '70%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: true,
        label: {
          show: true,
          formatter: '{b} {d}%',
          color: '#374151',
          fontSize: 12
        },
        labelLine: {
          show: true,
          length: 10,
          length2: 6
        },
        data: pieData.value.map(d => ({
          name: d.name,
          value: d.value,
          itemStyle: { color: d.color }
        }))
      }
    ]
  }
})

// 稳定伪随机增长率（-5% ~ 15%）
function growthForId(id: string): number {
  let h = 0
  for (let i = 0; i < id.length; i++) {
    h = (h * 31 + id.charCodeAt(i)) | 0
  }
  const val = (h >>> 0) % 2000 // 0..1999
  return val / 100 - 5 // -5..+15
}
</script>