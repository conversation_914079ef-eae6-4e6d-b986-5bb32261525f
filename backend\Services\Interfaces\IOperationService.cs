using MapStudio.Api.Models.DTOs;
using MapStudio.Api.Models.Entities;
using MapStudio.Api.Models.ViewModels;

namespace MapStudio.Api.Services.Interfaces;

public interface IOperationService
{
    Task<DailyStatsResponse> GetDailyStatsAsync(string? selectedTenant, string? selectedServiceType, string? selectedSize, string? viewMode);
    Task<MonthlyStatsResponse> GetMonthlyStatsAsync(string? selectedTenant, string? comparisonType);
    Task<AccessStatsResponse> GetAccessStatsAsync(string? timeRange, string? chartType);
    Task<ActionTypeRankResponse> GetActionTypeRankAsync(string selectedOperationType, string? tenantSize, string? industry, string? timeRange);
    Task<TenantActionRankResponse> GetTenantActionRankAsync();
    Task<UserUsageResponse> GetUserUsageAsync(string? selectedTenant, string? timeRange, string? timeGranularity);
    Task<OperationLogListResponse> GetOperationLogsAsync(int page, int pageSize, string? tenantName, string? actionType, string? userName, string? operationDate);
    Task<AlarmSettingsResponse> GetAlarmSettingsAsync();
    Task<AlarmTemplateUpdateResponse> UpdateAlarmTemplateAsync(string emailTemplate);
    Task<AlarmEmailResponse> AddAlarmEmailAsync(string email, string[] levels);
    Task<AlarmEmailUpdateResponse> UpdateAlarmEmailAsync(string id, bool enabled, string[] levels);
    Task DeleteAlarmEmailAsync(string id);
    Task LogOperationAsync(string tenantId, string? userId, string actionType, string? description);

    Task<OperationLog> GetOperationLogByIdAsync(string id);
    Task<ServiceStatusStatsDto> GetServiceStatusStatsAsync();
    Task<ServiceStatusStatsResponseDto> GetServiceStatusCodeStatsAsync();
    Task<SixMonthUsageStatsResponseDto> GetSixMonthUsageStatsAsync();
    Task<List<TenantDataDto>> GetAllTenantsAsync();
    /// <summary>
    /// 获取每日统计数据
    /// </summary>
    /// <param name="tenantName">租户名称，可选参数。当为空时表示统计所有租户的数据</param>
    /// <returns>每日统计数据响应</returns>
    Task<DailyStatsDTO> GetDailyStatsAsync(string? tenantName = null);
    /// <summary>
    /// 获取租户操作分布统计
    /// </summary>
    /// <param name="tenantName">租户名称，可选参数。当为空时表示统计所有租户的操作分布</param>
    /// <returns>租户操作分布统计列表</returns>
    Task<List<TenantOperationDistributionItem>> GetTenantOperationDistributionAsync(string? tenantName = null);
}