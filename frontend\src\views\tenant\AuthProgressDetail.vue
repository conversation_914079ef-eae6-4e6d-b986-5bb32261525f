<template>
  <div class="p-6 space-y-6">
    <!-- 页面标题和返回按钮 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">授权申请详情</h1>
        <p class="mt-1 text-gray-500">查看授权申请的详细信息和处理进度</p>
      </div>
      <router-link 
        to="/tenant/auth-progress" 
        class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
      >
        <i class="fa-solid fa-arrow-left mr-2"></i>
        返回列表
      </router-link>
    </div>

    <!-- 申请基本信息卡片 -->
    <div v-if="application" class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div>
          <h3 class="text-sm font-medium text-gray-500">申请单号</h3>
          <p class="mt-1 text-lg font-semibold text-blue-600">{{ application.id }}</p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500">租户名称</h3>
          <p class="mt-1 text-lg font-semibold text-gray-900">{{ application.tenantName }}</p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500">申请时间</h3>
          <p class="mt-1 text-lg font-semibold text-gray-900">{{ application.applyTime }}</p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500">权限类型</h3>
          <p class="mt-1">
            <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
              {{ application.serviceTypeName }}
            </span>
          </p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500">过期时间</h3>
          <p class="mt-1 text-lg font-semibold text-gray-900">{{ application.authPeriodName }}</p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500">当前状态</h3>
          <p class="mt-1">
            <span 
              class="px-2 py-1 text-xs font-medium rounded-full"
              :class="getStatusBadgeClass(application.status)"
            >
              {{ application.statusName }}
            </span>
          </p>
        </div>
      </div>
    </div>

    <!-- 进度条 -->
    <div v-if="application" class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">处理进度</h2>
      <div class="flex items-center mb-2">
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div 
            class="h-2 rounded-full"
            :class="application.status === 'rejected' ? 'bg-red-500' : 'bg-blue-500'"
            :style="{ width: application.progress + '%' }"
          ></div>
        </div>
        <span class="ml-2 text-sm text-gray-500">{{ application.progress }}%</span>
      </div>
      
      <!-- 进度步骤 -->
      <div class="mt-8">
        <div class="relative">
          <!-- 连接线 -->
          <div class="absolute top-5 left-5 h-full w-0.5 bg-gray-200"></div>
          
          <!-- 步骤列表 -->
          <ul class="relative space-y-8">
            <li v-for="(step, index) in application.steps" :key="step.id" class="relative pb-8">
              <!-- 步骤图标 -->
              <div 
                class="absolute left-0 mt-1 flex items-center justify-center w-10 h-10 rounded-full border-2 border-white"
                :class="getStepStatusClass(step.status)"
              >
                <i :class="getStepIcon(step.status, step.id)"></i>
              </div>
              
              <!-- 步骤内容 -->
              <div class="ml-16">
                <h3 class="text-lg font-medium text-gray-900">{{ step.name }}</h3>
                <p v-if="step.time" class="mt-1 text-sm text-gray-500">
                  {{ step.time }}
                </p>
                <p v-else class="mt-1 text-sm text-gray-400 italic">
                  等待处理
                </p>
                
                <!-- 驳回原因 -->
                <div v-if="step.id === 'approve' && application.status === 'rejected'" class="mt-2 p-3 bg-red-50 border border-red-100 rounded-md">
                  <h4 class="text-sm font-medium text-red-800">驳回原因：</h4>
                  <p class="text-sm text-red-700">{{ application.rejectReason }}</p>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 联系信息 -->
    <div v-if="application" class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">联系信息</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div>
          <h3 class="text-sm font-medium text-gray-500">联系人</h3>
          <p class="mt-1 text-lg font-semibold text-gray-900">{{ application.contactPerson }}</p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500">联系电话</h3>
          <p class="mt-1 text-lg font-semibold text-gray-900">{{ application.contactPhone }}</p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500">联系邮箱</h3>
          <p class="mt-1 text-lg font-semibold text-gray-900">{{ application.contactEmail }}</p>
        </div>
      </div>
    </div>

    <!-- 加载中状态 -->
    <div v-if="!application" class="flex justify-center items-center h-64">
      <div class="text-center">
        <i class="fa-solid fa-spinner fa-spin text-3xl text-blue-500 mb-4"></i>
        <p class="text-gray-500">正在加载申请详情...</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { useRoute } from 'vue-router'
import { cn } from '@/lib/utils'
import { TenantService, type AuthApplicationProgessDetail } from '@/services'

const route = useRoute()

const loadProgressData = async () => {
    try {
    console.log('applicationId:' + route.params.id);
    const result = await TenantService.getAuthProgressDetail(route.params.id as string);
    application.value = result; // 正确：使用.value来修改ref的值
    console.log(application.value);
  } catch (error) {
    console.error('获取申请详情失败:', error);
  }
};

// 模拟授权进度数据
// const progressData = [
//   {
//     id: 'TA-20250801',
//     tenantName: '智慧城市科技有限公司',
//     applyTime: '2025-08-01 09:23:45',
//     serviceType: 'enterprise',
//     serviceTypeName: '企业级地图服务',
//     authPeriod: '1year',
//     authPeriodName: '1年',
//     status: 'approved',
//     statusName: '已通过',
//     progress: 100,
//     currentStep: 'completed',
//     steps: [
//       { id: 'submit', name: '提交申请', time: '2025-08-01 09:23:45', status: 'completed' },
//       { id: 'review', name: '审核中', time: '2025-08-01 10:15:30', status: 'completed' },
//       { id: 'approve', name: '审批通过', time: '2025-08-02 14:30:00', status: 'completed' },
//       { id: 'activate', name: '服务激活', time: '2025-08-02 15:00:00', status: 'completed' },
//     ],
//     contactPerson: '张三',
//     contactPhone: '13800138000',
//     contactEmail: '<EMAIL>',
//   },
//   {
//     id: 'TA-20250805',
//     tenantName: '云端数据有限公司',
//     applyTime: '2025-08-05 11:45:22',
//     serviceType: 'advanced',
//     serviceTypeName: '高级地图服务',
//     authPeriod: '2years',
//     authPeriodName: '2年',
//     status: 'reviewing',
//     statusName: '审核中',
//     progress: 60,
//     currentStep: 'review',
//     steps: [
//       { id: 'submit', name: '提交申请', time: '2025-08-05 11:45:22', status: 'completed' },
//       { id: 'review', name: '审核中', time: '2025-08-05 14:20:15', status: 'current' },
//       { id: 'approve', name: '审批通过', time: '', status: 'pending' },
//       { id: 'activate', name: '服务激活', time: '', status: 'pending' },
//     ],
//     contactPerson: '李四',
//     contactPhone: '13900139000',
//     contactEmail: '<EMAIL>',
//   },
//   {
//     id: 'TA-20250803',
//     tenantName: '绿色能源集团',
//     applyTime: '2025-08-03 10:15:33',
//     serviceType: 'basic',
//     serviceTypeName: '基础地图服务',
//     authPeriod: '3months',
//     authPeriodName: '3个月',
//     status: 'pending',
//     statusName: '待审批',
//     progress: 25,
//     currentStep: 'review',
//     steps: [
//       { id: 'submit', name: '提交申请', time: '2025-08-03 10:15:33', status: 'completed' },
//       { id: 'review', name: '审核中', time: '', status: 'current' },
//       { id: 'approve', name: '审批通过', time: '', status: 'pending' },
//       { id: 'activate', name: '服务激活', time: '', status: 'pending' },
//     ],
//     contactPerson: '王五',
//     contactPhone: '13700137000',
//     contactEmail: '<EMAIL>',
//   },
//   {
//     id: 'TA-20250728',
//     tenantName: '智能物流股份有限公司',
//     applyTime: '2025-07-28 16:30:45',
//     serviceType: 'enterprise',
//     serviceTypeName: '企业级地图服务',
//     authPeriod: '1year',
//     authPeriodName: '1年',
//     status: 'rejected',
//     statusName: '已驳回',
//     progress: 50,
//     currentStep: 'review',
//     steps: [
//       { id: 'submit', name: '提交申请', time: '2025-07-28 16:30:45', status: 'completed' },
//       { id: 'review', name: '审核中', time: '2025-07-29 09:15:20', status: 'completed' },
//       { id: 'approve', name: '审批驳回', time: '2025-07-30 11:45:33', status: 'rejected' },
//       { id: 'activate', name: '服务激活', time: '', status: 'pending' },
//     ],
//     rejectReason: '企业资质文件不完整，请补充营业执照副本。',
//     contactPerson: '钱七',
//     contactPhone: '13500135000',
//     contactEmail: '<EMAIL>',
//   },
// ]

// 响应式数据
const application = ref<any>(null)

// 获取状态样式
const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'reviewing':
      return 'bg-blue-100 text-blue-800'
    case 'approved':
      return 'bg-green-100 text-green-800'
    case 'rejected':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 获取步骤状态样式
const getStepStatusClass = (stepStatus: string) => {
  switch (stepStatus) {
    case 'completed':
      return 'bg-green-100 text-green-800'
    case 'current':
      return 'bg-blue-100 text-blue-800'
    case 'rejected':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 获取步骤图标
const getStepIcon = (stepStatus: string, stepId: string) => {
  switch (stepStatus) {
    case 'completed':
      return 'fa-solid fa-check'
    case 'current':
      return 'fa-solid fa-hourglass-half'
    case 'rejected':
      return 'fa-solid fa-times'
    default:
      return 'fa-solid fa-circle'
  }
}

// 模拟API请求获取申请详情
onMounted(() => {
  // 模拟API请求延迟
  setTimeout(() => {
    loadProgressData();
  }, 800)
})
</script>