<template>
  <div class="p-6 space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">租户服务用户使用量查询</h1>
      <p class="mt-1 text-gray-500">查看各租户的服务用户数量、活跃用户数、新增用户数等指标</p>
    </div>
    
    <!-- 查询条件卡片 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="space-y-2">
          <label for="tenantSelect" class="block text-sm font-medium text-gray-700">
            租户选择
          </label>
          <select
            id="tenantSelect"
            v-model="selectedTenant"
            @change="handleTenantChange"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option v-for="tenant in tenantOptions" :key="tenant.id" :value="tenant.id">
              {{ tenant.name }}
            </option>
          </select>
        </div>
        
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            时间范围 <span class="text-red-500">*</span>
          </label>
          <div class="flex space-x-3">
            <button
              v-for="range in timeRangeOptions"
              :key="range.value"
              @click="handleTimeRangeChange(range.value)"
              :class="[
                'flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors',
                timeRange === range.value
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
              ]"
            >
              {{ range.label }}
            </button>
          </div>
        </div>
        
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            时间粒度 <span class="text-red-500">*</span>
          </label>
          <div class="flex space-x-3">
            <button
              v-for="granularity in timeGranularityOptions"
              :key="granularity.value"
              @click="handleTimeGranularityChange(granularity.value)"
              :disabled="
                (timeRange === '7d' && granularity.value === 'month') ||
                (timeRange === '30d' && granularity.value === 'month')
              "
              :class="[
                'flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors',
                timeGranularity === granularity.value
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed'
              ]"
            >
              {{ granularity.label }}
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 数据概览卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">总用户数</p>
            <h3 class="mt-1 text-2xl font-bold text-gray-900">
              {{ currentUserData?.totalUsers || 0 }}
            </h3>
            <p class="mt-1 text-xs text-green-500 flex items-center">
              <i class="fa-solid fa-arrow-up mr-1"></i> 8.2% 较上月
            </p>
          </div>
          <div class="p-3 bg-blue-100 rounded-lg">
            <i class="fa-solid fa-users text-blue-600 text-xl"></i>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">活跃用户数</p>
            <h3 class="mt-1 text-2xl font-bold text-gray-900">
              {{ currentUserData?.activeUsers || 0 }}
            </h3>
            <p class="mt-1 text-xs text-green-500 flex items-center">
              <i class="fa-solid fa-arrow-up mr-1"></i> 12.5% 较上月
            </p>
          </div>
          <div class="p-3 bg-green-100 rounded-lg">
            <i class="fa-solid fa-user-check text-green-600 text-xl"></i>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">新增用户数</p>
            <h3 class="mt-1 text-2xl font-bold text-gray-900">
              {{ currentUserData?.newUsers || 0 }}
            </h3>
            <p class="mt-1 text-xs text-red-500 flex items-center">
              <i class="fa-solid fa-arrow-down mr-1"></i> 3.1% 较上月
            </p>
          </div>
          <div class="p-3 bg-purple-100 rounded-lg">
            <i class="fa-solid fa-user-plus text-purple-600 text-xl"></i>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 趋势图卡片 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-gray-900">用户使用趋势</h2>
        <button class="text-blue-600 hover:text-blue-900 text-sm">
          <i class="fa-solid fa-download mr-1"></i> 导出数据
        </button>
      </div>
      
      <div class="h-80">
        <ChartBase
          :option="chartOption"
          :loading="chartLoading"
        />
      </div>
    </div>
    
    <!-- 租户列表数据表格 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
      <div class="flex items-center justify-between p-6 border-b border-gray-100">
        <h2 class="text-lg font-semibold text-gray-900">租户用户使用量明细</h2>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <i class="fa-solid fa-search text-gray-400"></i>
          </div>
          <input
            v-model="searchKeyword"
            type="text"
            placeholder="搜索租户名称或ID..."
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm w-64"
          />
        </div>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                租户ID
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                租户名称
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                服务类型
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                总用户数
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                活跃用户数
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                活跃率
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                新增用户数
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                用户增长
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                最近活跃
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr 
              v-for="tenant in filteredTenantList" 
              :key="tenant.id" 
              class="hover:bg-gray-50 transition-colors"
            >
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ tenant.id }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ tenant.name }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                  {{ tenant.serviceType }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ tenant.totalUsers }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ tenant.activeUsers }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      class="bg-green-600 h-2 rounded-full" 
                      :style="{ width: `${tenant.activeRate}%` }"
                    ></div>
                  </div>
                  <span class="ml-2 text-sm text-gray-900">{{ tenant.activeRate }}%</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ tenant.newUsers }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="[
                  'text-sm',
                  tenant.isUserGrowthPositive ? 'text-green-600' : 'text-red-600'
                ]">
                  {{ tenant.isUserGrowthPositive ? '+' : '' }}{{ tenant.userGrowth }}%
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ tenant.lastActive }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- 分页控件简化版 -->
      <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-center">
        <button class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 mr-2">
          <i class="fa-solid fa-angle-left mr-1"></i> 上一页
        </button>
        <button class="px-4 py-2 border border-transparent rounded-md bg-blue-600 text-sm font-medium text-white hover:bg-blue-700">
          1
        </button>
        <button class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 mx-2">
          2
        </button>
        <button class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 mx-2">
          3
        </button>
        <button class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
          下一页 <i class="fa-solid fa-angle-right ml-1"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import ChartBase from '@/components/ChartBase.vue'

// 租户选项数据 - 与React版本完全一致
const tenantOptions = [
  { id: '', name: '全部租户' },
  { id: 'TENANT-2025001', name: '智慧城市科技有限公司' },
  { id: 'TENANT-2025002', name: '未来交通研究院' },
  { id: 'TENANT-2025003', name: '绿色能源集团' },
  { id: 'TENANT-2025004', name: '数字农业科技有限公司' },
  { id: 'TENANT-2025005', name: '智慧医疗系统集成商' },
]

// 时间范围选项
const timeRangeOptions = [
  { value: '7d', label: '近7天' },
  { value: '30d', label: '近30天' },
  { value: '90d', label: '近90天' },
]

// 时间粒度选项
const timeGranularityOptions = [
  { value: 'day', label: '按日' },
  { value: 'week', label: '按周' },
  { value: 'month', label: '按月' },
]

// 响应式状态
const selectedTenant = ref('')
const timeRange = ref('30d')
const timeGranularity = ref('day')
const searchKeyword = ref('')
const chartLoading = ref(false)

// 生成用户使用量数据的函数 - 与React版本一致
const generateUserData = (days = 30) => {
  return Array.from({ length: days }, (_, i) => {
    const date = new Date()
    date.setDate(date.getDate() - (days - i - 1))
    
    return {
      date: date.toLocaleDateString('zh-CN'),
      totalUsers: Math.floor(Math.random() * 500) + 500,
      activeUsers: Math.floor(Math.random() * 300) + 200,
      newUsers: Math.floor(Math.random() * 50) + 10,
    }
  })
}

// 生成租户列表数据的函数 - 与React版本一致
const generateTenantListData = (count = 15) => {
  return Array.from({ length: count }, (_, i) => {
    const serviceTypes = ['基础地图服务', '高级地图服务', '企业级地图服务', '定制化地图服务']
    const randomService = serviceTypes[Math.floor(Math.random() * serviceTypes.length)]
    
    const totalUsers = Math.floor(Math.random() * 1000) + 100
    const activeUsers = Math.floor(totalUsers * (0.3 + Math.random() * 0.7))
    const newUsers = Math.floor(Math.random() * 100)
    const activeRate = ((activeUsers / totalUsers) * 100).toFixed(2)
    
    const userGrowth = (Math.random() * 20 - 5).toFixed(2)
    const isUserGrowthPositive = parseFloat(userGrowth) >= 0
    
    return {
      id: `TENANT-${2025000 + i + 1}`,
      name: `租户企业${i + 1}`,
      serviceType: randomService,
      totalUsers,
      activeUsers,
      newUsers,
      activeRate: parseFloat(activeRate),
      userGrowth,
      isUserGrowthPositive,
      lastActive: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toLocaleDateString('zh-CN'),
    }
  })
}

// 数据生成
const userData = ref(generateUserData(30))
const tenantListData = ref(generateTenantListData())

// 计算属性
const currentUserData = computed(() => {
  return userData.value[userData.value.length - 1]
})

const filteredTenantList = computed(() => {
  if (!searchKeyword.value) {
    return tenantListData.value
  }
  return tenantListData.value.filter(tenant => 
    tenant.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    tenant.id.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 图表配置 - 与React版本Recharts对应的ECharts配置
const chartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderColor: '#e5e7eb',
    borderWidth: 1,
    borderRadius: 8,
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
    textStyle: {
      color: '#374151'
    }
  },
  legend: {
    data: ['总用户数', '活跃用户数', '新增用户数'],
    bottom: 0,
    textStyle: {
      color: '#6b7280'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '15%',
    top: '10%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: userData.value.map(item => item.date),
    axisLine: {
      lineStyle: {
        color: '#e5e7eb'
      }
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      color: '#6b7280',
      fontSize: 12
    }
  },
  yAxis: {
    type: 'value',
    axisLine: {
      show: false
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      color: '#6b7280',
      fontSize: 12
    },
    splitLine: {
      lineStyle: {
        color: '#e5e7eb',
        type: 'dashed'
      }
    }
  },
  series: [
    {
      name: '总用户数',
      type: 'line',
      data: userData.value.map(item => item.totalUsers),
      smooth: true,
      lineStyle: {
        color: '#165DFF',
        width: 2
      },
      itemStyle: {
        color: '#165DFF'
      },
      symbol: 'circle',
      symbolSize: 4
    },
    {
      name: '活跃用户数',
      type: 'line',
      data: userData.value.map(item => item.activeUsers),
      smooth: true,
      lineStyle: {
        color: '#00B42A',
        width: 2
      },
      itemStyle: {
        color: '#00B42A'
      },
      symbol: 'circle',
      symbolSize: 4
    },
    {
      name: '新增用户数',
      type: 'line',
      data: userData.value.map(item => item.newUsers),
      smooth: true,
      lineStyle: {
        color: '#722ED1',
        width: 2
      },
      itemStyle: {
        color: '#722ED1'
      },
      symbol: 'circle',
      symbolSize: 4
    }
  ]
}))

// 事件处理函数 - 与React版本完全对应
const handleTenantChange = () => {
  console.log('租户选择变化:', selectedTenant.value)
  // 这里可以根据选择的租户重新加载数据
}

const handleTimeRangeChange = (value: string) => {
  timeRange.value = value
  // 重新生成数据
  const days = value === '7d' ? 7 : value === '30d' ? 30 : 90
  userData.value = generateUserData(days)
}

const handleTimeGranularityChange = (value: string) => {
  timeGranularity.value = value
  console.log('时间粒度变化:', value)
}

// 组件挂载
onMounted(() => {
  console.log('UserUsage页面已挂载')
})
</script>

<style scoped>
/* 与React版本完全一致的样式 */
.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* 悬停效果 */
.hover\:shadow-md:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.hover\:bg-gray-50:hover {
  background-color: #f9fafb;
}

.hover\:bg-blue-700:hover {
  background-color: #1d4ed8;
}

.hover\:text-blue-900:hover {
  color: #1e3a8a;
}

/* 焦点样式 */
.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-blue-500:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.focus\:border-blue-500:focus {
  border-color: #3b82f6;
}

/* 禁用状态 */
.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

/* 表格样式 */
.divide-y > :not([hidden]) ~ :not([hidden]) {
  border-top-width: 1px;
  border-color: #e5e7eb;
}

.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  border-color: #e5e7eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-1.md\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}
</style>