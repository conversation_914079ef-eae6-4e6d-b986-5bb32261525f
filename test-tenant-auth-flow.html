<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>租户授权申请流程测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .section {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
        }
        .button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>租户授权申请流程测试</h1>
    <p>这个页面用于测试租户授权申请的前后端流程，包括申请提交和审批清单查看功能。</p>

    <div class="container">
        <!-- 申请提交部分 -->
        <div class="section">
            <h2>1. 租户授权申请</h2>
            <div class="form-group">
                <label>租户名称：</label>
                <input type="text" id="tenantName" value="测试科技有限公司" />
            </div>
            <div class="form-group">
                <label>租户ID：</label>
                <input type="text" id="tenantId" value="TEST_TECH_001" />
            </div>
            <div class="form-group">
                <label>联系人：</label>
                <input type="text" id="contactPerson" value="张三" />
            </div>
            <div class="form-group">
                <label>联系邮箱：</label>
                <input type="email" id="contactEmail" value="<EMAIL>" />
            </div>
            <div class="form-group">
                <label>联系电话：</label>
                <input type="tel" id="contactPhone" value="13800138000" />
            </div>
            <div class="form-group">
                <label>服务类型：</label>
                <select id="serviceType">
                    <option value="basic">基础地图服务</option>
                    <option value="advanced">高级地图服务</option>
                    <option value="enterprise" selected>企业级地图服务</option>
                    <option value="custom">定制化地图服务</option>
                </select>
            </div>
            <div class="form-group">
                <label>授权期限：</label>
                <select id="authPeriod">
                    <option value="3months">3个月</option>
                    <option value="6months">6个月</option>
                    <option value="1year" selected>1年</option>
                    <option value="2years">2年</option>
                    <option value="3years">3年</option>
                </select>
            </div>
            <div class="form-group">
                <label>权限范围：</label>
                <select id="permissionScope">
                    <option value="read">只读权限</option>
                    <option value="write">读写权限</option>
                    <option value="admin" selected>管理员权限</option>
                </select>
            </div>
            <div class="form-group">
                <label>申请描述：</label>
                <textarea id="description" rows="3">需要企业级地图服务用于智能城市项目开发</textarea>
            </div>
            <button class="button" onclick="submitApplication()">📝 提交申请</button>
            <div id="submitResult" class="result" style="display: none;"></div>
        </div>

        <!-- 审批列表部分 -->
        <div class="section">
            <h2>2. 租户授权审批列表</h2>
            <div class="form-group">
                <label>搜索条件：</label>
                <input type="text" id="searchTerm" placeholder="搜索租户名称、申请ID或联系人" />
            </div>
            <div class="form-group">
                <label>服务类型筛选：</label>
                <select id="filterServiceType">
                    <option value="">全部服务类型</option>
                    <option value="basic">基础地图服务</option>
                    <option value="advanced">高级地图服务</option>
                    <option value="enterprise">企业级地图服务</option>
                    <option value="custom">定制化地图服务</option>
                </select>
            </div>
            <div class="form-group">
                <label>状态筛选：</label>
                <select id="filterStatus">
                    <option value="">全部状态</option>
                    <option value="pending" selected>待审批</option>
                    <option value="approved">已通过</option>
                    <option value="rejected">已拒绝</option>
                </select>
            </div>
            <button class="button" onclick="loadApplications()">📋 获取申请列表</button>
            <button class="button" onclick="approveApplication()">✅ 批量审批通过</button>
            <button class="button" onclick="rejectApplication()">❌ 批量审批拒绝</button>
            <div id="listResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <!-- 详细测试结果 -->
    <div class="section" style="margin-top: 20px;">
        <h2>3. 测试结果汇总</h2>
        <button class="button" onclick="runFullTest()">🚀 运行完整测试流程</button>
        <button class="button" onclick="clearResults()">🧹 清除结果</button>
        <div id="testSummary" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5172/api';
        let lastApplicationId = null;

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(`${API_BASE}${url}`, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.message || `HTTP ${response.status}`);
                }
                
                return data;
            } catch (error) {
                console.error('API请求失败:', error);
                throw error;
            }
        }

        async function submitApplication() {
            const submitBtn = document.querySelector('button[onclick="submitApplication()"]');
            const resultDiv = document.getElementById('submitResult');
            
            try {
                submitBtn.disabled = true;
                submitBtn.textContent = '提交中...';
                
                const applicationData = {
                    tenantName: document.getElementById('tenantName').value,
                    tenantId: document.getElementById('tenantId').value,
                    contactPerson: document.getElementById('contactPerson').value,
                    contactEmail: document.getElementById('contactEmail').value,
                    contactPhone: document.getElementById('contactPhone').value,
                    serviceType: document.getElementById('serviceType').value,
                    authPeriod: document.getElementById('authPeriod').value,
                    permissionScope: document.getElementById('permissionScope').value,
                    description: document.getElementById('description').value,
                    businessLicense: 'license-file-id',
                    organizationCode: 'org-code-file-id'
                };

                console.log('提交申请数据:', applicationData);

                const response = await makeRequest('/Tenant/auth-applications', {
                    method: 'POST',
                    body: JSON.stringify(applicationData)
                });

                lastApplicationId = response.data.id;

                resultDiv.className = 'result success';
                resultDiv.style.display = 'block';
                resultDiv.textContent = `✅ 申请提交成功！\n申请ID: ${response.data.id}\n状态: ${response.data.status}\n时间: ${new Date(response.data.appliedAt).toLocaleString()}`;

                logTest('申请提交', '成功', `申请ID: ${response.data.id}`);

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
                resultDiv.textContent = `❌ 申请提交失败: ${error.message}`;
                
                logTest('申请提交', '失败', error.message);
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '📝 提交申请';
            }
        }

        async function loadApplications() {
            const listBtn = document.querySelector('button[onclick="loadApplications()"]');
            const resultDiv = document.getElementById('listResult');
            
            try {
                listBtn.disabled = true;
                listBtn.textContent = '加载中...';
                
                const params = new URLSearchParams({
                    page: 1,
                    pageSize: 10,
                    ...(document.getElementById('searchTerm').value && { searchTerm: document.getElementById('searchTerm').value }),
                    ...(document.getElementById('filterServiceType').value && { serviceType: document.getElementById('filterServiceType').value }),
                    ...(document.getElementById('filterStatus').value && { status: document.getElementById('filterStatus').value })
                });

                console.log('获取申请列表，参数:', params.toString());

                const response = await makeRequest(`/Tenant/auth-applications?${params}`);

                resultDiv.className = 'result success';
                resultDiv.style.display = 'block';
                
                let content = `✅ 获取申请列表成功！\n总数: ${response.data.totalCount} 条\n当前页: ${response.data.currentPage}/${response.data.totalPages}\n\n`;
                
                if (response.data.items.length > 0) {
                    content += '申请列表:\n';
                    response.data.items.forEach((item, index) => {
                        content += `${index + 1}. ${item.id}\n`;
                        content += `   租户: ${item.tenantName}\n`;
                        content += `   联系人: ${item.contactPerson} (${item.contactPhone})\n`;
                        content += `   服务: ${item.serviceTypeName}\n`;
                        content += `   期限: ${item.authPeriodName}\n`;
                        content += `   状态: ${item.status}\n`;
                        content += `   时间: ${item.applyTime}\n\n`;
                    });
                } else {
                    content += '暂无申请记录';
                }
                
                resultDiv.textContent = content;
                
                logTest('获取申请列表', '成功', `共 ${response.data.totalCount} 条记录`);

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
                resultDiv.textContent = `❌ 获取申请列表失败: ${error.message}`;
                
                logTest('获取申请列表', '失败', error.message);
            } finally {
                listBtn.disabled = false;
                listBtn.textContent = '📋 获取申请列表';
            }
        }

        async function approveApplication() {
            if (!lastApplicationId) {
                alert('请先提交一个申请！');
                return;
            }

            const comments = prompt('请输入审批意见：', '申请材料齐全，符合要求，同意通过。');
            if (!comments) return;

            try {
                const response = await makeRequest(`/Tenant/auth-applications/${lastApplicationId}/approve`, {
                    method: 'PUT',
                    body: JSON.stringify({
                        action: 'approve',
                        comments: comments
                    })
                });

                alert(`✅ 审批通过成功！\n申请ID: ${response.data.id}\n状态: ${response.data.status}`);
                logTest('审批通过', '成功', `申请ID: ${response.data.id}`);
                
                // 重新加载列表
                loadApplications();

            } catch (error) {
                alert(`❌ 审批失败: ${error.message}`);
                logTest('审批通过', '失败', error.message);
            }
        }

        async function rejectApplication() {
            if (!lastApplicationId) {
                alert('请先提交一个申请！');
                return;
            }

            const comments = prompt('请输入审批意见：', '申请材料不完整，不符合要求，予以拒绝。');
            if (!comments) return;

            try {
                const response = await makeRequest(`/Tenant/auth-applications/${lastApplicationId}/approve`, {
                    method: 'PUT',
                    body: JSON.stringify({
                        action: 'reject',
                        comments: comments
                    })
                });

                alert(`✅ 审批拒绝成功！\n申请ID: ${response.data.id}\n状态: ${response.data.status}`);
                logTest('审批拒绝', '成功', `申请ID: ${response.data.id}`);
                
                // 重新加载列表
                loadApplications();

            } catch (error) {
                alert(`❌ 审批失败: ${error.message}`);
                logTest('审批拒绝', '失败', error.message);
            }
        }

        async function runFullTest() {
            const summaryDiv = document.getElementById('testSummary');
            summaryDiv.style.display = 'block';
            summaryDiv.className = 'result';
            summaryDiv.textContent = '🚀 开始运行完整测试流程...\n\n';

            try {
                // 1. 提交申请
                summaryDiv.textContent += '1. 提交租户授权申请...\n';
                await submitApplication();
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 2. 获取申请列表
                summaryDiv.textContent += '2. 获取待审批申请列表...\n';
                await loadApplications();
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 3. 审批通过
                summaryDiv.textContent += '3. 执行审批操作...\n';
                await approveApplication();
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 4. 再次获取列表验证状态变化
                summaryDiv.textContent += '4. 验证审批后状态...\n';
                document.getElementById('filterStatus').value = 'approved';
                await loadApplications();

                summaryDiv.className = 'result success';
                summaryDiv.textContent += '\n✅ 完整测试流程执行成功！\n';
                summaryDiv.textContent += '所有功能正常工作：申请提交 → 数据保存 → 审批列表显示 → 状态更新';

            } catch (error) {
                summaryDiv.className = 'result error';
                summaryDiv.textContent += `\n❌ 测试流程执行失败: ${error.message}`;
            }
        }

        function clearResults() {
            document.getElementById('submitResult').style.display = 'none';
            document.getElementById('listResult').style.display = 'none';
            document.getElementById('testSummary').style.display = 'none';
            lastApplicationId = null;
            console.clear();
        }

        function logTest(action, status, details) {
            const timestamp = new Date().toLocaleTimeString();
            console.log(`[${timestamp}] ${action}: ${status} - ${details}`);
        }

        // 页面加载时自动加载申请列表
        window.onload = function() {
            console.log('租户授权申请流程测试页面已加载');
            console.log('前端地址: http://localhost:3001');
            console.log('后端API地址: http://localhost:5172/api');
            loadApplications();
        };
    </script>
</body>
</html>