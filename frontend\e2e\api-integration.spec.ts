import { test, expect } from '@playwright/test';

test.describe('前后端连接测试', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('应该能够连接到后端API', async ({ page }) => {
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 检查是否有API请求成功的指示
    const apiStatus = page.locator('.api-status, [class*="api"], text=API');

    if (await apiStatus.count() > 0) {
      await expect(apiStatus.first()).toBeVisible();

      // 检查连接状态
      const connected = page.locator('text=已连接, text=连接成功, .connected, .success');
      const disconnected = page.locator('text=连接失败, text=断开, .disconnected, .error');

      // 应该显示连接状态（成功或失败都要有指示）
      const hasStatus = (await connected.count() > 0) || (await disconnected.count() > 0);
      expect(hasStatus).toBeTruthy();
    }
  });

  test('API测试按钮应该能正常工作', async ({ page }) => {
    // 查找API测试按钮
    const testButton = page.locator('button:has-text("测试")');

    if (await testButton.count() > 0) {
      await expect(testButton).toBeVisible();
      await expect(testButton).toBeEnabled();

      // 点击测试按钮
      await testButton.click();

      // 等待按钮状态变化
      await expect(testButton).toContainText('测试中...', { timeout: 2000 });

      // 等待测试完成
      await expect(testButton).toContainText('测试', { timeout: 10000 });
    }
  });

  test('应该能够获取服务类型数据', async ({ page }) => {
    // 监听网络请求
    const requestPromise = page.waitForRequest(request =>
      request.url().includes('/api/Metadata/service-types') && request.method() === 'GET'
    );

    const responsePromise = page.waitForResponse(response =>
      response.url().includes('/api/Metadata/service-types') && response.status() === 200
    );

    // 触发API请求（通过点击测试按钮）
    const testButton = page.locator('button:has-text("测试")');
    if (await testButton.count() > 0) {
      await testButton.click();

      try {
        // 等待请求和响应
        await Promise.race([
          Promise.all([requestPromise, responsePromise]),
          page.waitForTimeout(10000)
        ]);

        // 如果请求成功，验证响应
        const response = await responsePromise;
        expect(response.status()).toBe(200);
      } catch (error) {
        // 如果API请求失败，至少确保有错误处理
        console.log('API请求可能失败，检查错误处理');
      }
    }
  });

  test('应该正确处理CORS请求', async ({ page }) => {
    // 监听所有网络请求，检查CORS
    const requests: any[] = [];

    page.on('request', request => {
      if (request.url().includes('localhost:5172')) {
        requests.push({
          url: request.url(),
          method: request.method(),
          headers: request.headers()
        });
      }
    });

    // 触发一个API请求
    const testButton = page.locator('button:has-text("测试")');
    if (await testButton.count() > 0) {
      await testButton.click();
      await page.waitForTimeout(3000);

      // 检查是否有跨域请求
      if (requests.length > 0) {
        const apiRequest = requests[0];
        expect(apiRequest.url).toContain('localhost:5172');

        // 验证请求头
        expect(apiRequest.headers).toBeDefined();
      }
    }
  });

  test('应该正确显示API响应数据', async ({ page }) => {
    // 等待页面加载并查找数据显示
    await page.waitForLoadState('networkidle');

    // 检查是否有来自API的数据显示
    const dataElements = page.locator('.data, .response, .result, .api-data');

    // 或者检查页面是否加载了动态内容（非Mock数据）
    const dynamicContent = page.locator('[data-testid*="api"], [class*="api-"]');

    if (await dataElements.count() > 0 || await dynamicContent.count() > 0) {
      // 有API数据显示，验证其可见性
      const element = (await dataElements.count() > 0) ? dataElements.first() : dynamicContent.first();
      await expect(element).toBeVisible();
    }
  });

  test('应该正确处理API错误', async ({ page }) => {
    // 模拟网络错误或API不可用的情况
    await page.route('**/api/**', route => {
      route.abort('failed');
    });

    // 重新加载页面
    await page.reload();

    // 检查错误处理
    const errorIndicators = page.locator('text=错误, text=失败, text=连接失败, .error, .failed');

    // 应该有某种错误指示
    if (await errorIndicators.count() > 0) {
      await expect(errorIndicators.first()).toBeVisible();
    }

    // 或者检查是否回退到Mock数据
    const mockIndicators = page.locator('text=Mock, .mock-mode, .fallback');
    if (await mockIndicators.count() > 0) {
      await expect(mockIndicators.first()).toBeVisible();
    }
  });

  test('Mock模式和真实API模式应该都能工作', async ({ page }) => {
    // 如果有Mock切换功能
    const mockToggle = page.locator('button:has-text("Mock"), .mock-toggle');

    if (await mockToggle.count() > 0) {
      // 测试Mock模式
      await mockToggle.click();
      await page.waitForTimeout(1000);

      // 验证Mock模式指示
      const mockStatus = page.locator('text=Mock, .mock-active');
      if (await mockStatus.count() > 0) {
        await expect(mockStatus.first()).toBeVisible();
      }

      // 切换回真实API
      await mockToggle.click();
      await page.waitForTimeout(1000);

      // 验证真实API模式
      const realApiStatus = page.locator('text=真实, text=API, .real-api');
      if (await realApiStatus.count() > 0) {
        await expect(realApiStatus.first()).toBeVisible();
      }
    }
  });

  test('应该能正确处理超时请求', async ({ page }) => {
    // 模拟慢速API响应
    await page.route('**/api/**', async route => {
      await page.waitForTimeout(6000); // 超过默认超时时间
      route.continue();
    });

    // 触发API请求
    const testButton = page.locator('button:has-text("测试")');
    if (await testButton.count() > 0) {
      await testButton.click();

      // 等待超时处理
      await page.waitForTimeout(8000);

      // 检查是否有超时错误处理
      const timeoutIndicators = page.locator('text=超时, text=timeout, .timeout, .slow');
      if (await timeoutIndicators.count() > 0) {
        await expect(timeoutIndicators.first()).toBeVisible();
      }
    }
  });
});
