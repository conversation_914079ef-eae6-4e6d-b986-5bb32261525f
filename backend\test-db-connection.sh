#!/bin/bash

# 数据库连接测试脚本
# 测试与MCP MySQL服务器的连接

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印带颜色的消息
print_info() {
    echo -e "${BLUE}🔍 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info "测试数据库连接..."

# 数据库连接参数
DB_HOST="*************"
DB_PORT="3307"
DB_USER="dtauser"
DB_PASSWORD="dtauser"
DB_NAME="MTNOH_AAA_Platform"

echo
print_info "连接参数:"
echo "  主机: $DB_HOST"
echo "  端口: $DB_PORT"
echo "  用户: $DB_USER"
echo "  数据库: $DB_NAME"

# 测试网络连接
echo
print_info "测试网络连接..."
if command -v nc &> /dev/null; then
    if nc -z -w5 $DB_HOST $DB_PORT 2>/dev/null; then
        print_success "网络连接成功"
    else
        print_error "网络连接失败"
        exit 1
    fi
elif command -v telnet &> /dev/null; then
    if timeout 5 telnet $DB_HOST $DB_PORT &>/dev/null; then
        print_success "网络连接成功"
    else
        print_error "网络连接失败"
        exit 1
    fi
else
    print_warning "nc 或 telnet 未安装，跳过网络连接测试"
fi

# 测试MySQL连接
echo
print_info "测试MySQL连接..."
if command -v mysql &> /dev/null; then
    if mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASSWORD -e "SELECT 1 as test;" &>/dev/null; then
        print_success "MySQL连接成功"
    else
        print_error "MySQL连接失败"
        print_info "请检查以下事项:"
        echo "  - MySQL服务器是否运行"
        echo "  - 用户名密码是否正确"
        echo "  - 数据库是否存在"
        echo "  - 网络防火墙设置"
    fi
else
    print_warning "MySQL客户端未安装，跳过数据库连接测试"
    print_info "可以通过以下命令安装MySQL客户端:"
    echo "  Ubuntu/Debian: sudo apt-get install mysql-client"
    echo "  CentOS/RHEL: sudo yum install mysql"
    echo "  macOS: brew install mysql-client"
fi

# 验证.NET配置文件
echo
print_info "验证.NET配置..."
config_files=("appsettings.json" "appsettings.Testing.json")

for config_file in "${config_files[@]}"; do
    if [ -f "$config_file" ]; then
        if command -v jq &> /dev/null; then
            connection_string=$(jq -r '.ConnectionStrings.DefaultConnection' "$config_file" 2>/dev/null)
            if [[ $connection_string == *"*************"* && $connection_string == *"3307"* && $connection_string == *"MTNOH_AAA_Platform"* ]]; then
                print_success "$config_file 配置正确"
            else
                print_error "$config_file 配置可能有误"
                echo "  当前连接字符串: $connection_string"
            fi
        else
            if grep -q "*************.*3307.*MTNOH_AAA_Platform" "$config_file"; then
                print_success "$config_file 配置正确"
            else
                print_warning "$config_file 配置可能有误，建议手动检查"
            fi
        fi
    else
        print_warning "配置文件不存在: $config_file"
    fi
done

echo
print_info "配置摘要:"
echo "  - 数据库已配置为连接到MCP MySQL服务器"
echo "  - 主配置文件: appsettings.json"
echo "  - 测试配置文件: appsettings.Testing.json"
echo "  - Docker配置已更新以使用外部数据库"

echo
print_info "接下来的步骤:"
echo "  1. 确保MCP MySQL服务器正在运行"
echo "  2. 验证网络连接和防火墙设置"
echo "  3. 运行后端应用测试数据库连接"
echo "  4. 检查数据库中是否存在所需的表结构"

echo
print_success "配置更新完成！"