/**
 * 测试数据库初始化器
 * 负责创建和管理测试数据库、初始化测试数据
 */

using MapStudio.Api.Models.Entities;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace MapStudio.Api.Data.Context
{
    public class TestDatabaseInitializer
    {
        private readonly ISqlSugarClient _db;
        private readonly IConfiguration _configuration;
        private readonly ILogger<TestDatabaseInitializer> _logger;

        public TestDatabaseInitializer(
            ISqlSugarClient db, 
            IConfiguration configuration,
            ILogger<TestDatabaseInitializer> logger)
        {
            _db = db;
            _configuration = configuration;
            _logger = logger;
        }

        /// <summary>
        /// 初始化测试数据库
        /// </summary>
        public async Task InitializeAsync()
        {
            try
            {
                _logger.LogInformation("开始初始化测试数据库...");

                // 1. 创建数据库（如果不存在）
                await CreateDatabaseIfNotExistsAsync();

                // 2. 创建表结构
                await CreateTablesAsync();

                // 3. 清理现有测试数据
                await CleanTestDataAsync();

                // 4. 种子测试数据
                if (_configuration.GetValue<bool>("TestSettings:AutoSeedData"))
                {
                    await SeedTestDataAsync();
                }

                _logger.LogInformation("测试数据库初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试数据库初始化失败");
                throw;
            }
        }

        /// <summary>
        /// 创建数据库
        /// </summary>
        private async Task CreateDatabaseIfNotExistsAsync()
        {
            try
            {
                // 获取数据库名称
                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                var dbName = ExtractDatabaseName(connectionString);

                // 创建数据库
                _db.DbMaintenance.CreateDatabase(dbName);
                _logger.LogInformation($"数据库 {dbName} 创建成功或已存在");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "创建数据库时出现警告，可能数据库已存在");
            }
        }

        /// <summary>
        /// 创建表结构
        /// </summary>
        private Task CreateTablesAsync()
        {
            // 检查并创建所有实体表
            CheckAndCreateTable<Models.Entities.User>();
            CheckAndCreateTable<Models.Entities.Tenant>();
            CheckAndCreateTable<Models.Entities.AuthApplication>();
            CheckAndCreateTable<Models.Entities.OperationLog>();
            CheckAndCreateTable<Models.Entities.ServiceType>();
            CheckAndCreateTable<Models.Entities.PermissionScope>();
            CheckAndCreateTable<Models.Entities.File>();
            CheckAndCreateTable<Models.Entities.AlarmSetting>();
            CheckAndCreateTable<Models.Entities.SmsAlarmSetting>();
            CheckAndCreateTable<Models.Entities.SmsPhoneSetting>();
            CheckAndCreateTable<Models.Entities.SmsTemplateSetting>();

            _logger.LogInformation("数据库表结构创建完成");
            return Task.CompletedTask;
        }

        /// <summary>
        /// 检查并创建表
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        private void CheckAndCreateTable<T>() where T : class, new()
        {
            var entityType = typeof(T);
            // 使用SqlSugar的EntityMaintenance获取表名
            var tableName = _db.EntityMaintenance.GetEntityInfo(entityType).DbTableName;
            
            // 检查表是否存在
            if (!_db.DbMaintenance.IsAnyTable(tableName))
            {
                // 如果表不存在，则创建表
                _db.CodeFirst.InitTables<T>();
                _logger.LogInformation($"表 {tableName} 不存在，已自动创建");
            }
            else
            {
                _logger.LogInformation($"表 {tableName} 已存在");
            }
        }

        /// <summary>
        /// 清理测试数据
        /// </summary>
        public async Task CleanTestDataAsync()
        {
            try
            {
                _logger.LogInformation("开始清理测试数据...");

                // 清理顺序很重要，需要考虑外键约束
                await _db.Deleteable<Models.Entities.OperationLog>().Where(x => x.Id != null).ExecuteCommandAsync();
                await _db.Deleteable<Models.Entities.AuthApplication>().Where(x => x.Id != null).ExecuteCommandAsync();
                await _db.Deleteable<Models.Entities.File>().Where(x => x.Id != null).ExecuteCommandAsync();
                await _db.Deleteable<Models.Entities.Tenant>().Where(x => x.Id != null).ExecuteCommandAsync();
                await _db.Deleteable<Models.Entities.User>().Where(x => x.Name.Contains("测试") || x.Name.Contains("Test")).ExecuteCommandAsync();

                _logger.LogInformation("测试数据清理完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理测试数据失败");
                throw;
            }
        }

        /// <summary>
        /// 种子测试数据
        /// </summary>
        private async Task SeedTestDataAsync()
        {
            try
            {
                _logger.LogInformation("开始种子测试数据...");

                // 1. 种子系统用户
                await SeedUsersAsync();

                // 2. 种子服务类型
                await SeedServiceTypesAsync();

                // 3. 种子权限范围
                await SeedPermissionScopesAsync();

                // 4. 种子测试租户
                await SeedTenantsAsync();

                // 5. 种子测试申请
                await SeedApplicationsAsync();

                // 6. 种子操作日志
                await SeedOperationLogsAsync();

                // 7. 种子告警设置
                await SeedAlarmSettingsAsync();

                _logger.LogInformation("测试数据种子完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "种子测试数据失败");
                throw;
            }
        }

        /// <summary>
        /// 种子用户数据
        /// </summary>
        public async Task SeedUsersAsync()
        {
            var users = new List<Models.Entities.User>
            {
                new Models.Entities.User
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "系统管理员",
                    Email = "<EMAIL>",
                    Role = "admin",
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                },
                new Models.Entities.User
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "测试用户",
                    Email = "<EMAIL>",
                    Role = "user",
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                },
                new Models.Entities.User
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "审批员",
                    Email = "<EMAIL>",
                    Role = "approver",
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                }
            };

            await _db.Insertable(users).ExecuteCommandAsync();
            _logger.LogInformation($"已种子 {users.Count} 个用户");
        }

        /// <summary>
        /// 种子服务类型数据
        /// </summary>
        public async Task SeedServiceTypesAsync()
        {
            var serviceTypes = new List<Models.Entities.ServiceType>
            {
                new Models.Entities.ServiceType
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "基础地图服务",
                    Description = "提供基础地图展示、查询功能",
                    IsActive = true
                },
                new Models.Entities.ServiceType
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "高级地图服务",
                    Description = "提供高级分析、编辑功能",
                    IsActive = true
                },
                new Models.Entities.ServiceType
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "企业级地图服务",
                    Description = "提供企业级功能和优先支持",
                    IsActive = true
                },
                new Models.Entities.ServiceType
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "定制化地图服务",
                    Description = "提供定制化开发和集成服务",
                    IsActive = true
                }
            };

            await _db.Insertable(serviceTypes).ExecuteCommandAsync();
            _logger.LogInformation($"已种子 {serviceTypes.Count} 个服务类型");
        }

        /// <summary>
        /// 种子权限范围数据
        /// </summary>
        public async Task SeedPermissionScopesAsync()
        {
            var permissionScopes = new List<Models.Entities.PermissionScope>
            {
                new Models.Entities.PermissionScope
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "只读权限",
                    Description = "仅可查看和浏览地图数据",
                    Permissions = "read"
                },
                new Models.Entities.PermissionScope
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "读写权限",
                    Description = "可查看、编辑和创建地图数据",
                    Permissions = "read,write"
                },
                new Models.Entities.PermissionScope
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "管理员权限",
                    Description = "具备完整的管理和配置权限",
                    Permissions = "read,write,admin"
                }
            };

            await _db.Insertable(permissionScopes).ExecuteCommandAsync();
            _logger.LogInformation($"已种子 {permissionScopes.Count} 个权限范围");
        }

        /// <summary>
        /// 种子租户数据
        /// </summary>
        private async Task SeedTenantsAsync()
        {
            var tenants = new List<Models.Entities.Tenant>
            {
                new Models.Entities.Tenant
                {
                    Id = Guid.NewGuid().ToString(),
                    TenantName = "智慧城市科技有限公司",
                    TenantId = "SMART_CITY_TECH",
                    ContactPerson = "张三",
                    ContactEmail = "<EMAIL>",
                    ContactPhone = "13800138000",
                    Status = "Active",
                    CreatedAt = DateTime.Now.AddDays(-30),
                    UpdatedAt = DateTime.Now
                },
                new Models.Entities.Tenant
                {
                    Id = Guid.NewGuid().ToString(),
                    TenantName = "绿色能源开发有限公司",
                    TenantId = "GREEN_ENERGY_DEV",
                    ContactPerson = "李四",
                    ContactEmail = "<EMAIL>",
                    ContactPhone = "13900139000",
                    Status = "Active",
                    CreatedAt = DateTime.Now.AddDays(-20),
                    UpdatedAt = DateTime.Now
                }
            };

            await _db.Insertable(tenants).ExecuteCommandAsync();
            _logger.LogInformation($"已种子 {tenants.Count} 个租户");
        }

        /// <summary>
        /// 种子申请数据
        /// </summary>
        private async Task SeedApplicationsAsync()
        {
            var applications = new List<Models.Entities.AuthApplication>
            {
                new Models.Entities.AuthApplication
                {
                    Id = "TA-" + DateTime.Now.ToString("yyyyMMdd") + "001",
                    TenantName = "测试科技有限公司",
                    TenantId = "TEST_TECH_CORP",
                    ContactPerson = "王五",
                    ContactEmail = "<EMAIL>",
                    ContactPhone = "13700137000",
                    ServiceType = "enterprise",
                    AuthPeriod = "1year",
                    PermissionScope = "admin",
                    Description = "测试企业申请",
                    Status = "Pending",
                    AppliedAt = DateTime.Now.AddHours(-2)
                },
                new Models.Entities.AuthApplication
                {
                    Id = "TA-" + DateTime.Now.ToString("yyyyMMdd") + "002",
                    TenantName = "物流配送科技公司",
                    TenantId = "LOGISTICS_TECH",
                    ContactPerson = "赵六",
                    ContactEmail = "<EMAIL>",
                    ContactPhone = "13600136000",
                    ServiceType = "advanced",
                    AuthPeriod = "6months",
                    PermissionScope = "write",
                    Description = "物流行业地图服务申请",
                    Status = "Approved",
                    AppliedAt = DateTime.Now.AddDays(-1),
                    ApprovedAt = DateTime.Now.AddHours(-1)
                }
            };

            await _db.Insertable(applications).ExecuteCommandAsync();
            _logger.LogInformation($"已种子 {applications.Count} 个申请");
        }

        /// <summary>
        /// 种子操作日志数据
        /// </summary>
        private async Task SeedOperationLogsAsync()
        {
            var operationLogs = new List<Models.Entities.OperationLog>();
            var random = new Random();
            var actionTypes = new[] { "create", "update", "delete", "query", "login", "logout" };
            var tenantIds = new[] { "SMART_CITY_TECH", "GREEN_ENERGY_DEV", "TEST_TECH_CORP" };
            var userIds = await _db.Queryable<Models.Entities.User>().Select(u => u.Id).ToListAsync();

            for (int i = 0; i < 50; i++)
            {
                operationLogs.Add(new Models.Entities.OperationLog
                {
                    Id = Guid.NewGuid().ToString(),
                    TenantId = tenantIds[random.Next(tenantIds.Length)],
                    UserId = userIds[random.Next(userIds.Count)],
                    ActionType = actionTypes[random.Next(actionTypes.Length)],
                    Description = $"测试操作日志 {i + 1}",
                    ActionObject = $"测试对象 {i + 1}",
                    OperatedAt = DateTime.Now.AddMinutes(-random.Next(1, 1440)) // 随机过去24小时内
                });
            }

            await _db.Insertable(operationLogs).ExecuteCommandAsync();
            _logger.LogInformation($"已种子 {operationLogs.Count} 条操作日志");
        }

        /// <summary>
        /// 种子告警设置数据
        /// </summary>
        private async Task SeedAlarmSettingsAsync()
        {
            var alarmSetting = new Models.Entities.AlarmSetting
            {
                Id = Guid.NewGuid().ToString(),
                EmailTemplate = "系统告警：{Title}\n\n详细信息：{Description}\n\n时间：{Timestamp}",
                EmailList = """["<EMAIL>", "<EMAIL>"]""",
                IsActive = true,
                UpdatedAt = DateTime.Now
            };

            await _db.Insertable(alarmSetting).ExecuteCommandAsync();
            _logger.LogInformation("已种子告警设置");
        }

        /// <summary>
        /// 提取数据库名称
        /// </summary>
        private string ExtractDatabaseName(string connectionString)
        {
            var parts = connectionString.Split(';');
            foreach (var part in parts)
            {
                var kvp = part.Split('=');
                if (kvp.Length == 2 && kvp[0].Trim().ToLower() == "database")
                {
                    return kvp[1].Trim();
                }
            }
            return "mapstudio_test";
        }

        /// <summary>
        /// 重置数据库
        /// </summary>
        public async Task ResetDatabaseAsync()
        {
            _logger.LogInformation("重置测试数据库...");
            
            await CleanTestDataAsync();
            await SeedTestDataAsync();
            
            _logger.LogInformation("测试数据库重置完成");
        }

        /// <summary>
        /// 备份测试数据
        /// </summary>
        public async Task BackupTestDataAsync(string backupPath)
        {
            try
            {
                _logger.LogInformation($"备份测试数据到: {backupPath}");
                
                // 这里可以实现数据备份逻辑
                // 例如导出到JSON文件或SQL脚本
                
                _logger.LogInformation("测试数据备份完成");
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "备份测试数据失败");
                throw;
            }
        }

        /// <summary>
        /// 恢复测试数据
        /// </summary>
        public async Task RestoreTestDataAsync(string backupPath)
        {
            try
            {
                _logger.LogInformation($"从备份恢复测试数据: {backupPath}");
                
                // 这里可以实现数据恢复逻辑
                
                _logger.LogInformation("测试数据恢复完成");
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "恢复测试数据失败");
                throw;
            }
        }
    }
}