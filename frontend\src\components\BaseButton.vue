<template>
  <button
    :class="buttonClasses"
    :disabled="disabled"
    @click="handleClick"
  >
    <i v-if="icon" :class="iconClasses"></i>
    <span v-if="$slots.default" :class="textClasses">
      <slot></slot>
    </span>
    <i v-if="loading" class="loading-spinner ml-2"></i>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { cn, conditionalClass } from '@/utils/styleUtils';
import { componentPresets } from '@/styles/shared-config';

interface Props {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  icon?: string;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  rounded?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md',
  disabled: false,
  loading: false,
  iconPosition: 'left',
  fullWidth: false,
  rounded: 'md'
});

const emit = defineEmits<{
  click: [event: MouseEvent];
}>();

// 计算按钮样式类
const buttonClasses = computed(() => {
  const baseClass = componentPresets.button.base;
  const variantClass = componentPresets.button.variants[props.variant];
  const sizeClass = componentPresets.button.sizes[props.size];
  // 从设计系统中获取圆角样式
  const roundedClass = props.rounded ? `rounded-${props.rounded}` : '';
  const widthClass = props.fullWidth ? 'w-full' : '';
  const loadingClass = props.loading ? 'cursor-wait' : '';

  return cn(
    baseClass,
    variantClass,
    sizeClass,
    roundedClass,
    widthClass,
    loadingClass
  );
});

// 计算图标样式类
const iconClasses = computed(() => {
  if (!props.icon) return '';
  
  const baseIconClass = `fa-solid ${props.icon}`;
  const positionClass = props.iconPosition === 'right' ? 'ml-2' : 'mr-2';
  const sizeClass = props.size === 'sm' ? 'text-xs' : props.size === 'lg' ? 'text-base' : 'text-sm';
  
  return cn(baseIconClass, positionClass, sizeClass);
});

// 计算文本样式类
const textClasses = computed(() => {
  return props.icon && props.iconPosition === 'right' ? 'order-first' : '';
});

// 处理点击事件
const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event);
  }
};
</script>

<style scoped>
/* 组件特定样式 */
.loading-spinner {
  @apply animate-spin rounded-full h-4 w-4 border-b-2 border-current;
}
</style>