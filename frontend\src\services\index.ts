/**
 * API服务统一入口
 * 导出所有业务服务模块
 */

export { AuthService } from './auth.service';
export { TenantService } from './tenant.service';
export { OperationService } from './operation.service';
export { FileService } from './file.service';
export { MetadataService } from './metadata.service';

// 导出类型定义
export * from './types';

// 导出配置和工具
export { apiConfig, toggleMockMode, getCurrentMockMode, getEnvironmentInfo, checkApiConnection } from '../config/api.config';
export { API_ENDPOINTS } from '../config/endpoints';
export { httpClient } from '../utils/http.client';

/**
 * 服务初始化
 * 在应用启动时调用，进行必要的初始化工作
 */
export const initializeServices = async (): Promise<void> => {
  try {
    console.log('🚀 初始化API服务...');

    // 检查API连接状态
    const { checkApiConnection } = await import('../config/api.config');
    const isConnected = await checkApiConnection();
    if (!isConnected) {
      console.warn('⚠️ API连接检查失败，可能影响功能使用');
    }

    // 预加载基础数据
    const { MetadataService } = await import('./metadata.service');
    await MetadataService.getAllMetadata();

    console.log('✅ API服务初始化完成');
  } catch (error) {
    console.error('❌ API服务初始化失败:', error);
    // 不抛出异常，允许应用继续启动
  }
};

/**
 * 服务健康检查
 * 检查所有关键服务的可用性
 */
export const healthCheck = async (): Promise<{
  api: boolean;
  auth: boolean;
  services: boolean;
}> => {
  const result = {
    api: false,
    auth: false,
    services: false
  };

  try {
    // 检查API连接
    const { checkApiConnection } = await import('../config/api.config');
    result.api = await checkApiConnection();

    // 检查认证状态
    const { AuthService } = await import('./auth.service');
    result.auth = AuthService.isAuthenticated();

    // 检查服务可用性
    try {
      const { MetadataService } = await import('./metadata.service');
      await MetadataService.getServiceTypes();
      result.services = true;
    } catch (error) {
      result.services = false;
    }

  } catch (error) {
    console.error('健康检查失败:', error);
  }

  return result;
};
