<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mock/API模式切换测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .switch-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border: 2px solid #e9ecef;
        }
        .current-mode {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            margin-left: 15px;
        }
        .mock-mode {
            background-color: #ffeaa7;
            color: #fdcb6e;
        }
        .api-mode {
            background-color: #a8e6cf;
            color: #00b894;
        }
        .button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button.mock-btn {
            background-color: #f39c12;
        }
        .button.mock-btn:hover {
            background-color: #e67e22;
        }
        .button.api-btn {
            background-color: #27ae60;
        }
        .button.api-btn:hover {
            background-color: #229954;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .comparison-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
        }
        .comparison-item h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h3 {
            color: #5d6d7e;
            margin-top: 25px;
        }
        .info-box {
            background-color: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-box {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Mock/API模式切换测试工具</h1>
        
        <div class="info-box">
            <strong>📖 使用说明:</strong><br>
            • 前端项目支持Mock数据与真实API的智能切换<br>
            • Mock模式：使用本地模拟数据，无需后端服务<br>
            • API模式：调用真实后端接口，需要后端服务运行<br>
            • 可以一键切换模式并对比测试结果
        </div>

        <!-- 模式切换区域 -->
        <div class="switch-section">
            <h2>🎯 当前模式</h2>
            <span>当前模式：</span>
            <span id="currentMode" class="current-mode">检测中...</span>
            <br><br>
            <button class="button mock-btn" onclick="switchToMock()">🎭 切换到Mock模式</button>
            <button class="button api-btn" onclick="switchToAPI()">🌐 切换到API模式</button>
            <button class="button" onclick="checkCurrentMode()">🔍 检查当前模式</button>
        </div>

        <!-- 功能测试区域 -->
        <div class="test-section">
            <h2>🧪 功能测试</h2>
            <button class="button" onclick="testTenantApplication()">📝 测试租户申请提交</button>
            <button class="button" onclick="testApplicationList()">📋 测试申请列表获取</button>
            <button class="button" onclick="testApproval()">✅ 测试审批功能</button>
            <button class="button" onclick="runFullTest()">🚀 运行完整测试</button>
            <button class="button" onclick="clearResults()">🧹 清除结果</button>
            
            <div id="testResults" class="result" style="display: none;"></div>
        </div>

        <!-- 对比测试区域 -->
        <div class="test-section">
            <h2>⚖️ 对比测试</h2>
            <p>同时在Mock模式和API模式下运行相同的测试，对比结果：</p>
            <button class="button" onclick="runComparisonTest()">🔬 运行对比测试</button>
            
            <div class="comparison" id="comparisonResults" style="display: none;">
                <div class="comparison-item">
                    <h4>🎭 Mock模式结果</h4>
                    <div id="mockResults" class="result"></div>
                </div>
                <div class="comparison-item">
                    <h4>🌐 API模式结果</h4>
                    <div id="apiResults" class="result"></div>
                </div>
            </div>
        </div>

        <div class="warning-box">
            <strong>⚠️ 注意事项:</strong><br>
            • 切换到API模式前，请确保后端服务正在运行（http://localhost:5172）<br>
            • Mock模式使用本地数据，不会影响真实数据库<br>
            • API模式会实际操作数据库，请注意数据安全
        </div>
    </div>

    <script>
        // 检测当前模式
        function checkCurrentMode() {
            const useMock = localStorage.getItem('api_mock_mode') === 'true';
            const modeElement = document.getElementById('currentMode');
            
            if (useMock) {
                modeElement.textContent = 'Mock模式';
                modeElement.className = 'current-mode mock-mode';
            } else {
                modeElement.textContent = 'API模式';
                modeElement.className = 'current-mode api-mode';
            }
            
            log(`🔍 当前模式: ${useMock ? 'Mock模式' : 'API模式'}`);
        }

        // 切换到Mock模式
        function switchToMock() {
            localStorage.setItem('api_mock_mode', 'true');
            log('🎭 已切换到Mock模式');
            checkCurrentMode();
            
            if (confirm('模式已切换，是否重新加载页面以应用更改？')) {
                window.location.reload();
            }
        }

        // 切换到API模式
        function switchToAPI() {
            localStorage.setItem('api_mock_mode', 'false');
            log('🌐 已切换到API模式');
            checkCurrentMode();
            
            if (confirm('模式已切换，是否重新加载页面以应用更改？')) {
                window.location.reload();
            }
        }

        // 日志输出
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            console.log(`[${timestamp}] ${message}`);
            
            const resultsDiv = document.getElementById('testResults');
            if (resultsDiv.style.display !== 'none') {
                resultsDiv.textContent += `[${timestamp}] ${message}\n`;
                resultsDiv.scrollTop = resultsDiv.scrollHeight;
            }
        }

        // 测试函数
        async function testTenantApplication() {
            showResults();
            log('📝 开始测试租户申请提交...');
            
            const testData = {
                tenantName: '测试科技有限公司',
                tenantId: 'TEST_TECH_' + Date.now(),
                contactPerson: '张三',
                contactEmail: '<EMAIL>',
                contactPhone: '13800138000',
                serviceType: 'enterprise',
                authPeriod: '1year',
                permissionScope: 'admin',
                description: '测试申请'
            };

            try {
                const response = await makeAPICall('/Tenant/auth-applications', 'POST', testData);
                log(`✅ 申请提交成功: ${JSON.stringify(response, null, 2)}`);
                return response;
            } catch (error) {
                log(`❌ 申请提交失败: ${error.message}`);
                throw error;
            }
        }

        async function testApplicationList() {
            showResults();
            log('📋 开始测试申请列表获取...');
            
            try {
                const response = await makeAPICall('/Tenant/auth-applications?page=1&pageSize=10&status=pending', 'GET');
                log(`✅ 获取列表成功: ${JSON.stringify(response, null, 2)}`);
                return response;
            } catch (error) {
                log(`❌ 获取列表失败: ${error.message}`);
                throw error;
            }
        }

        async function testApproval() {
            showResults();
            log('✅ 开始测试审批功能...');
            
            // 需要先有申请ID，这里使用Mock ID
            const applicationId = 'TA-20250825';
            const approvalData = {
                action: 'approve',
                comments: '测试审批通过'
            };

            try {
                const response = await makeAPICall(`/Tenant/auth-applications/${applicationId}/approve`, 'PUT', approvalData);
                log(`✅ 审批成功: ${JSON.stringify(response, null, 2)}`);
                return response;
            } catch (error) {
                log(`❌ 审批失败: ${error.message}`);
                throw error;
            }
        }

        async function runFullTest() {
            showResults();
            log('🚀 开始运行完整测试流程...\n');
            
            try {
                // 1. 提交申请
                log('1. 测试申请提交...');
                const submitResult = await testTenantApplication();
                await sleep(1000);

                // 2. 获取列表
                log('\n2. 测试列表获取...');
                const listResult = await testApplicationList();
                await sleep(1000);

                // 3. 测试审批
                log('\n3. 测试审批功能...');
                const approvalResult = await testApproval();

                log('\n🎉 完整测试流程成功完成！');
                
            } catch (error) {
                log(`\n💥 测试流程失败: ${error.message}`);
            }
        }

        async function runComparisonTest() {
            const comparisonDiv = document.getElementById('comparisonResults');
            const mockDiv = document.getElementById('mockResults');
            const apiDiv = document.getElementById('apiResults');
            
            comparisonDiv.style.display = 'grid';
            mockDiv.textContent = '';
            apiDiv.textContent = '';

            // Mock模式测试
            mockDiv.textContent += '切换到Mock模式进行测试...\n';
            localStorage.setItem('api_mock_mode', 'true');
            
            try {
                const mockResult = await makeAPICall('/Tenant/auth-applications?page=1&pageSize=5', 'GET');
                mockDiv.textContent += `✅ Mock测试成功:\n${JSON.stringify(mockResult, null, 2)}\n`;
                mockDiv.className = 'result success';
            } catch (error) {
                mockDiv.textContent += `❌ Mock测试失败: ${error.message}\n`;
                mockDiv.className = 'result error';
            }

            // API模式测试
            apiDiv.textContent += '切换到API模式进行测试...\n';
            localStorage.setItem('api_mock_mode', 'false');
            
            try {
                const apiResult = await makeAPICall('/Tenant/auth-applications?page=1&pageSize=5', 'GET');
                apiDiv.textContent += `✅ API测试成功:\n${JSON.stringify(apiResult, null, 2)}\n`;
                apiDiv.className = 'result success';
            } catch (error) {
                apiDiv.textContent += `❌ API测试失败: ${error.message}\n`;
                apiDiv.className = 'result error';
            }

            checkCurrentMode();
        }

        // API调用函数
        async function makeAPICall(endpoint, method, data = null) {
            const isUseMock = localStorage.getItem('api_mock_mode') === 'true';
            const baseURL = isUseMock ? '' : 'http://localhost:5172/api';
            const url = `${baseURL}${endpoint}`;

            // 如果是Mock模式，直接返回Mock数据
            if (isUseMock) {
                return getMockData(endpoint, method, data);
            }

            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                }
            };

            if (data && (method === 'POST' || method === 'PUT')) {
                options.body = JSON.stringify(data);
            }

            const response = await fetch(url, options);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        }

        // Mock数据
        function getMockData(endpoint, method, data) {
            const mockData = {
                'GET:/Tenant/auth-applications': {
                    success: true,
                    data: {
                        items: [
                            {
                                id: 'TA-MOCK-001',
                                tenantName: 'Mock测试公司',
                                applyTime: '2025-08-26 10:00:00',
                                serviceType: 'enterprise',
                                serviceTypeName: '企业级地图服务',
                                authPeriod: '1year',
                                authPeriodName: '1年',
                                contactPerson: 'Mock测试员',
                                contactPhone: '13800138000',
                                contactEmail: '<EMAIL>',
                                status: 'pending'
                            }
                        ],
                        totalCount: 1,
                        currentPage: 1,
                        pageSize: 10,
                        totalPages: 1
                    },
                    message: '获取申请列表成功'
                },
                'POST:/Tenant/auth-applications': {
                    success: true,
                    data: {
                        id: 'TA-MOCK-' + Date.now(),
                        status: 'Pending',
                        appliedAt: new Date().toISOString(),
                        message: 'Mock申请提交成功'
                    },
                    message: '申请提交成功'
                }
            };

            const key = `${method}:${endpoint.split('?')[0]}`;
            return mockData[key] || { 
                success: false, 
                message: `Mock数据未找到: ${key}` 
            };
        }

        // 工具函数
        function showResults() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.style.display = 'block';
            resultsDiv.textContent = '';
            resultsDiv.className = 'result';
        }

        function clearResults() {
            document.getElementById('testResults').style.display = 'none';
            document.getElementById('comparisonResults').style.display = 'none';
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // 页面加载时检查当前模式
        window.onload = function() {
            checkCurrentMode();
            log('页面加载完成，Mock/API切换测试工具就绪');
        };
    </script>
</body>
</html>