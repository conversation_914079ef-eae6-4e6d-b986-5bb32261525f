# Map Studio 前端应用 Dockerfile
# 支持开发、测试和生产环境的多阶段构建

# Node.js 基础镜像
FROM node:18-alpine AS base

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
RUN apk add --no-cache \
    git \
    curl \
    python3 \
    make \
    g++

# 开发环境
FROM base AS development

# 设置开发环境变量
ENV NODE_ENV=development
ENV VITE_API_BASE_URL=http://localhost:5000/api

# 复制 package 文件
COPY package*.json ./

# 安装依赖 (包括开发依赖)
RUN npm ci

# 复制源代码
COPY . .

# 暴露端口
EXPOSE 3001

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:3001 || exit 1

# 开发服务启动命令
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "3001"]

# 构建阶段
FROM base AS build-stage

# 设置生产环境变量
ENV NODE_ENV=production
ENV VITE_API_BASE_URL=/api

# 复制 package 文件
COPY package*.json ./

# 安装生产依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产环境 - Nginx
FROM nginx:alpine AS production

# 安装必要的工具
RUN apk add --no-cache curl

# 复制构建的文件
COPY --from=build-stage /app/dist /usr/share/nginx/html

# 复制 Nginx 配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 创建 Nginx 配置目录
RUN mkdir -p /var/cache/nginx/client_temp && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /usr/share/nginx/html

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost || exit 1

# 启动 Nginx
CMD ["nginx", "-g", "daemon off;"]

# 测试环境
FROM development AS testing

# 安装测试相关的全局包
RUN npm install -g @playwright/test

# 安装 Playwright 浏览器
RUN npx playwright install --with-deps chromium firefox

# 设置测试环境变量
ENV NODE_ENV=test
ENV CI=true

# 复制测试配置
COPY playwright.config.ts ./
COPY playwright-e2e.config.ts ./

# 暴露测试端口
EXPOSE 3001

# 运行测试的命令
CMD ["npm", "run", "test:e2e"]

# 静态文件服务器 (用于生产构建的本地测试)
FROM node:18-alpine AS serve

WORKDIR /app

# 安装 serve
RUN npm install -g serve

# 复制构建的文件
COPY --from=build-stage /app/dist ./dist

# 暴露端口
EXPOSE 3001

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:3001 || exit 1

# 启动静态文件服务器
CMD ["serve", "-s", "dist", "-l", "3001"]