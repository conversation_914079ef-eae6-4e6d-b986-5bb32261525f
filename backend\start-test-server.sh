#!/bin/bash

# Map Studio 后端测试环境启动脚本
# 用于启动开发和测试环境的后端服务

set -e

echo "🚀 启动 Map Studio 后端测试环境..."

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查参数
ENVIRONMENT=${1:-Testing}
PORT=${2:-5000}

print_info "环境: $ENVIRONMENT"
print_info "端口: $PORT"

# 检查 .NET 是否安装
if ! command -v dotnet &> /dev/null; then
    print_error ".NET SDK 未安装或未在PATH中"
    exit 1
fi

print_success ".NET SDK 已安装: $(dotnet --version)"

# 检查项目文件
if [ ! -f "MapStudio.Api.csproj" ]; then
    print_error "MapStudio.Api.csproj 文件未找到"
    print_info "请确保在后端项目根目录运行此脚本"
    exit 1
fi

# 检查数据库连接
print_info "检查数据库连接..."

# 从配置文件读取数据库连接信息
DB_HOST="*************"
DB_PORT="3307"
DB_NAME="MTNOH_AAA_Platform"

# 检查MySQL是否运行
if command -v mysql &> /dev/null; then
    if mysql -h $DB_HOST -P $DB_PORT -u root -e "SELECT 1" &> /dev/null; then
        print_success "MySQL 数据库连接正常"
    else
        print_warning "MySQL 数据库连接失败，请确保MySQL服务已启动"
        print_info "或使用Docker启动MySQL:"
        print_info "docker run --name mapstudio-mysql -e MYSQL_ROOT_PASSWORD=test_password -p 3306:3306 -d mysql:8.0"
    fi
else
    print_warning "MySQL 客户端未安装，跳过数据库连接检查"
fi

# 创建必要的目录
print_info "创建必要的目录..."

mkdir -p logs
mkdir -p test_uploads
mkdir -p temp

print_success "目录创建完成"

# 恢复NuGet包
print_info "恢复NuGet包..."

if dotnet restore; then
    print_success "NuGet包恢复完成"
else
    print_error "NuGet包恢复失败"
    exit 1
fi

# 构建项目
print_info "构建项目..."

if dotnet build --configuration Debug; then
    print_success "项目构建完成"
else
    print_error "项目构建失败"
    exit 1
fi

# 设置环境变量
export ASPNETCORE_ENVIRONMENT=$ENVIRONMENT
export ASPNETCORE_URLS="http://localhost:$PORT"

# 启动应用
print_info "启动应用..."
print_info "访问地址: http://localhost:$PORT"
print_info "API文档: http://localhost:$PORT/swagger"

# 捕获中断信号
trap 'print_info "正在停止服务..."; kill $APP_PID 2>/dev/null; exit 0' INT TERM

# 启动应用并获取PID
dotnet run --environment $ENVIRONMENT --urls "http://localhost:$PORT" &
APP_PID=$!

print_success "应用已启动 (PID: $APP_PID)"
print_info "按 Ctrl+C 停止服务"

# 等待应用进程
wait $APP_PID