<template>
  <div class="p-6 space-y-6">
    <div class="bg-white rounded-lg shadow p-6">
      <h1 class="text-2xl font-bold mb-4">运营统计API测试</h1>
      
      <!-- Mock/API切换 -->
      <div class="mb-6 p-4 bg-gray-50 rounded-lg">
        <h2 class="text-lg font-semibold mb-2">API模式切换</h2>
        <div class="flex items-center space-x-4">
          <label class="flex items-center">
            <input 
              type="radio" 
              :value="false" 
              v-model="mockMode" 
              class="mr-2"
            >
            <span>API模式</span>
          </label>
          <label class="flex items-center">
            <input 
              type="radio" 
              :value="true" 
              v-model="mockMode" 
              class="mr-2"
            >
            <span>Mock模式</span>
          </label>
          <button 
            @click="toggleMockMode"
            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            切换模式
          </button>
        </div>
        <p class="text-sm text-gray-600 mt-2">
          当前模式: {{ mockMode ? 'Mock模式' : 'API模式' }}
        </p>
      </div>

      <!-- API测试按钮 -->
      <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
        <button 
          @click="testDailyStats" 
          :disabled="testing"
          class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
        >
          测试日度统计
        </button>
        <button 
          @click="testMonthlyStats" 
          :disabled="testing"
          class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
        >
          测试月度统计
        </button>
        <button 
          @click="testAccessStats" 
          :disabled="testing"
          class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
        >
          测试访问统计
        </button>
        <button 
          @click="testActionTypeRank" 
          :disabled="testing"
          class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
        >
          测试操作排行
        </button>
        <button 
          @click="testTenantActionRank" 
          :disabled="testing"
          class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
        >
          测试租户排行
        </button>
        <button 
          @click="testUserUsage" 
          :disabled="testing"
          class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
        >
          测试用户使用
        </button>
        <button 
          @click="testOperationLogs" 
          :disabled="testing"
          class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
        >
          测试操作日志
        </button>
        <button 
          @click="testAlarmSettings" 
          :disabled="testing"
          class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
        >
          测试告警设置
        </button>
        <button 
          @click="testAllApis" 
          :disabled="testing"
          class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 col-span-2 md:col-span-1"
        >
          测试所有API
        </button>
      </div>

      <!-- 测试结果显示 -->
      <div class="space-y-4">
        <div v-if="testing" class="text-blue-600 flex items-center">
          <i class="fa-solid fa-spinner fa-spin mr-2"></i>
          正在测试中...
        </div>
        
        <div v-for="(result, index) in testResults" :key="index" class="p-4 border rounded-lg">
          <div class="flex items-center justify-between mb-2">
            <h3 class="font-semibold">{{ result.name }}</h3>
            <span 
              class="px-2 py-1 rounded text-sm font-medium"
              :class="result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
            >
              {{ result.success ? '成功' : '失败' }}
            </span>
          </div>
          <div class="text-sm text-gray-600">
            <div>耗时: {{ result.duration }}ms</div>
            <div v-if="result.error" class="text-red-600 mt-1">错误: {{ result.error }}</div>
            <details v-if="result.data" class="mt-2">
              <summary class="cursor-pointer text-blue-600">查看数据</summary>
              <pre class="mt-2 p-2 bg-gray-100 rounded text-xs overflow-x-auto">{{ JSON.stringify(result.data, null, 2) }}</pre>
            </details>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { OperationService } from '../../services/operation.service'
import { apiConfig } from '../../config/api.config'

interface TestResult {
  name: string
  success: boolean
  duration: number
  error?: string
  data?: any
}

const testing = ref(false)
const testResults = ref<TestResult[]>([])
const mockMode = ref(apiConfig.useMock)

const toggleMockMode = () => {
  apiConfig.toggleMockMode()
  mockMode.value = apiConfig.useMock
  console.log('🔄 已切换到', mockMode.value ? 'Mock模式' : 'API模式')
}

const runTest = async (name: string, testFn: () => Promise<any>) => {
  const startTime = Date.now()
  try {
    const data = await testFn()
    const duration = Date.now() - startTime
    testResults.value.unshift({
      name,
      success: true,
      duration,
      data
    })
    console.log(`✅ ${name} 测试成功`, data)
  } catch (error: any) {
    const duration = Date.now() - startTime
    testResults.value.unshift({
      name,
      success: false,
      duration,
      error: error.message
    })
    console.error(`❌ ${name} 测试失败`, error)
  }
}

const testDailyStats = () => runTest('日度统计API', () => 
  OperationService.getDailyStats({ selectedTenant: '', viewMode: 'bar' })
)

const testMonthlyStats = () => runTest('月度统计API', () => 
  OperationService.getMonthlyStats({ comparisonType: 'yoy' })
)

const testAccessStats = () => runTest('访问统计API', () => 
  OperationService.getAccessStats('30d', 'line')
)

const testActionTypeRank = () => runTest('操作类型排行API', () => 
  OperationService.getActionTypeRank({ selectedOperationType: 'all' })
)

const testTenantActionRank = () => runTest('租户操作排行API', () => 
  OperationService.getTenantActionRank()
)

const testUserUsage = () => runTest('用户使用情况API', () => 
  OperationService.getUserUsage({ timeRange: '30d' })
)

const testOperationLogs = () => runTest('操作日志API', () => 
  OperationService.getOperationLogs({ page: 1, pageSize: 10 })
)

const testAlarmSettings = () => runTest('告警设置API', () => 
  OperationService.getAlarmSettings()
)

const testAllApis = async () => {
  testing.value = true
  testResults.value = []
  
  try {
    await testDailyStats()
    await testMonthlyStats()
    await testAccessStats()
    await testActionTypeRank()
    await testTenantActionRank()
    await testUserUsage()
    await testOperationLogs()
    await testAlarmSettings()
    
    console.log('🎉 所有API测试完成')
  } finally {
    testing.value = false
  }
}

onMounted(() => {
  console.log('🧪 API测试页面已加载')
  console.log('📡 当前API配置:', {
    baseURL: apiConfig.baseURL,
    useMock: apiConfig.useMock
  })
})
</script>