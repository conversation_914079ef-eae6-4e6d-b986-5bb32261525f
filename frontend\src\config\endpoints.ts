/**
 * API端点配置
 * 统一管理所有API路径，方便维护和修改
 */

export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    PROFILE: '/auth/profile',
    REFRESH: '/auth/refresh'
  },

  // 租户管理
  TENANT: {
    // 授权申请相关
    AUTH_APPLICATIONS: '/Tenant/auth-applications',
    AUTH_APPLICATION_DETAIL: (id: string) => `/Tenant/auth-applications/${id}`,
    APPROVE_APPLICATION: (id: string) => `/Tenant/auth-applications/${id}/approve`,

    // 服务状态
    SERVICE_STATUS: '/Tenant/service-status',

       // 授权销毁
    DESTROY_AUTH: (id: string) => `/Tenant/auth-applications/${id}/destroy`,

    // 授权销毁
    DESTROY_AUTH_DETAIL: `/Tenant/auth-applications/destroydetail`,

     // 授权进度查询
    AUTH_PROGRESS: `/Tenant/auth-progress`,
         // 授权进度查询
    AUTH_PROGRESS_DETAIL: (id: string) =>`/Tenant/auth-progress/${id}`,
  },

  // 运营统计
  OPERATION: {
    // 统计数据
    DAILY_STATS: '/operation/daily-stats',
    DAILY_STATS_SUMMARY: '/operation/daily-stats/summary',
    MONTHLY_STATS: '/operation/monthly-stats',
    ACCESS_STATS: '/operation/access-stats',
    ACTION_TYPE_RANK: '/operation/action-type-rank',
    TENANT_ACTION_RANK: '/operation/tenant-action-rank',
    USER_USAGE: '/operation/user-usage',
    SERVICE_STATUS_STATS: '/operation/service-status-stats',
    SERVICE_STATUS_CODE_STATS: '/operation/service-status-code-stats',
    SIX_MONTH_USAGE_STATS: '/operation/six-month-usage-stats',

    // 租户信息
    TENANTS: '/operation/tenants',

    // 操作日志
    LOGS: '/operation/logs',
    LOG_DETAIL: (id: string) => `/operation/logs/${id}`,

    // 告警设置
    ALARM_SETTINGS: '/operation/alarm-settings',
    ALARM_TEMPLATE: '/operation/alarm-settings/template',
    ALARM_EMAILS: '/operation/alarm-settings/emails',
    ALARM_EMAIL: (id: string) => `/operation/alarm-settings/emails/${id}`,
    ALARM_SMS_TEMPLATE: '/operation/alarm-settings/sms-template',
    ALARM_SMS_PHONES: '/operation/alarm-settings/sms-phones',
    ALARM_SMS_PHONE: (id: string) => `/operation/alarm-settings/sms-phones/${id}`
  },

  // 文件管理
  FILES: {
    UPLOAD: '/files/upload',
    DOWNLOAD: (fileId: string) => `/files/download/${fileId}`,
    PREVIEW: (fileId: string) => `/files/preview/${fileId}`,
    DELETE: (fileId: string) => `/files/${fileId}`
  },

  // 基础数据
  METADATA: {
    SERVICE_TYPES: '/metadata/service-types',
    PERMISSION_SCOPES: '/metadata/permission-scopes',
    AUTH_PERIODS: '/metadata/auth-periods',
    TENANT_SIZES: '/metadata/tenant-sizes',
    INDUSTRIES: '/metadata/industries'
  },

  // 系统监控
  SYSTEM: {
    HEALTH: '/system/health',
    VERSION: '/system/version',
    STATUS: '/system/status'
  },

  // 短信管理
  SEND_MSG: {
    PHONES: '/SendMsg/phones'
  },

  // 短信模板管理
  SMS_TEMPLATE: {
    GET_SMS_TEMPLATE: '/SendMsg/sms-template',
    SAVE_SMS_TEMPLATE: '/SendMsg/sms-template'
  }
};

/**
 * 构建完整的API URL
 * @param endpoint API端点
 * @param baseURL 基础URL（可选）
 */
export const buildApiUrl = (endpoint: string, baseURL?: string): string => {
  const base = baseURL || '';
  return endpoint.startsWith('/') ? `${base}${endpoint}` : `${base}/${endpoint}`;
};

/**
 * 检查端点是否需要认证
 * @param endpoint API端点
 */
export const requiresAuth = (endpoint: string): boolean => {
  const publicEndpoints = [
    API_ENDPOINTS.AUTH.LOGIN,
    API_ENDPOINTS.SYSTEM.HEALTH,
    API_ENDPOINTS.SYSTEM.VERSION,
    API_ENDPOINTS.SYSTEM.STATUS
  ];

  return !publicEndpoints.some(publicEndpoint => endpoint.includes(publicEndpoint));
};

/**
 * 获取端点描述（用于日志和调试）
 * @param endpoint API端点
 */
export const getEndpointDescription = (endpoint: string): string => {
  const descriptions: { [key: string]: string } = {
    [API_ENDPOINTS.AUTH.LOGIN]: '用户登录',
    [API_ENDPOINTS.AUTH.LOGOUT]: '用户登出',
    [API_ENDPOINTS.AUTH.PROFILE]: '获取用户信息',
    [API_ENDPOINTS.TENANT.AUTH_APPLICATIONS]: '租户授权申请',
    [API_ENDPOINTS.OPERATION.DAILY_STATS]: '日度统计数据',
    [API_ENDPOINTS.OPERATION.MONTHLY_STATS]: '月度统计数据',
    [API_ENDPOINTS.OPERATION.SIX_MONTH_USAGE_STATS]: '近6个月使用率统计',
    [API_ENDPOINTS.OPERATION.LOGS]: '操作日志',
    [API_ENDPOINTS.FILES.UPLOAD]: '文件上传'
  };

  // 查找精确匹配
  if (descriptions[endpoint]) {
    return descriptions[endpoint];
  }

  // 查找模糊匹配
  for (const [pattern, description] of Object.entries(descriptions)) {
    if (endpoint.includes(pattern.replace(/\/\{.*?\}/g, ''))) {
      return description;
    }
  }

  return '未知API';
};
