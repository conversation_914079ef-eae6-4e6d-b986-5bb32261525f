import { test, expect } from '@playwright/test';

test.describe('TypeScript编译检查', () => {
  test('应该没有TypeScript编译错误', async ({ page }) => {
    const compilationErrors: string[] = [];
    const moduleErrors: string[] = [];

    // 监听所有类型的错误
    page.on('console', msg => {
      const text = msg.text();
      if (msg.type() === 'error') {
        // TypeScript编译错误通常包含这些关键词
        if (text.includes('does not provide an export') ||
            text.includes('Cannot resolve module') ||
            text.includes('Module not found') ||
            text.includes('TS')) {
          compilationErrors.push(text);
        }
      }
    });

    // 监听页面错误（包括语法错误）
    page.on('pageerror', error => {
      const message = error.message;
      if (message.includes('SyntaxError') ||
          message.includes('does not provide an export') ||
          message.includes('Failed to resolve import')) {
        moduleErrors.push(message);
      }
    });

    // 监听请求失败（模块加载失败）
    page.on('requestfailed', request => {
      const url = request.url();
      const failure = request.failure();

      if ((url.includes('.ts') || url.includes('.js')) && failure) {
        moduleErrors.push(`Failed to load: ${url} - ${failure.errorText}`);
      }
    });

    // 访问页面
    await page.goto('http://localhost:3001');

    // 等待足够长的时间让所有模块加载
    await page.waitForTimeout(5000);

    // 尝试等待网络空闲，但如果有编译错误可能会失败
    try {
      await page.waitForLoadState('networkidle', { timeout: 10000 });
    } catch (error) {
      // 如果网络没有空闲，可能是因为有持续的错误
      console.warn('Network did not become idle, possible compilation issues');
    }

    // 检查页面是否正常渲染
    let pageRendered = false;
    try {
      await expect(page.locator('#app')).toBeVisible({ timeout: 5000 });
      pageRendered = true;
    } catch {
      // 页面没有正常渲染，可能有编译错误
    }

    // 收集所有错误
    const allErrors = [...compilationErrors, ...moduleErrors];

    if (allErrors.length > 0) {
      console.error('Found TypeScript/Module errors:', allErrors);

      // 如果有编译错误，测试应该失败
      throw new Error(`Found ${allErrors.length} compilation/module errors:\n${allErrors.join('\n')}`);
    }

    // 如果没有错误，页面应该正常渲染
    if (!pageRendered) {
      throw new Error('Page did not render properly, but no specific errors were caught');
    }

    console.log('✅ No TypeScript compilation errors found');
  });

  test('应该能正常导入和使用服务模块', async ({ page }) => {
    let serviceLoadErrors: string[] = [];

    // 监听特定的服务加载错误
    page.on('console', msg => {
      if (msg.type() === 'error') {
        const text = msg.text();
        if (text.includes('services/') ||
            text.includes('AuthService') ||
            text.includes('ApiResponse')) {
          serviceLoadErrors.push(text);
        }
      }
    });

    page.on('pageerror', error => {
      if (error.message.includes('services/') ||
          error.message.includes('AuthService') ||
          error.message.includes('ApiResponse')) {
        serviceLoadErrors.push(error.message);
      }
    });

    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');

    // 等待应用完全加载
    await page.waitForTimeout(3000);

    // 检查是否有服务相关的错误
    if (serviceLoadErrors.length > 0) {
      console.error('Service loading errors:', serviceLoadErrors);
      throw new Error(`Service loading failed: ${serviceLoadErrors.join(', ')}`);
    }

    // 尝试检查页面是否有正常功能（表明服务加载成功）
    await expect(page.locator('#app')).toBeVisible();

    console.log('✅ Services loaded successfully');
  });

  test('模块导入应该正常工作', async ({ page }) => {
    // 这个测试会尝试触发一些需要服务的功能
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');

    // 检查是否有按钮或链接（需要正常的Vue组件加载）
    const interactiveElements = page.locator('button, a');
    const elementCount = await interactiveElements.count();

    // 如果页面完全无法加载，连基本元素都没有
    expect(elementCount).toBeGreaterThan(0);

    // 检查Vue应用是否正常挂载
    const vueApp = page.locator('#app');
    await expect(vueApp).toBeVisible();

    // 检查是否有一些内容（证明组件正常渲染）
    const hasContent = await vueApp.locator('*').count();
    expect(hasContent).toBeGreaterThan(0);
  });
});
