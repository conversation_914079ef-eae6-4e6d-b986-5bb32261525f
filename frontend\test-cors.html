<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>前后端连接CORS测试</h1>
    
    <p>此页面用于测试前端（端口3002）与后端（端口5172）的CORS配置是否正确。</p>
    
    <div>
        <button onclick="testConnection()">测试API连接</button>
        <button onclick="testServiceTypes()">测试服务类型接口</button>
        <button onclick="clearResults()">清除结果</button>
    </div>

    <div id="results"></div>

    <script>
        const API_BASE_URL = 'http://localhost:5172/api';
        
        function showResult(message, isSuccess = true) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${isSuccess ? 'success' : 'error'}`;
            div.innerHTML = `
                <strong>${new Date().toLocaleTimeString()}</strong><br>
                ${message}
            `;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        async function testConnection() {
            showResult('开始测试API连接...', true);
            
            try {
                const response = await fetch(`${API_BASE_URL}/System/health`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult(`✅ API连接成功！<br>状态码: ${response.status}<br>响应: <pre>${JSON.stringify(data, null, 2)}</pre>`, true);
                } else {
                    showResult(`⚠️ API响应异常<br>状态码: ${response.status}`, false);
                }
            } catch (error) {
                showResult(`❌ API连接失败<br>错误: ${error.message}<br><br>可能的原因：<br>1. CORS配置不正确<br>2. 后端服务未启动<br>3. 网络连接问题`, false);
            }
        }

        async function testServiceTypes() {
            showResult('开始测试服务类型接口...', true);
            
            try {
                const response = await fetch(`${API_BASE_URL}/Metadata/service-types`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult(`✅ 服务类型接口调用成功！<br>状态码: ${response.status}<br>数据: <pre>${JSON.stringify(data, null, 2)}</pre>`, true);
                } else {
                    showResult(`⚠️ 服务类型接口响应异常<br>状态码: ${response.status}<br>响应: ${await response.text()}`, false);
                }
            } catch (error) {
                showResult(`❌ 服务类型接口调用失败<br>错误: ${error.message}<br><br>这是您遇到的CORS错误！`, false);
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // 页面加载时显示当前配置
        window.onload = function() {
            showResult(`📋 当前配置信息：<br>前端地址: ${window.location.origin}<br>后端地址: ${API_BASE_URL}<br>测试时间: ${new Date().toLocaleString()}`, true);
        };
    </script>
</body>
</html>