
using MapStudio.Api.Attributes;
using MapStudio.Api.Models.DTOs;
using MapStudio.Api.Models.Entities;
using MapStudio.Api.Models.ViewModels;
using MapStudio.Api.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace MapStudio.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class TenantController : ControllerBase
{
    private readonly ITenantService _tenantService;
    private readonly ILogger<TenantController> _logger;

    public TenantController(ITenantService tenantService, ILogger<TenantController> logger)
    {
        _tenantService = tenantService;
        _logger = logger;
    }

    /// <summary>
    /// 提交租户授权申请
    /// </summary>
    /// <param name="request">授权申请请求</param>
    [HttpPost("auth-applications")]
    public async Task<ActionResult<ApiResponse<AuthApplicationResponse>>> SubmitAuthApplication([FromBody] SubmitAuthApplicationRequest request)
    {
        try
        {
            var result = await _tenantService.SubmitAuthApplicationAsync(request);
            return Ok(ApiResponse<AuthApplicationResponse>.SuccessResult(result, "申请提交成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "提交授权申请失败");
            return StatusCode(500, ApiResponse<AuthApplicationResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取授权申请列表
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="searchTerm">搜索条件</param>
    /// <param name="serviceType">服务类型</param>
    /// <param name="authPeriod">授权期限</param>
    /// <param name="status">申请状态</param>
    [HttpGet("auth-applications")]
    [ConditionalAuthorize]
    public async Task<ActionResult<ApiResponse<AuthApplicationListResponse>>> GetAuthApplications(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? searchTerm = null,
        [FromQuery] string? serviceType = null,
        [FromQuery] string? authPeriod = null,
        [FromQuery] string? status = null)
    {
        try
        {
            var result = await _tenantService.GetAuthApplicationsAsync(page, pageSize, searchTerm, serviceType, authPeriod, status);
            return Ok(ApiResponse<AuthApplicationListResponse>.SuccessResult(result, "获取申请列表成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取授权申请列表失败");
            return StatusCode(500, ApiResponse<AuthApplicationListResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取授权申请详情
    /// </summary>
    /// <param name="id">申请ID</param>
    [HttpGet("auth-applications/{id}")]
    [ConditionalAuthorize]
    public async Task<ActionResult<ApiResponse<AuthApplicationDetailResponse>>> GetAuthApplicationDetail(string id)
    {
        try
        {
            var result = await _tenantService.GetAuthApplicationDetailAsync(id);
            return Ok(ApiResponse<AuthApplicationDetailResponse>.SuccessResult(result, "获取申请详情成功"));
        }
        catch (ArgumentException ex)
        {
            return NotFound(ApiResponse<AuthApplicationDetailResponse>.ErrorResult(ex.Message, "NOT_FOUND"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取授权申请详情失败: {Id}", id);
            return StatusCode(500, ApiResponse<AuthApplicationDetailResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }


    /// <summary>
    /// 获取租户服务状态
    /// </summary>
    /// <param name="tenantId">租户ID</param>
    [HttpGet("service-status")]
    [ConditionalAuthorize]
    public async Task<ActionResult<ApiResponse<TenantServiceStatusResponse>>> GetServiceStatus([FromQuery] string? tenantId = null)
    {
        try
        {
            var result = await _tenantService.GetServiceStatusAsync(tenantId);
            return Ok(ApiResponse<TenantServiceStatusResponse>.SuccessResult(result, "获取服务状态成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取服务状态失败");
            return StatusCode(500, ApiResponse<TenantServiceStatusResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取授权进度列表
    /// </summary>
    /// <param name="tenantId">租户ID</param>
    [HttpGet("auth-progress")]
    [ConditionalAuthorize]
    public async Task<ActionResult<ApiResponse<TenantServiceProgressResponse>>> GetAuthApplicationProgress([FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? searchTerm = null,
        [FromQuery] string? serviceType = null,
        [FromQuery] string? authPeriod = null,
        [FromQuery] string? status = null)
    {
        try
        {
            var result = await _tenantService.GetAuthApplicationProgressAsync(page,pageSize, searchTerm, serviceType, authPeriod, status);
            return Ok(ApiResponse<TenantServiceProgressResponse>.SuccessResult(result, "获取授权进度列表成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取授权进度列表失败");
            return StatusCode(500, ApiResponse<TenantServiceProgressResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取授权进度详情
    /// </summary>
    /// <param name="tenantId">租户ID</param>
    [HttpGet("auth-progress/{id}")]
    [ConditionalAuthorize]
    public async Task<ActionResult<ApiResponse<TenantServiceProgressDto>>> GetAuthApplicationProgress(string id)
    {
        try
        {
            var result = await _tenantService.GetAuthApplicationProgressDetailAsync(id);
            return Ok(ApiResponse<TenantServiceProgressDto>.SuccessResult(result, "获取授权进度详情成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取授权进度详情失败");
            return StatusCode(500, ApiResponse<TenantServiceProgressDto>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }
}