/**
 * 认证服务
 * 处理用户登录、登出、令牌管理等认证相关功能
 */

import { httpClient } from '../utils/http.client';
import { API_ENDPOINTS } from '../config/endpoints';
import type {
  LoginRequest,
  LoginResponse,
  UserInfo,
  ApiResponse
} from './types';

export class AuthService {
  private static readonly TOKEN_KEY = 'auth_token';
  private static readonly USER_KEY = 'user_info';
  private static readonly REDIRECT_KEY = 'redirect_after_login';

  /**
   * 用户登录
   * @param credentials 登录凭据
   */
  static async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      console.log('🔑 开始用户登录...');

      const response = await httpClient.post<LoginResponse>(
        API_ENDPOINTS.AUTH.LOGIN,
        credentials
      );

      if (response.success && response.token) {
        // 保存认证信息
        this.saveAuthInfo(response.token, response.user);

        console.log('✅ 登录成功:', response.user.name);
        return response;
      } else {
        throw new Error('登录失败');
      }
    } catch (error: any) {
      console.error('❌ 登录失败:', error.message);
      throw new Error(error.message || '登录失败，请检查用户名和密码');
    }
  }

  /**
   * 用户登出
   */
  static async logout(): Promise<void> {
    try {
      console.log('🚪 开始用户登出...');

      // 调用后端登出接口（如果token仍然有效）
      const token = this.getToken();
      if (token) {
        try {
          await httpClient.post(API_ENDPOINTS.AUTH.LOGOUT);
        } catch (error) {
          // 即使后端登出失败，也要清除本地信息
          console.warn('后端登出失败，但将清除本地认证信息:', error);
        }
      }

      // 清除本地认证信息
      this.clearAuthInfo();

      console.log('✅ 登出成功');
    } catch (error: any) {
      console.error('❌ 登出失败:', error.message);
      // 即使出错也要清除本地信息
      this.clearAuthInfo();
    }
  }

  /**
   * 获取当前用户信息
   */
  static async getCurrentUser(): Promise<UserInfo> {
    try {
      console.log('👤 获取当前用户信息...');

      const response = await httpClient.get<UserInfo>(API_ENDPOINTS.AUTH.PROFILE);

      // 更新本地存储的用户信息
      this.saveUserInfo(response);

      console.log('✅ 获取用户信息成功:', response.name);
      return response;
    } catch (error: any) {
      console.error('❌ 获取用户信息失败:', error.message);

      // 如果是401错误，可能token已过期
      if (error.code === '401') {
        this.clearAuthInfo();
        throw new Error('认证已过期，请重新登录');
      }

      throw new Error(error.message || '获取用户信息失败');
    }
  }

  /**
   * 刷新令牌
   */
  static async refreshToken(): Promise<string> {
    try {
      console.log('🔄 刷新认证令牌...');

      const response = await httpClient.post<{ token: string }>(
        API_ENDPOINTS.AUTH.REFRESH
      );

      if (response.token) {
        this.saveToken(response.token);
        console.log('✅ 令牌刷新成功');
        return response.token;
      } else {
        throw new Error('刷新令牌失败');
      }
    } catch (error: any) {
      console.error('❌ 刷新令牌失败:', error.message);
      this.clearAuthInfo();
      throw new Error('令牌刷新失败，请重新登录');
    }
  }

  /**
   * 检查是否已登录
   */
  static isAuthenticated(): boolean {
    const token = this.getToken();
    const user = this.getUserInfo();
    return !!(token && user);
  }

  /**
   * 获取认证令牌
   */
  static getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  /**
   * 获取用户信息
   */
  static getUserInfo(): UserInfo | null {
    const userStr = localStorage.getItem(this.USER_KEY);
    if (userStr) {
      try {
        return JSON.parse(userStr);
      } catch (error) {
        console.error('解析用户信息失败:', error);
        this.clearAuthInfo();
        return null;
      }
    }
    return null;
  }

  /**
   * 保存认证信息
   */
  private static saveAuthInfo(token: string, user: UserInfo): void {
    this.saveToken(token);
    this.saveUserInfo(user);
  }

  /**
   * 保存认证令牌
   */
  private static saveToken(token: string): void {
    localStorage.setItem(this.TOKEN_KEY, token);
  }

  /**
   * 保存用户信息
   */
  private static saveUserInfo(user: UserInfo): void {
    localStorage.setItem(this.USER_KEY, JSON.stringify(user));
  }

  /**
   * 清除认证信息
   */
  private static clearAuthInfo(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
  }

  /**
   * 保存登录后重定向路径
   */
  static saveRedirectPath(path: string): void {
    localStorage.setItem(this.REDIRECT_KEY, path);
  }

  /**
   * 获取并清除重定向路径
   */
  static getAndClearRedirectPath(): string | null {
    const path = localStorage.getItem(this.REDIRECT_KEY);
    if (path) {
      localStorage.removeItem(this.REDIRECT_KEY);
    }
    return path;
  }

  /**
   * 检查用户权限
   */
  static hasPermission(permission: string): boolean {
    const user = this.getUserInfo();
    if (!user || !user.permissions) {
      return false;
    }

    return user.permissions.includes(permission) || user.permissions.includes('admin');
  }

  /**
   * 检查用户角色
   */
  static hasRole(role: string): boolean {
    const user = this.getUserInfo();
    return user?.role === role;
  }

  /**
   * 是否为管理员
   */
  static isAdmin(): boolean {
    return this.hasRole('admin');
  }

  /**
   * 自动登录检查（页面加载时调用）
   */
  static async autoLoginCheck(): Promise<boolean> {
    try {
      if (!this.isAuthenticated()) {
        return false;
      }

      // 验证token是否仍然有效
      await this.getCurrentUser();
      return true;
    } catch (error) {
      console.warn('自动登录检查失败，清除认证信息:', error);
      this.clearAuthInfo();
      return false;
    }
  }

  /**
   * 监听认证状态变化
   */
  static onAuthStateChange(callback: (isAuthenticated: boolean) => void): (() => void) {
    // 监听localStorage变化
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === this.TOKEN_KEY || event.key === this.USER_KEY) {
        callback(this.isAuthenticated());
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // 返回清理函数
    return () => window.removeEventListener('storage', handleStorageChange);
  }
}
