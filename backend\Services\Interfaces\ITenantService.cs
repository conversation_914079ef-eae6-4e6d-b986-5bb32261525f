using MapStudio.Api.Models.DTOs;
using MapStudio.Api.Models.Entities;

namespace MapStudio.Api.Services.Interfaces;

public interface ITenantService
{
    Task<AuthApplicationResponse> SubmitAuthApplicationAsync(SubmitAuthApplicationRequest request);
    Task<AuthApplicationListResponse> GetAuthApplicationsAsync(int page, int pageSize, string? searchTerm, string? serviceType, string? authPeriod, string? status = null);
    Task<AuthApplicationDetailResponse> GetAuthApplicationDetailAsync(string id);
    Task<ApprovalResponse> ApproveAuthApplicationAsync(string id, ApprovalRequest request);
    Task DeleteAuthApplicationAsync(string id);
    Task<TenantServiceStatusResponse> GetServiceStatusAsync(string? tenantId);

    Task<TenantServiceProgressResponse> GetAuthApplicationProgressAsync(int page, int pageSize, string? searchTerm, string? serviceType, string? authPeriod, string? status = null);
    Task<TenantServiceProgressDto> GetAuthApplicationProgressDetailAsync(string id);

    Task<AuthDestoryListResponse> GetAuthDestoryAsync(int page, int pageSize, string? searchTerm, string? serviceType, string? authPeriod, string? status = null);

    Task<AuthApplication> IsValidateAsync(TenantValidateDto dto);
}