using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Security.Claims;

namespace MapStudio.Api.Attributes;

/// <summary>
/// 条件认证属性
/// 根据配置决定是否启用JWT认证
/// </summary>
public class ConditionalAuthorizeAttribute : Attribute, IAuthorizationFilter
{
    public void OnAuthorization(AuthorizationFilterContext context)
    {
        // 从服务容器获取配置
        var configuration = context.HttpContext.RequestServices.GetRequiredService<IConfiguration>();
        var jwtEnabled = configuration.GetValue<bool>("JwtSettings:Enabled", false);

        // 如果JWT未启用，创建默认用户身份并直接通过认证
        if (!jwtEnabled)
        {
            // 获取数据库中的管理员用户ID
            var dbContext = context.HttpContext.RequestServices.GetService<SqlSugar.ISqlSugarClient>();
            if (dbContext != null)
            {
                try
                {
                    var adminUser = dbContext.Queryable<MapStudio.Api.Models.Entities.User>()
                        .Where(u => u.Role == "admin")
                        .First();
                    
                    if (adminUser != null)
                    {
                        // 使用数据库中实际的管理员用户信息
                        var defaultClaims = new[]
                        {
                            new Claim(ClaimTypes.NameIdentifier, adminUser.Id),
                            new Claim(ClaimTypes.Name, adminUser.Name),
                            new Claim(ClaimTypes.Email, adminUser.Email),
                            new Claim(ClaimTypes.Role, adminUser.Role),
                            new Claim("UserId", adminUser.Id)
                        };
                        
                        var identity = new ClaimsIdentity(defaultClaims, "Development");
                        var principal = new ClaimsPrincipal(identity);
                        context.HttpContext.User = principal;
                        
                        return;
                    }
                }
                catch
                {
                    // 如果数据库查询失败，使用默认用户
                }
            }
            
            // Fallback: 创建默认用户身份（仅用于开发环境）
            var fallbackClaims = new[]
            {
                new Claim(ClaimTypes.NameIdentifier, "dev-user-001"),
                new Claim(ClaimTypes.Name, "开发环境用户"),
                new Claim(ClaimTypes.Email, "<EMAIL>"),
                new Claim(ClaimTypes.Role, "admin"),
                new Claim("UserId", "dev-user-001")
            };
            
            var fallbackIdentity = new ClaimsIdentity(fallbackClaims, "Development");
            var fallbackPrincipal = new ClaimsPrincipal(fallbackIdentity);
            context.HttpContext.User = fallbackPrincipal;
            
            return;
        }

        // 如果JWT启用，执行标准认证检查
        if (!context.HttpContext.User.Identity?.IsAuthenticated ?? true)
        {
            context.Result = new UnauthorizedResult();
            return;
        }
    }
}