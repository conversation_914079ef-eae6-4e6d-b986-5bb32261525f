using MapStudio.Api.Models.DTOs;
using MapStudio.Api.Models.Entities;

namespace MapStudio.Api.Services.Interfaces;

public interface IAuthService
{
    Task<LoginResponse> LoginAsync(LoginRequest request);
    Task<User?> GetUserByIdAsync(string userId);
    Task<bool> ValidateTokenAsync(string token);
    string GenerateJwtToken(User user);
    Task LogoutAsync();
    Task<UserProfileResponse> GetUserProfileAsync(string userId);
}