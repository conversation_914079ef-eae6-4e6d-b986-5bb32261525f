# Vue & React 样式统一指南

本文档详细说明了Vue和React版本之间样式统一的实现方案，包括设计系统、组件规范和最佳实践。

## 目录

1. [设计系统](#设计系统)
2. [样式架构](#样式架构)
3. [组件规范](#组件规范)
4. [跨框架样式共享](#跨框架样式共享)
5. [最佳实践](#最佳实践)
6. [迁移指南](#迁移指南)

## 设计系统

### 颜色系统

我们采用统一的颜色调色板，确保两个框架使用相同的颜色值：

```css
/* 主色调 */
--color-primary: #3b82f6;
--color-primary-hover: #2563eb;
--color-primary-light: #dbeafe;

/* 语义化颜色 */
--color-success: #10b981;
--color-warning: #f59e0b;
--color-error: #ef4444;
--color-info: #06b6d4;
```

### 字体系统

统一的字体栈和字号规范：

```css
font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;

/* 字号规范 */
--text-xs: 0.75rem;
--text-sm: 0.875rem;
--text-base: 1rem;
--text-lg: 1.125rem;
--text-xl: 1.25rem;
```

### 间距系统

基于4px网格的间距系统：

```css
--spacing-xs: 0.25rem;  /* 4px */
--spacing-sm: 0.5rem;   /* 8px */
--spacing-md: 1rem;     /* 16px */
--spacing-lg: 1.5rem;   /* 24px */
--spacing-xl: 2rem;     /* 32px */
```

## 样式架构

### 文件结构

```
src/styles/
├── variables.css      # CSS变量定义
├── global.css         # 全局样式和工具类
└── shared-config.ts   # 跨框架配置

src/utils/
└── styleUtils.ts      # 样式工具函数

src/components/
├── BaseButton.vue     # 基础按钮组件
├── BaseInput.vue      # 基础输入框组件
└── ...               # 其他基础组件
```

### Tailwind CSS 配置统一

两个项目使用相同的Tailwind配置：

```javascript
// tailwind.config.js/mjs
export default {
  darkMode: "class",
  content: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
  theme: {
    container: { center: true },
    extend: {
      colors: { /* 统一颜色配置 */ },
      fontFamily: { /* 统一字体配置 */ },
      spacing: { /* 统一间距配置 */ }
    }
  },
  plugins: []
}
```

## 组件规范

### 按钮组件

统一的按钮样式规范：

#### React 版本
```tsx
// React 实现
import { cn } from '@/lib/utils';

interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  // ...其他props
}

export function Button({ variant = 'primary', size = 'md', ...props }: ButtonProps) {
  return (
    <button
      className={cn(
        'inline-flex items-center justify-center font-medium rounded-md transition-colors',
        {
          'bg-blue-600 text-white hover:bg-blue-700': variant === 'primary',
          'bg-gray-200 text-gray-900 hover:bg-gray-300': variant === 'secondary',
          // ...其他变体
        },
        {
          'px-3 py-1.5 text-sm': size === 'sm',
          'px-4 py-2 text-sm': size === 'md',
          'px-6 py-3 text-base': size === 'lg',
        }
      )}
      {...props}
    />
  );
}
```

#### Vue 版本
```vue
<!-- Vue 实现 -->
<template>
  <button :class="buttonClasses" v-bind="$attrs">
    <slot></slot>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { cn, createComponentStyle } from '@/utils/styleUtils';
import { componentPresets } from '@/styles/shared-config';

interface Props {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md'
});

const buttonClasses = computed(() => {
  return cn(
    componentPresets.button.base,
    componentPresets.button.variants[props.variant],
    componentPresets.button.sizes[props.size]
  );
});
</script>
```

### 输入框组件

统一的输入框样式规范：

#### 共同特性
- 统一的边框样式和圆角
- 一致的焦点状态和错误状态
- 相同的内边距和字体大小
- 统一的图标位置和样式

#### 状态管理
- 正常状态：`border-gray-300 focus:ring-blue-500`
- 错误状态：`border-red-300 focus:ring-red-500`
- 禁用状态：`bg-gray-100 cursor-not-allowed`

## 跨框架样式共享

### 1. CSS 变量方案

**优点：**
- 真正的样式共享，一处修改全局生效
- 支持动态主题切换
- 浏览器原生支持，性能好

**缺点：**
- 不支持复杂的样式逻辑
- 调试相对困难

**实现：**
```css
/* variables.css */
:root {
  --color-primary: #3b82f6;
  --spacing-md: 1rem;
}

/* 在组件中使用 */
.button {
  background-color: var(--color-primary);
  padding: var(--spacing-md);
}
```

### 2. 配置文件方案

**优点：**
- 类型安全（TypeScript）
- 支持复杂的样式逻辑
- 易于维护和扩展

**缺点：**
- 需要构建时处理
- 两个项目需要同步更新

**实现：**
```typescript
// shared-config.ts
export const designSystem = {
  colors: {
    primary: '#3b82f6',
    // ...
  },
  spacing: {
    md: '1rem',
    // ...
  }
};
```

### 3. Tailwind CSS 方案

**优点：**
- 原子化CSS，高度一致
- 优秀的开发体验
- 自动优化和压缩

**缺点：**
- 学习成本
- 需要统一配置文件

**实现：**
```javascript
// 统一的 tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: '#3b82f6'
      }
    }
  }
}
```

## 最佳实践

### 1. 组件设计原则

- **一致性优先**：确保相同功能的组件在两个框架中表现完全一致
- **可复用性**：设计通用的组件API，减少重复代码
- **可维护性**：使用统一的命名规范和文档

### 2. 样式编写规范

```css
/* ✅ 推荐：使用语义化的类名 */
.btn-primary { /* ... */ }
.form-input { /* ... */ }

/* ❌ 避免：使用过于具体的类名 */
.blue-button { /* ... */ }
.login-form-email-input { /* ... */ }
```

### 3. 响应式设计

使用统一的断点系统：

```css
/* 移动优先的响应式设计 */
.container {
  padding: 1rem;
}

@media (min-width: 640px) {
  .container {
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 2rem;
  }
}
```

### 4. 主题支持

实现统一的主题切换机制：

```css
/* 浅色主题 */
.theme-light {
  --bg-primary: #ffffff;
  --text-primary: #111827;
}

/* 深色主题 */
.theme-dark {
  --bg-primary: #1f2937;
  --text-primary: #f9fafb;
}
```

### 5. 动画和过渡

使用统一的动画时长和缓动函数：

```css
.transition-base {
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
```

## 迁移指南

### 从现有项目迁移

1. **安装依赖**
   ```bash
   npm install tailwindcss @tailwindcss/forms
   ```

2. **配置 Tailwind**
   ```javascript
   // 复制统一的 tailwind.config.js
   ```

3. **导入全局样式**
   ```css
   @import './styles/variables.css';
   @import './styles/global.css';
   ```

4. **替换组件**
   ```vue
   <!-- 旧的实现 -->
   <button class="old-button-class">按钮</button>
   
   <!-- 新的实现 -->
   <BaseButton variant="primary" size="md">按钮</BaseButton>
   ```

### 样式冲突解决

1. **CSS 特异性冲突**
   ```css
   /* 使用更具体的选择器 */
   .app .btn-primary {
     /* 覆盖样式 */
   }
   ```

2. **框架特定样式**
   ```css
   /* Vue scoped 样式 */
   <style scoped>
   .component-specific {
     /* Vue 特定样式 */
   }
   </style>
   
   /* React CSS Modules */
   .componentSpecific {
     /* React 特定样式 */
   }
   ```

## 工具和资源

### 开发工具

- **VS Code 扩展**
  - Tailwind CSS IntelliSense
  - PostCSS Language Support

- **浏览器工具**
  - Chrome DevTools
  - Vue DevTools / React DevTools

### 设计资源

- **设计系统文档**：详细的组件规范和使用指南
- **Figma 组件库**：设计师和开发者共享的组件库
- **Storybook**：组件展示和测试平台

## 总结

通过统一的设计系统、样式架构和组件规范，我们成功实现了Vue和React版本之间的样式一致性。这种方案不仅提高了开发效率，还确保了用户体验的一致性。

关键成功因素：
1. **统一的设计语言**：颜色、字体、间距等基础元素的标准化
2. **共享的配置文件**：通过配置文件实现样式规范的共享
3. **组件化思维**：将样式封装在可复用的组件中
4. **工具链支持**：使用Tailwind CSS等工具提高开发效率
5. **文档和规范**：详细的文档确保团队成员遵循统一标准

这套方案为后续的维护和扩展奠定了坚实的基础，同时也为其他类似项目提供了可参考的实践经验。