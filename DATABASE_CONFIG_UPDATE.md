# 数据库配置更新摘要

## 📋 更新概述

已将Map Studio后端项目的数据库配置更新为与MySQL MCP服务器保持一致。

## 🔧 配置变更详情

### MCP MySQL服务器信息
- **主机地址**: 192.168.2.119
- **端口**: 3307
- **数据库名**: MTNOH_AAA_Platform
- **用户名**: dtauser
- **密码**: dtauser

### 更新的配置文件

#### 1. appsettings.json
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=192.168.2.119;Port=3307;Database=MTNOH_AAA_Platform;Uid=dtauser;Pwd=dtauser;charset=utf8mb4;"
  }
}
```

#### 2. appsettings.Testing.json
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=192.168.2.119;Port=3307;Database=MTNOH_AAA_Platform;Uid=dtauser;Pwd=dtauser;charset=utf8mb4;",
    "TestConnection": "Server=192.168.2.119;Port=3307;Database=MTNOH_AAA_Platform_test;Uid=dtauser;Pwd=dtauser;charset=utf8mb4;"
  }
}
```

#### 3. docker-compose.yml
- 更新了后端服务的数据库连接环境变量
- 更新了测试后端服务的数据库连接环境变量
- 移除了对本地MySQL容器的依赖（已注释）

#### 4. 启动脚本更新
- **start-test-server.sh**: 更新了数据库连接参数
- **start-test-server.bat**: 更新了MySQL连接检查命令

## 🔍 验证工具

创建了两个测试脚本用于验证数据库连接：

### Windows PowerShell脚本
```powershell
# 运行数据库连接测试
.\test-db-connection.ps1
```

### Linux/macOS Bash脚本
```bash
# 给脚本执行权限
chmod +x test-db-connection.sh

# 运行数据库连接测试
./test-db-connection.sh
```

## ⚠️ 重要注意事项

### 1. 数据库权限要求
确保用户 `dtauser` 在MCP MySQL服务器上具有以下权限：
- `MTNOH_AAA_Platform` 数据库的完整访问权限
- 创建和管理表的权限
- 数据读写权限

### 2. 网络连接要求
- 确保后端服务器能够访问 `192.168.2.119:3307`
- 检查防火墙设置，确保端口3307开放
- 验证网络路由配置

### 3. 测试环境注意事项
- 测试环境使用数据库名 `MTNOH_AAA_Platform_test`
- 需要在MCP MySQL服务器上创建此测试数据库
- 或者修改测试配置使用相同的主数据库

## 🚀 部署建议

### 1. 开发环境
```bash
# 直接启动后端服务
cd backend
dotnet run
```

### 2. Docker环境
```bash
# 使用Docker Compose启动（会使用外部数据库）
docker-compose up backend
```

### 3. 测试环境
```bash
# 启动测试环境
cd backend
ASPNETCORE_ENVIRONMENT=Testing dotnet run
```

## 🔧 故障排除

### 常见问题及解决方案

1. **连接超时**
   - 检查网络连接: `telnet 192.168.2.119 3307`
   - 验证防火墙设置

2. **认证失败**
   - 验证用户名密码是否正确
   - 检查MySQL用户权限设置

3. **数据库不存在**
   - 在MCP MySQL服务器上创建数据库
   - 或联系数据库管理员确认数据库名称

4. **表结构问题**
   - 应用会自动创建所需的表结构
   - 如有问题，检查DatabaseInitializer日志

## 📝 后续步骤

1. **运行连接测试**
   ```bash
   # Windows
   .\test-db-connection.ps1
   
   # Linux/macOS
   ./test-db-connection.sh
   ```

2. **启动后端服务**
   ```bash
   cd backend
   dotnet run
   ```

3. **验证应用功能**
   - 访问 `http://localhost:5000/swagger` 查看API文档
   - 测试关键API端点
   - 检查数据库表是否正确创建

4. **前端配置**
   - 确保前端API配置指向正确的后端地址
   - 测试前后端数据交互

## ✅ 配置完成

数据库配置已成功更新为使用MCP MySQL服务器。现在可以启动应用并进行测试。