# 移动硬盘Git配置使用指南

## 概述
本指南帮助您在不同电脑上使用存储在移动硬盘中的map-studio项目，确保Git提交时使用正确的用户信息：glau <<EMAIL>>

## 核心特性
- ✅ 智能检测：自动检测电脑现有Git配置，避免冲突
- ✅ 非侵入性：不覆盖他人电脑的Git全局设置
- ✅ 跨平台兼容：支持Windows、Linux、macOS
- ✅ 移除无痕：移除硬盘后不影响电脑原有配置

## 在新电脑上的使用步骤

### 1. 插入移动硬盘
将移动硬盘插入目标电脑，确保能正常访问。

### 2. 打开命令行
- **Windows**: 按 `Win + R`，输入 `cmd` 或 `powershell`
- **macOS**: 按 `Cmd + Space`，输入 `terminal`
- **Linux**: 按 `Ctrl + Alt + T`

### 3. 进入项目目录
```bash
# Windows 示例
cd F:\map-studio

# macOS 示例
cd /Volumes/YourDisk/map-studio

# Linux 示例
cd /media/username/YourDisk/map-studio
```

### 4. 运行配置脚本

#### Windows
```batch
setup-git-portable.bat
```

#### Linux/macOS
```bash
# 赋予执行权限（首次使用时）
chmod +x setup-git-portable.sh

# 运行配置脚本
./setup-git-portable.sh
```

### 5. 验证配置
```bash
git config user.name     # 应显示: glau
git config user.email    # 应显示: <EMAIL>
git status              # 检查仓库状态
```

## 日常使用

配置完成后，您就可以正常使用Git命令：

```bash
# 查看状态
git status

# 添加文件
git add .
git add 文件名

# 提交更改
git commit -m "您的提交信息"

# 查看提交历史
git log --oneline

# 推送到远程（如果有配置）
git push
```

## 配置原理

### 配置层级（优先级从高到低）
1. **项目级配置** (.git/config) - 主要使用
2. **全局配置** (~/.gitconfig) - 备用配置
3. **系统级配置** - 通常不修改

### 智能配置策略
- 如果电脑**没有**全局Git用户配置 → 设置临时全局配置
- 如果电脑**已有**其他用户的全局配置 → 仅设置项目级配置
- 项目级配置始终设置为：glau <<EMAIL>>

## 常见问题解决

### 问题1：提示需要设置用户信息
**解决**：重新运行配置脚本
```bash
# Windows
setup-git-portable.bat

# Linux/macOS
./setup-git-portable.sh
```

### 问题2：权限不足
**Windows**：以管理员身份运行命令提示符
**Linux/macOS**：使用 `sudo` 或检查文件权限

### 问题3：脚本无法执行
**Linux/macOS**：确保脚本有执行权限
```bash
chmod +x setup-git-portable.sh
```

### 问题4：配置验证失败
手动检查配置：
```bash
# 查看所有配置
git config --list | grep user

# 查看实际生效的配置
git config user.name
git config user.email
```

## 安全提醒

1. **配置脚本不会删除或覆盖电脑原有的Git设置**
2. **移除硬盘前请确保所有更改已提交**
3. **建议定期将代码推送到远程仓库作为备份**
4. **如果在公用电脑使用，使用完毕后可运行以下命令清理（可选）**：
   ```bash
   git config --global --unset user.name
   git config --global --unset user.email
   ```

## 技术支持

如果遇到问题，请检查：
1. Git是否已正确安装：`git --version`
2. 移动硬盘是否正常挂载
3. 是否有足够的权限访问配置文件
4. 网络连接是否正常（如需推送到远程仓库）

---
最后更新：2025-08-25
用户：glau <<EMAIL>>