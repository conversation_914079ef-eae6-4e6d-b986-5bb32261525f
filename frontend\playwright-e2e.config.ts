/**
 * E2E测试配置
 * 配置Playwright端到端测试环境
 */

import { defineConfig, devices } from '@playwright/test';

/**
 * Playwright配置
 * 支持多浏览器、并行测试、报告生成等功能
 */
export default defineConfig({
  testDir: './src/tests/e2e',
  
  /* 并行运行测试 */
  fullyParallel: true,
  
  /* 在CI环境中失败时重试 */
  retries: process.env.CI ? 2 : 0,
  
  /* CI环境中选择工作进程数量 */
  workers: process.env.CI ? 1 : undefined,
  
  /* 测试报告配置 */
  reporter: [
    ['html', { outputFolder: 'test-results/e2e-report' }],
    ['json', { outputFile: 'test-results/e2e-results.json' }],
    ['junit', { outputFile: 'test-results/e2e-results.xml' }]
  ],
  
  /* 共享测试配置 */
  use: {
    /* 基础URL */
    baseURL: 'http://localhost:3001',
    
    /* 收集失败时的trace */
    trace: 'on-first-retry',
    
    /* 截图配置 */
    screenshot: 'only-on-failure',
    
    /* 视频录制 */
    video: 'retain-on-failure',
    
    /* 浏览器上下文配置 */
    contextOptions: {
      // 忽略HTTPS错误
      ignoreHTTPSErrors: true,
    },
    
    /* 默认超时时间 */
    actionTimeout: 30000,
    navigationTimeout: 30000,
  },
  
  /* 测试项目配置 */
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    
    /* 移动端测试 */
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
    
    /* Microsoft Edge */
    {
      name: 'Microsoft Edge',
      use: { ...devices['Desktop Edge'], channel: 'msedge' },
    },
  ],
  
  /* 本地开发服务器配置 */
  webServer: [
    {
      command: 'npm run dev',
      port: 3001,
      reuseExistingServer: !process.env.CI,
      env: {
        NODE_ENV: 'test'
      }
    },
    {
      command: 'cd ../backend && dotnet run --environment Testing',
      port: 5000,
      reuseExistingServer: !process.env.CI,
      timeout: 120 * 1000 // 2分钟启动超时
    }
  ],
  
  /* 全局设置 */
  globalSetup: require.resolve('./src/tests/e2e/global-setup.ts'),
  globalTeardown: require.resolve('./src/tests/e2e/global-teardown.ts'),
  
  /* 超时配置 */
  timeout: 60 * 1000, // 每个测试60秒超时
  expect: {
    timeout: 10 * 1000 // 断言10秒超时
  },
  
  /* 输出目录 */
  outputDir: 'test-results/e2e-artifacts',
});