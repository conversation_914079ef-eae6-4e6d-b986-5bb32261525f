<template>
  <div class="p-6 space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">单租户操作行为热点排行</h1>
      <p class="mt-1 text-gray-500">查看指定租户在选定时间范围内的操作行为频次排行</p>
    </div>
    
    <!-- 查询条件卡片 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="space-y-2">
          <label for="tenantSelect" class="block text-sm font-medium text-gray-700">
            选择租户 <span class="text-red-500">*</span>
          </label>
          <select
            id="tenantSelect"
            v-model="selectedTenant"
            @change="handleTenantChange"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option v-for="tenant in tenantOptions" :key="tenant.id" :value="tenant.id">
              {{ tenant.name }}
            </option>
          </select>
        </div>
        
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            时间范围 <span class="text-red-500">*</span>
          </label>
          <div class="flex space-x-3">
            <button
              v-for="range in timeRangeOptions"
              :key="range.value"
              @click="handleTimeRangeChange(range.value)"
              :class="[
                'flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors',
                timeRange === range.value
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
              ]"
            >
              {{ range.label }}
            </button>
          </div>
        </div>
        
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            时间粒度 <span class="text-red-500">*</span>
          </label>
          <div class="flex space-x-3">
            <button
              v-for="granularity in timeGranularityOptions"
              :key="granularity.value"
              @click="handleTimeGranularityChange(granularity.value)"
              :disabled="
                (timeRange === '7d' && granularity.value === 'month') ||
                (timeRange === '30d' && granularity.value === 'month')
              "
              :class="[
                'flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors',
                timeGranularity === granularity.value
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed'
              ]"
            >
              {{ granularity.label }}
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 数据概览卡片 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-gray-900">
          {{ selectedTenantName }} - 操作行为统计
        </h2>
        <button class="text-blue-600 hover:text-blue-900 text-sm">
          <i class="fa-solid fa-download mr-1"></i> 导出数据
        </button>
      </div>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- 操作行为频次排行 -->
        <div>
          <h3 class="text-base font-medium text-gray-900 mb-4">操作行为频次排行</h3>
          <div class="h-80">
            <ChartBase
              :option="barChartOption"
              :loading="chartLoading"
              class="h-80"
            />
          </div>
        </div>
        
        <!-- 操作行为趋势 -->
        <div>
          <h3 class="text-base font-medium text-gray-900 mb-4">操作行为趋势</h3>
          <div class="h-80">
            <ChartBase
              :option="trendChartOption"
              :loading="chartLoading"
              class="h-80"
            />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 详细数据表格 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作类型
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作次数
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                占比
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                环比增长
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                最近操作时间
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr 
              v-for="(action, index) in actionData" 
              :key="action.name" 
              class="hover:bg-gray-50 transition-colors"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div :class="[
                    'w-3 h-3 rounded-full mr-3',
                    getActionColor(index)
                  ]"></div>
                  <div class="text-sm font-medium text-gray-900">{{ action.name }}</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ action.value }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      class="bg-blue-600 h-2 rounded-full" 
                      :style="{ width: `${action.percentage}%` }"
                    ></div>
                  </div>
                  <span class="ml-2 text-sm text-gray-900">{{ action.percentage }}%</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="[
                  'text-sm',
                  action.isGrowthPositive ? 'text-green-600' : 'text-red-600'
                ]">
                  {{ action.isGrowthPositive ? '+' : '' }}{{ action.growthRate }}%
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ action.recentTime }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import ChartBase from '@/components/ChartBase.vue'

// 租户选项数据 - 与React版本完全一致
const tenantOptions = [
  { id: 'TENANT-2025001', name: '智慧城市科技有限公司' },
  { id: 'TENANT-2025002', name: '未来交通研究院' },
  { id: 'TENANT-2025003', name: '绿色能源集团' },
  { id: 'TENANT-2025004', name: '数字农业科技有限公司' },
  { id: 'TENANT-2025005', name: '智慧医疗系统集成商' },
]

// 时间范围选项
const timeRangeOptions = [
  { value: '7d', label: '近7天' },
  { value: '30d', label: '近30天' },
  { value: '90d', label: '近90天' },
]

// 时间粒度选项
const timeGranularityOptions = [
  { value: 'day', label: '按日' },
  { value: 'week', label: '按周' },
  { value: 'month', label: '按月' },
]

// 响应式状态
const selectedTenant = ref(tenantOptions[0].id)
const timeRange = ref('7d')
const timeGranularity = ref('day')
const chartLoading = ref(false)

// 生成操作行为数据的函数 - 与React版本一致
const generateActionData = (tenantId: string) => {
  const actionTypes = [
    { name: '地图查询', value: 0 },
    { name: '数据上传', value: 0 },
    { name: '数据下载', value: 0 },
    { name: '图层编辑', value: 0 },
    { name: '权限管理', value: 0 },
  ]
  
  // 为每种操作类型生成随机数据
  const rawData = actionTypes.map(type => ({
    ...type,
    value: Math.floor(Math.random() * 1000) + 100, // 生成100-1000的随机数
  }))
  
  // 计算总数和百分比
  const total = rawData.reduce((sum, item) => sum + item.value, 0)
  
  return rawData.map((item, index) => {
    const percentage = ((item.value / total) * 100).toFixed(2)
    const growthRate = (Math.random() * 20 - 5).toFixed(2) // -5% 到 15% 之间的随机数
    const isGrowthPositive = parseFloat(growthRate) >= 0
    
    // 生成随机最近操作时间
    const recentDate = new Date()
    recentDate.setHours(recentDate.getHours() - Math.floor(Math.random() * 24))
    recentDate.setMinutes(recentDate.getMinutes() - Math.floor(Math.random() * 60))
    
    return {
      ...item,
      percentage: parseFloat(percentage),
      growthRate,
      isGrowthPositive,
      recentTime: recentDate.toLocaleString('zh-CN'),
    }
  })
}

// 生成时间趋势数据的函数 - 与React版本一致
const generateTrendData = (days = 7) => {
  return Array.from({ length: days }, (_, i) => {
    const date = new Date()
    date.setDate(date.getDate() - (days - i - 1))
    
    return {
      date: date.toLocaleDateString('zh-CN'),
      '地图查询': Math.floor(Math.random() * 200) + 50,
      '数据上传': Math.floor(Math.random() * 150) + 30,
      '数据下载': Math.floor(Math.random() * 180) + 40,
      '图层编辑': Math.floor(Math.random() * 120) + 20,
      '权限管理': Math.floor(Math.random() * 80) + 10,
    }
  })
}

// 数据生成
const actionData = ref(generateActionData(selectedTenant.value))
const trendData = ref(generateTrendData(7))

// 计算属性
const selectedTenantName = computed(() => {
  return tenantOptions.find(t => t.id === selectedTenant.value)?.name || ''
})

// 获取操作类型颜色的函数
const getActionColor = (index: number) => {
  const colors = [
    'bg-blue-500',
    'bg-green-500', 
    'bg-purple-500',
    'bg-yellow-500',
    'bg-gray-500'
  ]
  return colors[index] || 'bg-gray-500'
}

// 柱状图配置 - 与React版本Recharts对应的ECharts配置
const barChartOption = computed(() => {
  if (!actionData.value || actionData.value.length === 0) {
    return {}
  }
  
  return {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e5e7eb',
      borderWidth: 1,
      borderRadius: 8,
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
      textStyle: {
        color: '#374151'
      },
      formatter: (params: any) => {
        const data = params[0]
        return `${data.name}<br/>${data.value} 次`
      }
    },
    grid: {
      left: '15%',
      right: '4%',
      bottom: '10%',
      top: '10%',
      containLabel: false
    },
    xAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e5e7eb'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: '#e5e7eb',
          type: 'dashed'
        }
      }
    },
    yAxis: {
      type: 'category',
      data: actionData.value.map(item => item.name),
      axisLine: {
        lineStyle: {
          color: '#e5e7eb'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 12
      }
    },
    series: [
      {
        type: 'bar',
        data: actionData.value.map(item => ({
          name: item.name,
          value: item.value
        })),
        itemStyle: {
          color: '#3b82f6',
          borderRadius: [0, 4, 4, 0]
        },
        barWidth: '60%'
      }
    ]
  }
})

// 趋势图配置 - 与React版本Recharts对应的ECharts配置
const trendChartOption = computed(() => {
  if (!trendData.value || trendData.value.length === 0) {
    return {}
  }
  
  return {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e5e7eb',
      borderWidth: 1,
      borderRadius: 8,
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
      textStyle: {
        color: '#374151'
      },
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          result += `${param.seriesName}: ${param.value} 次<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['地图查询', '数据上传', '数据下载'],
      bottom: 0,
      textStyle: {
        color: '#6b7280'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: trendData.value.map(item => item.date),
      axisLine: {
        lineStyle: {
          color: '#e5e7eb'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: '#e5e7eb',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '地图查询',
        type: 'bar',
        stack: 'total',
        data: trendData.value.map(item => item['地图查询']),
        itemStyle: {
          color: '#3b82f6',
          borderRadius: [4, 4, 0, 0]
        }
      },
      {
        name: '数据上传',
        type: 'bar',
        stack: 'total',
        data: trendData.value.map(item => item['数据上传']),
        itemStyle: {
          color: '#10b981',
          borderRadius: [4, 4, 0, 0]
        }
      },
      {
        name: '数据下载',
        type: 'bar',
        stack: 'total',
        data: trendData.value.map(item => item['数据下载']),
        itemStyle: {
          color: '#8b5cf6',
          borderRadius: [4, 4, 0, 0]
        }
      }
    ]
  }
})

// 事件处理函数 - 与React版本完全对应
const handleTenantChange = () => {
  console.log('租户选择变化:', selectedTenant.value)
  // 重新生成数据
  actionData.value = generateActionData(selectedTenant.value)
}

const handleTimeRangeChange = (value: string) => {
  timeRange.value = value
  // 重新生成趋势数据
  const days = value === '7d' ? 7 : value === '30d' ? 30 : 90
  trendData.value = generateTrendData(days)
}

const handleTimeGranularityChange = (value: string) => {
  timeGranularity.value = value
  console.log('时间粒度变化:', value)
}

// 组件挂载
onMounted(() => {
  console.log('TenantActionRank页面已挂载')
  // 模拟数据加载
  chartLoading.value = true
  setTimeout(() => {
    chartLoading.value = false
  }, 500)
})
</script>

<style scoped>
/* 与React版本完全一致的样式 */
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* 悬停效果 */
.hover\:bg-gray-50:hover {
  background-color: #f9fafb;
}

.hover\:text-blue-900:hover {
  color: #1e3a8a;
}

/* 焦点样式 */
.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-blue-500:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.focus\:border-blue-500:focus {
  border-color: #3b82f6;
}

/* 禁用状态 */
.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

/* 表格样式 */
.divide-y > :not([hidden]) ~ :not([hidden]) {
  border-top-width: 1px;
  border-color: #e5e7eb;
}

.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  border-color: #e5e7eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-1.md\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .grid-cols-1.lg\:grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}
</style>