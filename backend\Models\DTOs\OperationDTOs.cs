namespace MapStudio.Api.Models.DTOs;

public class DailyStatsResponse
{
    public List<DailyStatsDto> DailyStatsData { get; set; } = new();
    public DailyStatsOverviewDto Overview { get; set; } = new();
}

public class DailyStatsDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string ServiceType { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public string Size { get; set; } = string.Empty;
    public int DailyOperations { get; set; }
    public string Color { get; set; } = string.Empty;
}

public class DailyStatsOverviewDto
{
    public int TotalTenants { get; set; }
    public int AverageDailyOperations { get; set; }
    public int MaxDailyOperations { get; set; }
    public TenantSummaryDto? MaxTenant { get; set; }
    public int TotalOperations { get; set; }
}

public class TenantSummaryDto
{
    public string Name { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
}

public class MonthlyStatsResponse
{
    public MonthlyStatsOverviewDto Overview { get; set; } = new();
    public List<MonthlyDataDto> MonthlyData { get; set; } = new();
}

public class MonthlyStatsOverviewDto
{
    public int TotalOperations { get; set; }
    public int MonthlyAvg { get; set; }
    public int MaxMonthOperations { get; set; }
    public string MaxMonthName { get; set; } = string.Empty;
    public double AverageGrowth { get; set; }
}

public class MonthlyDataDto
{
    public string Month { get; set; } = string.Empty;
    public int Operations { get; set; }
    public int PrevYearOperations { get; set; }
    public double YoyGrowth { get; set; }
}

public class OperationLogDto
{
    public string Id { get; set; } = string.Empty;
    public string Time { get; set; } = string.Empty;
    public string Tenant { get; set; } = string.Empty;
    public string? User { get; set; }
    public string Type { get; set; } = string.Empty;
    public string? Object { get; set; }
    public string? Detail { get; set; }
}

public class OperationLogListResponse
{
    public List<OperationLogDto> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int CurrentPage { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
}

public class AccessStatsResponse
{
    public List<AccessStatsDto> AccessData { get; set; } = new();
    public AccessStatsOverviewDto Overview { get; set; } = new();
}

public class AccessStatsDto
{
    public string Date { get; set; } = string.Empty;
    public int AccessCount { get; set; }
    public int UniqueVisitors { get; set; }
}

public class AccessStatsOverviewDto
{
    public int TotalAccess { get; set; }
    public int TotalUniqueVisitors { get; set; }
    public double AverageDaily { get; set; }
    public double GrowthRate { get; set; }
}

public class ActionTypeRankResponse
{
    public List<ActionTypeRankDto> RankingData { get; set; } = new();
    public ActionTypeOverviewDto Overview { get; set; } = new();
}

public class ActionTypeRankDto
{
    public string ActionType { get; set; } = string.Empty;
    public string ActionName { get; set; } = string.Empty;
    public int Count { get; set; }
    public double Percentage { get; set; }
}

public class ActionTypeOverviewDto
{
    public int TotalOperations { get; set; }
    public string MostPopularAction { get; set; } = string.Empty;
    public double MostPopularPercentage { get; set; }
}

public class TenantActionRankResponse
{
    public List<TenantActionRankDto> RankingData { get; set; } = new();
}

public class TenantActionRankDto
{
    public string TenantId { get; set; } = string.Empty;
    public string TenantName { get; set; } = string.Empty;
    public int OperationCount { get; set; }
    public string ServiceType { get; set; } = string.Empty;
    public string Size { get; set; } = string.Empty;
    public double GrowthRate { get; set; }
}

public class UserUsageResponse
{
    public List<UserUsageDto> UsageData { get; set; } = new();
    public UserUsageOverviewDto Overview { get; set; } = new();
}

public class UserUsageDto
{
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string TenantName { get; set; } = string.Empty;
    public int OperationCount { get; set; }
    public string LastActiveTime { get; set; } = string.Empty;
}

public class UserUsageOverviewDto
{
    public int TotalUsers { get; set; }
    public int ActiveUsers { get; set; }
    public double AverageOperations { get; set; }
}

/// <summary>
/// 服务状态统计数据传输对象
/// </summary>
public class ServiceStatusStatsDto
{
    /// <summary>
    /// 总用户数：统计ms_auth_applications表的数据量
    /// </summary>
    public int TotalUsers { get; set; }

    /// <summary>
    /// 活跃用户：统计ms_operation_logs表的数据，按TenantId字段去重，统计数据量
    /// </summary>
    public int ActiveUsers { get; set; }

    /// <summary>
    /// 今日访问量：统计ms_operation_logs表的数据，查询当天的总数据量
    /// </summary>
    public int TodayVisits { get; set; }

    /// <summary>
    /// 待审批申请：统计ms_auth_applications表的数据，按照status=Pending过滤，统计总数据量
    /// </summary>
    public int PendingApplications { get; set; }
}

/// <summary>
/// 月度使用率数据传输对象
/// </summary>
public class MonthlyUsageDataDto
{
    /// <summary>
    /// 月份标签，格式如"2025-01"
    /// </summary>
    public string Month { get; set; } = string.Empty;

    /// <summary>
    /// 该月的操作总量
    /// </summary>
    public int Operations { get; set; }

    /// <summary>
    /// 该月的活跃租户数（可选）
    /// </summary>
    public int? ActiveTenants { get; set; }
}

/// <summary>
/// 近6个月使用率统计响应数据传输对象
/// </summary>
public class SixMonthUsageStatsResponseDto
{
    /// <summary>
    /// 近6个月的使用率数据列表
    /// </summary>
    public List<MonthlyUsageDataDto> MonthlyData { get; set; } = new();

    /// <summary>
    /// 近6个月的总操作量
    /// </summary>
    public int TotalOperations { get; set; }

    /// <summary>
    /// 近6个月的平均月操作量
    /// </summary>
    public double AverageOperations { get; set; }

    /// <summary>
    /// 相比上一个6个月期间的增长率（可选）
    /// </summary>
    public double? GrowthRate { get; set; }
}

/// <summary>
/// 状态码统计数据传输对象，用于表示单个状态码的统计数据
/// </summary>
public class StatusCodeStatsDto
{
    /// <summary>
    /// 状态码，如200、404等
    /// </summary>
    public int StatusCode { get; set; }

    /// <summary>
    /// 状态名称，如"正常运行"、"资源不存在"等
    /// </summary>
    public string StatusName { get; set; } = string.Empty;

    /// <summary>
    /// 该状态码出现的次数
    /// </summary>
    public int Count { get; set; }

    /// <summary>
    /// 该状态码占总数量的百分比
    /// </summary>
    public double Percentage { get; set; }
}

/// <summary>
/// 运行状况统计响应数据传输对象，用于表示运行状况统计的响应数据
/// </summary>
public class ServiceStatusStatsResponseDto
{
    /// <summary>
    /// ms_operation_logs表的总数据量
    /// </summary>
    public int TotalLogs { get; set; }

    /// <summary>
    /// 各个状态码的统计数据列表
    /// </summary>
    public List<StatusCodeStatsDto> StatusCodeStats { get; set; } = new();

    /// <summary>
    /// 数据最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// 租户信息数据传输对象
/// </summary>
[SqlSugar.SugarTable("ms_auth_applications")]
public class TenantDataDto
{
    /// <summary>
    /// 租户ID
    /// </summary>
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// 租户名称
    /// </summary>
    public string TenantName { get; set; } = string.Empty;
}

/// <summary>
/// 日统计汇总数据传输对象，用于表示日统计汇总数据
/// </summary>
public class DailyStatsSummaryDto
{
    /// <summary>
    /// 租户总数：统计ms_auth_applications表的数据量
    /// </summary>
    public int TotalTenants { get; set; }

    /// <summary>
    /// 平均日操作次数：按照操作时间按天粒度汇聚统计，将每天统计好的操作数求平均值
    /// </summary>
    public decimal AverageDailyOperations { get; set; }

    /// <summary>
    /// 最大日操作数：按照操作时间按天粒度汇聚统计，统计最多数据的那一天的操作数量
    /// </summary>
    public int MaxDailyOperations { get; set; }

    /// <summary>
    /// 操作总量：统计ms_operation_logs表的数据，查询当天的总数据量
    /// </summary>
    public int TotalOperationsToday { get; set; }
}

/// <summary>
/// 日统计数据传输对象，用于daily-stats页面的统计功能
/// </summary>
public class DailyStatsDTO
{
    /// <summary>
    /// 租户总数：统计ms_auth_applications表的数据量
    /// </summary>
    public int TotalTenants { get; set; }

    /// <summary>
    /// 平均日操作次数：按天汇聚统计操作数，然后求平均值
    /// </summary>
    public decimal AverageDailyOperations { get; set; }

    /// <summary>
    /// 最大日操作数：按天汇聚统计，找出最大值
    /// </summary>
    public int MaxDailyOperations { get; set; }

    /// <summary>
    /// 操作总量：统计ms_operation_logs表当天的总数据量
    /// </summary>
    public int TotalOperations { get; set; }
}
public class TenantOperationDistributionItem
{
    public string TenantName { get; set; } = string.Empty;
    public int OperationCount { get; set; }
}