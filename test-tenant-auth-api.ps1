# 租户授权申请流程测试脚本
Write-Host "=== 租户授权申请流程测试 ===" -ForegroundColor Green

$apiBase = "http://localhost:5172/api"

# 1. 提交租户授权申请
Write-Host "`n1. 提交租户授权申请..." -ForegroundColor Yellow

$applicationData = @{
    tenantName = "测试科技有限公司"
    tenantId = "TEST_TECH_001"
    contactPerson = "张三"
    contactEmail = "<EMAIL>"
    contactPhone = "***********"
    serviceType = "enterprise"
    authPeriod = "1year"
    permissionScope = "admin"
    description = "需要企业级地图服务用于智能城市项目开发"
    businessLicense = "license-file-id"
    organizationCode = "org-code-file-id"
}

$body = $applicationData | ConvertTo-Json -Depth 10
Write-Host "请求数据: $body" -ForegroundColor Gray

try {
    $submitResponse = Invoke-RestMethod -Uri "$apiBase/Tenant/auth-applications" -Method POST -Body $body -ContentType "application/json"
    Write-Host "✅ 申请提交成功!" -ForegroundColor Green
    Write-Host "申请ID: $($submitResponse.data.id)" -ForegroundColor Green
    Write-Host "状态: $($submitResponse.data.status)" -ForegroundColor Green
    $applicationId = $submitResponse.data.id
}
catch {
    Write-Host "❌ 申请提交失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "详细错误: $($_.ErrorDetails.Message)" -ForegroundColor Red
    exit 1
}

# 2. 获取申请列表（待审批）
Write-Host "`n2. 获取待审批申请列表..." -ForegroundColor Yellow

try {
    $listResponse = Invoke-RestMethod -Uri "$apiBase/Tenant/auth-applications?status=pending&page=1&pageSize=10" -Method GET
    Write-Host "✅ 获取申请列表成功!" -ForegroundColor Green
    Write-Host "总数: $($listResponse.data.totalCount) 条" -ForegroundColor Green
    
    if ($listResponse.data.items.Count -gt 0) {
        Write-Host "`n申请列表:" -ForegroundColor Cyan
        foreach ($item in $listResponse.data.items) {
            Write-Host "- ID: $($item.id)" -ForegroundColor White
            Write-Host "  租户: $($item.tenantName)" -ForegroundColor White
            Write-Host "  联系人: $($item.contactPerson)" -ForegroundColor White
            Write-Host "  服务: $($item.serviceTypeName)" -ForegroundColor White
            Write-Host "  状态: $($item.status)" -ForegroundColor White
            Write-Host "  时间: $($item.applyTime)" -ForegroundColor White
            Write-Host ""
        }
    }
}
catch {
    Write-Host "❌ 获取申请列表失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "详细错误: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

# 3. 审批申请（通过）
if ($applicationId) {
    Write-Host "`n3. 审批申请（通过）..." -ForegroundColor Yellow
    
    $approvalData = @{
        action = "approve"
        comments = "申请材料齐全，符合要求，同意通过。"
    }
    
    $approvalBody = $approvalData | ConvertTo-Json
    
    try {
        $approvalResponse = Invoke-RestMethod -Uri "$apiBase/Tenant/auth-applications/$applicationId/approve" -Method PUT -Body $approvalBody -ContentType "application/json"
        Write-Host "✅ 审批通过成功!" -ForegroundColor Green
        Write-Host "申请ID: $($approvalResponse.data.id)" -ForegroundColor Green
        Write-Host "新状态: $($approvalResponse.data.status)" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ 审批失败: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "详细错误: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
}

# 4. 再次获取申请列表（已通过）
Write-Host "`n4. 获取已通过申请列表..." -ForegroundColor Yellow

try {
    $approvedListResponse = Invoke-RestMethod -Uri "$apiBase/Tenant/auth-applications?status=approved&page=1&pageSize=10" -Method GET
    Write-Host "✅ 获取已通过申请列表成功!" -ForegroundColor Green
    Write-Host "总数: $($approvedListResponse.data.totalCount) 条" -ForegroundColor Green
    
    if ($approvedListResponse.data.items.Count -gt 0) {
        Write-Host "`n已通过申请列表:" -ForegroundColor Cyan
        foreach ($item in $approvedListResponse.data.items) {
            Write-Host "- ID: $($item.id)" -ForegroundColor White
            Write-Host "  租户: $($item.tenantName)" -ForegroundColor White
            Write-Host "  状态: $($item.status)" -ForegroundColor White
            Write-Host ""
        }
    }
}
catch {
    Write-Host "❌ 获取已通过申请列表失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "详细错误: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "功能验证: 申请提交 → 数据保存 → 审批列表显示 → 状态更新 ✅" -ForegroundColor Green