# MySQL MCP Server 配置说明

本项目已配置MySQL MCP Server，用于连接到指定的MySQL数据库。

## 数据库连接信息

- **主机**: *************
- **端口**: 3307
- **数据库**: MTNOH_AAA_Platform
- **用户名**: dtauser
- **密码**: dtauser

## 文件说明

### 配置文件
- `mcp-server-config.json` - MCP server主配置文件
- `.env.mcp` - 环境变量配置文件

### 启动脚本
- `start-mcp-server.sh` - Linux/macOS启动脚本
- `start-mcp-server.bat` - Windows启动脚本

## 使用方法

### 方法1: 使用启动脚本

**Windows系统:**
```bash
start-mcp-server.bat
```

**Linux/macOS系统:**
```bash
chmod +x start-mcp-server.sh
./start-mcp-server.sh
```

### 方法2: 直接命令行启动

```bash
uvx mcp-server-mysql \
  --host ************* \
  --port 3307 \
  --username dtauser \
  --password dtauser \
  --database MTNOH_AAA_Platform
```

### 方法3: 使用配置文件

如果您的IDE或工具支持MCP配置文件，可以直接使用 `mcp-server-config.json` 文件。

## 安装依赖

首先确保您已经安装了MCP server：

```bash
# 安装MySQL MCP server
pip install mcp-server-mysql
```

或者使用uvx（推荐）：

```bash
# uvx会自动处理依赖
uvx mcp-server-mysql --help
```

## 验证连接

启动server后，您应该能看到类似以下的输出：
```
正在启动 MySQL MCP Server...
数据库连接信息:
  主机: *************
  端口: 3307
  数据库: MTNOH_AAA_Platform
  用户名: dtauser

Server started on port ...
```

## 故障排除

### 连接问题
1. 确认MySQL服务器正在运行
2. 检查网络连接到 *************:3307
3. 验证用户名密码是否正确
4. 确认数据库 MTNOH_AAA_Platform 存在

### 权限问题
确保用户 `dtauser` 具有访问数据库的适当权限。

### 防火墙问题
确认防火墙允许连接到端口 3307。

## 支持的操作

MCP server启动后，您可以通过MCP协议执行以下操作：
- 查询数据库结构
- 执行SQL查询
- 获取表信息
- 查看数据
- 等等...

具体的API接口取决于您使用的MCP客户端工具。