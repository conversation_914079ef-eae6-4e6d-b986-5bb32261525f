# API端点实现计划：服务状态统计

## 概述
本文档描述了如何在后端实现服务状态统计的API端点，包括TenantController的修改和相关服务方法的实现。

## 实现步骤

### 1. 创建DTO类
首先需要创建ServiceStatusResponse DTO类来封装返回数据。

#### 1.1 ServiceStatusResponse DTO
在`backend/Models/DTOs/`目录下创建`ServiceStatusDTOs.cs`文件：

```csharp
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MapStudio.Api.Models.DTOs
{
    /// <summary>
    /// 服务状态统计响应
    /// </summary>
    public class ServiceStatusResponse
    {
        /// <summary>
        /// 总用户数 - 统计ms_auth_applications表的数据量
        /// </summary>
        public int TotalUsers { get; set; }

        /// <summary>
        /// 活跃用户数 - 统计ms_operation_logs表的数据，按TenantId字段去重后的数量
        /// </summary>
        public int ActiveUsers { get; set; }

        /// <summary>
        /// 今日访问量 - 统计ms_operation_logs表中当天的数据量
        /// </summary>
        public int TodayVisits { get; set; }

        /// <summary>
        /// 待审批申请数 - 统计ms_auth_applications表中status=Pending的数据量
        /// </summary>
        public int PendingApplications { get; set; }
    }
}
```

### 2. 更新ITenantService接口
在`backend/Services/Interfaces/ITenantService.cs`中添加新方法：

```csharp
/// <summary>
/// 获取服务状态统计数据
/// </summary>
Task<ServiceStatusResponse> GetServiceStatusStatisticsAsync();
```

### 3. 实现TenantService方法
在`backend/Services/Implementations/TenantService.cs`中实现统计逻辑：

```csharp
/// <summary>
/// 获取服务状态统计数据
/// </summary>
public async Task<ServiceStatusResponse> GetServiceStatusStatisticsAsync()
{
    try
    {
        // 1. 总用户数：统计ms_auth_applications表的数据量
        var totalUsers = await _db.Queryable<AuthApplication>().CountAsync();

        // 2. 活跃用户：统计ms_operation_logs表的数据，按TenantId字段去重，统计数据量
        var activeUsers = await _db.Queryable<OperationLog>()
            .Where(log => log.OperatedAt.Date == DateTime.Today)
            .GroupBy(log => log.TenantId)
            .CountAsync();

        // 3. 今日访问量：统计ms_operation_logs表的数据，查询当天的总数据量
        var todayVisits = await _db.Queryable<OperationLog>()
            .Where(log => log.OperatedAt.Date == DateTime.Today)
            .CountAsync();

        // 4. 待审批申请：统计ms_auth_applications表的数据，按照status=Pending过滤，统计总数据量
        var pendingApplications = await _db.Queryable<AuthApplication>()
            .Where(app => app.Status == "Pending")
            .CountAsync();

        return new ServiceStatusResponse
        {
            TotalUsers = totalUsers,
            ActiveUsers = activeUsers,
            TodayVisits = todayVisits,
            PendingApplications = pendingApplications
        };
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "获取服务状态统计数据失败");
        throw;
    }
}
```

### 4. 更新TenantController
在`backend/Controllers/TenantController.cs`中添加新的API端点：

```csharp
/// <summary>
/// 获取服务状态统计数据
/// </summary>
[HttpGet("service-status-statistics")]
[ConditionalAuthorize]
public async Task<ActionResult<ApiResponse<ServiceStatusResponse>>> GetServiceStatusStatistics()
{
    try
    {
        var result = await _tenantService.GetServiceStatusStatisticsAsync();
        return Ok(ApiResponse<ServiceStatusResponse>.SuccessResult(result, "获取服务状态统计数据成功"));
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "获取服务状态统计数据失败");
        return StatusCode(500, ApiResponse<ServiceStatusResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
    }
}
```

### 5. 性能优化建议

#### 5.1 数据库索引
为提高查询性能，建议在以下字段上创建索引：
- `ms_auth_applications`表的`Status`字段
- `ms_operation_logs`表的`OperatedAt`和`TenantId`字段

#### 5.2 查询优化
- 使用异步查询避免阻塞
- 尽量在数据库层面完成统计计算，避免加载大量数据到内存

### 6. 错误处理
确保所有可能的异常都被正确捕获和处理：
- 数据库连接异常
- 查询执行异常
- 其他未预期的异常

### 7. 日志记录
添加适当的日志记录以便于调试和监控：
- 成功执行的日志
- 异常情况的日志
- 性能相关的日志

## 部署注意事项
1. 确保新添加的DTO类已正确编译
2. 验证API端点URL无冲突
3. 测试在不同数据量下的性能表现
4. 确保权限验证正常工作