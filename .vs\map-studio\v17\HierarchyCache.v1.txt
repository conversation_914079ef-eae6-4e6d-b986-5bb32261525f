﻿++解决方案 'map-studio' ‎ (1 个项目，共 1 个)
i:{00000000-0000-0000-0000-000000000000}:map-studio.sln
++MapStudio.Api
i:{00000000-0000-0000-0000-000000000000}:MapStudio.Api
++Connected Services 
i:{************************************}:>162
++Properties
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\properties\
++依赖项
i:{************************************}:>164
++Attributes
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\attributes\
++Controllers
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\controllers\
++.gitkeep
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\controllers\.gitkeep
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\models\dtos\.gitkeep
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\models\.gitkeep
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\services\implementations\.gitkeep
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\services\interfaces\.gitkeep
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\services\.gitkeep
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\data\.gitkeep
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\extensions\.gitkeep
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\middleware\.gitkeep
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\models\entities\.gitkeep
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\models\viewmodels\.gitkeep
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\data\context\.gitkeep
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\data\repositories\.gitkeep
++AuthController.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\controllers\authcontroller.cs
++FileController.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\controllers\filecontroller.cs
++OperationController.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\controllers\operationcontroller.cs
++SystemController.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\controllers\systemcontroller.cs
++TenantController.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\controllers\tenantcontroller.cs
++TestController.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\controllers\testcontroller.cs
++Data
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\data\
++Extensions
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\extensions\
++logs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\logs\
++Middleware
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\middleware\
++Models
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\models\
++DTOs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\models\dtos\
++AuthDTOs.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\models\dtos\authdtos.cs
++FileDTOs.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\models\dtos\filedtos.cs
++OperationDTOs.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\models\dtos\operationdtos.cs
++TenantDTOs.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\models\dtos\tenantdtos.cs
++Entities
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\models\entities\
++ViewModels
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\models\viewmodels\
++Services
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\services\
++Implementations
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\services\implementations\
++AuthService.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\services\implementations\authservice.cs
++FileService.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\services\implementations\fileservice.cs
++MetadataService.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\services\implementations\metadataservice.cs
++OperationService.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\services\implementations\operationservice.cs
++TenantService.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\services\implementations\tenantservice.cs
++Interfaces
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\services\interfaces\
++IAuthService.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\services\interfaces\iauthservice.cs
++IFileService.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\services\interfaces\ifileservice.cs
++IMetadataService.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\services\interfaces\imetadataservice.cs
++IOperationService.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\services\interfaces\ioperationservice.cs
++ITenantService.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\services\interfaces\itenantservice.cs
++appsettings.json
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\appsettings.json
++Dockerfile
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\dockerfile
++MapStudio.Api.http
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\mapstudio.api.http
++Program.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\program.cs
++start-test-server.bat
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\start-test-server.bat
++start-test-server.sh
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\start-test-server.sh
++test-db-connection.ps1
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\test-db-connection.ps1
++test-db-connection.sh
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\test-db-connection.sh
++未发现任何服务依赖项
i:{************************************}:>163
++launchSettings.json
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\properties\launchsettings.json
++包
i:{************************************}:>220
++分析器
i:{************************************}:>197
++框架
i:{************************************}:>217
++ConditionalAuthorizeAttribute.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\attributes\conditionalauthorizeattribute.cs
++Context
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\data\context\
++Repositories
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\data\repositories\
++ServiceCollectionExtensions.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\extensions\servicecollectionextensions.cs
++mapstudio-api-20250909.txt
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\logs\mapstudio-api-20250909.txt
++GlobalExceptionMiddleware.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\middleware\globalexceptionmiddleware.cs
++OperationLoggingMiddleware.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\middleware\operationloggingmiddleware.cs
++AlarmSetting.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\models\entities\alarmsetting.cs
++AuthApplication.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\models\entities\authapplication.cs
++File.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\models\entities\file.cs
++OperationLog.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\models\entities\operationlog.cs
++PermissionScope.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\models\entities\permissionscope.cs
++ServiceType.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\models\entities\servicetype.cs
++Tenant.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\models\entities\tenant.cs
++User.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\models\entities\user.cs
++ApiViewModels.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\models\viewmodels\apiviewmodels.cs
++appsettings.Development.json
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\appsettings.development.json
++appsettings.Production.json
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\appsettings.production.json
++appsettings.Testing.json
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\appsettings.testing.json
++Microsoft.AspNetCore.Authentication.JwtBearer (9.0.8)
i:{************************************}:>230
++Microsoft.AspNetCore.Cors (2.2.0)
i:{************************************}:>225
++Microsoft.AspNetCore.OpenApi (9.0.8)
i:{************************************}:>229
++MySql.Data (9.1.0)
i:{************************************}:>231
++Newtonsoft.Json (13.0.3)
i:{************************************}:>223
++Serilog.AspNetCore (8.0.3)
i:{************************************}:>224
++Serilog.Sinks.Console (6.0.0)
i:{************************************}:>227
++Serilog.Sinks.File (6.0.0)
i:{************************************}:>228
++SqlSugar (5.1.4.157)
i:{************************************}:>226
++Swashbuckle.AspNetCore (7.1.0)
i:{************************************}:>222
++System.IdentityModel.Tokens.Jwt (8.2.1)
i:{************************************}:>221
++Microsoft.AspNetCore.Analyzers
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.analyzers.dll
++Microsoft.AspNetCore.App.Analyzers
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.6\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
++Microsoft.AspNetCore.App.CodeFixes
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.6\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
++Microsoft.AspNetCore.Components.Analyzers
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.6\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
++Microsoft.AspNetCore.Mvc.Analyzers
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.mvc.analyzers.dll
++Microsoft.AspNetCore.Razor.Utilities.Shared
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.razor\source-generators\microsoft.aspnetcore.razor.utilities.shared.dll
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++Microsoft.CodeAnalysis.Razor.Compiler
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.razor\source-generators\microsoft.codeanalysis.razor.compiler.dll
++Microsoft.Extensions.Logging.Generators
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.6\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
++Microsoft.Extensions.ObjectPool
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.razor\source-generators\microsoft.extensions.objectpool.dll
++Microsoft.Extensions.Options.SourceGeneration
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.6\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
++Microsoft.Interop.ComInterfaceGenerator
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.6\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
++Microsoft.Interop.JavaScript.JSImportGenerator
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.6\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
++Microsoft.Interop.LibraryImportGenerator
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.6\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
++Microsoft.Interop.SourceGeneration
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.6\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
++System.Collections.Immutable
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.razor\source-generators\system.collections.immutable.dll
++System.Text.Json.SourceGeneration
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.6\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
++System.Text.RegularExpressions.Generator
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.6\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
++Microsoft.AspNetCore.App
i:{************************************}:>218
++Microsoft.NETCore.App
i:{************************************}:>219
++DatabaseInitializer.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\data\context\databaseinitializer.cs
++TestDatabaseInitializer.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\data\context\testdatabaseinitializer.cs
++SmsAlarmSetting.cs
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\models\entities\smsalarmsetting.cs
++mapstudio-api-20250910.txt
i:{************************************}:d:\路网通项目svn\1_后台专题类模块\map-studio\backend\logs\mapstudio-api-20250910.txt
