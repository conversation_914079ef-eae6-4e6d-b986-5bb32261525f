// 前端API模式验证和设置脚本
// 在浏览器开发者工具控制台中运行

console.log('🔧 Map Studio API模式检查工具');
console.log('================================');

// 检查当前模式
const currentMockMode = localStorage.getItem('api_mock_mode');
console.log(`📊 当前localStorage设置: api_mock_mode = ${currentMockMode}`);

// 检查环境变量
console.log('🌍 环境变量信息:');
console.log(`  - MODE: ${import.meta?.env?.MODE || 'unknown'}`);
console.log(`  - VITE_API_BASE_URL: ${import.meta?.env?.VITE_API_BASE_URL || 'not set'}`);
console.log(`  - VITE_ENABLE_MOCK: ${import.meta?.env?.VITE_ENABLE_MOCK || 'not set'}`);

// 强制切换到真实API模式
function switchToRealAPI() {
    console.log('🔄 切换到真实API模式...');
    localStorage.setItem('api_mock_mode', 'false');
    console.log('✅ 已设置 api_mock_mode = false');

    console.log('🔄 准备重新加载页面以应用更改...');
    setTimeout(() => {
        window.location.reload();
    }, 2000);
}

// 测试API连接
async function testAPIConnection() {
    console.log('🧪 测试API连接...');

    const testEndpoints = [
        'http://localhost:5172/api/Metadata/service-types',
        'http://localhost:5172/api/Metadata/permission-scopes'
    ];

    for (const url of testEndpoints) {
        try {
            console.log(`📡 测试: ${url}`);
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Client-Version': '1.0.0'
                }
            });

            if (response.ok) {
                const data = await response.json();
                console.log(`✅ ${url} - 状态: ${response.status}`);
                console.log(`📄 数据预览:`, data.data?.slice(0, 2));
            } else {
                console.log(`❌ ${url} - 状态: ${response.status}`);
            }
        } catch (error) {
            console.log(`❌ ${url} - 错误: ${error.message}`);
        }
    }
}

// 显示当前状态
if (currentMockMode === 'true') {
    console.log('⚠️  当前处于Mock模式');
    console.log('💡 建议运行: switchToRealAPI()');
} else if (currentMockMode === 'false') {
    console.log('✅ 当前处于真实API模式');
    console.log('💡 可以运行: testAPIConnection()');
} else {
    console.log('❓ Mock模式未设置，使用默认配置');
    console.log('💡 建议运行: switchToRealAPI()');
}

console.log('');
console.log('🛠️  可用命令:');
console.log('  - switchToRealAPI()    // 切换到真实API模式');
console.log('  - testAPIConnection()  // 测试API连接');
console.log('================================');

// 导出函数到全局作用域
window.switchToRealAPI = switchToRealAPI;
window.testAPIConnection = testAPIConnection;
