# Map Studio 部署指南

## 📋 目录

- [概述](#概述)
- [系统要求](#系统要求)
- [快速开始](#快速开始)
- [开发环境部署](#开发环境部署)
- [测试环境部署](#测试环境部署)
- [生产环境部署](#生产环境部署)
- [Docker部署](#Docker部署)
- [监控和维护](#监控和维护)
- [故障排除](#故障排除)
- [最佳实践](#最佳实践)

## 🎯 概述

Map Studio 是基于前后端分离架构的租户管理系统，支持多环境部署。本指南详细说明如何在不同环境中部署和配置系统。

### 架构图

```mermaid
graph TB
    subgraph "用户层"
        A[Web浏览器]
        B[移动端]
    end
    
    subgraph "CDN/负载均衡"
        C[Nginx/Apache]
    end
    
    subgraph "前端服务"
        D[Vue 3 应用]
    end
    
    subgraph "后端服务"
        E[.NET 9 API]
        F[身份认证]
        G[业务逻辑]
    end
    
    subgraph "数据层"
        H[MySQL 数据库]
        I[Redis 缓存]
        J[文件存储]
    end
    
    subgraph "监控"
        K[日志系统]
        L[监控告警]
    end
    
    A --> C
    B --> C
    C --> D
    D --> E
    E --> F
    E --> G
    G --> H
    E --> I
    E --> J
    E --> K
    K --> L
```

## 💻 系统要求

### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **磁盘**: 20GB 可用空间
- **网络**: 100Mbps

### 推荐配置
- **CPU**: 4核心
- **内存**: 8GB RAM
- **磁盘**: 50GB SSD
- **网络**: 1Gbps

### 软件要求

#### 开发环境
- Node.js 18.x 或更高版本
- .NET SDK 9.0 或更高版本
- MySQL 8.0 或更高版本
- Git 2.30 或更高版本

#### 生产环境
- Docker 20.x 或更高版本
- Docker Compose 2.x 或更高版本
- Nginx 1.20 或更高版本 (可选)

## 🚀 快速开始

### 一键部署（推荐）

```bash
# 克隆项目
git clone https://github.com/your-org/map-studio.git
cd map-studio

# 启动开发环境
./deploy.sh dev --build

# 或在 Windows 上
./deploy.bat dev --build
```

访问应用：
- 前端：http://localhost:3001
- 后端：http://localhost:5000
- API文档：http://localhost:5000/swagger

### 手动部署

如果不使用 Docker，可以手动部署各个组件：

```bash
# 1. 启动数据库
# 参考下面的数据库配置章节

# 2. 启动后端
cd backend
./start-test-server.sh Development

# 3. 启动前端
cd frontend
npm install
npm run dev
```

## 🔧 开发环境部署

### 环境配置

#### 1. 克隆项目
```bash
git clone https://github.com/your-org/map-studio.git
cd map-studio
```

#### 2. 配置数据库
```bash
# 使用 Docker 启动 MySQL
docker run --name mapstudio-mysql \
  -e MYSQL_ROOT_PASSWORD=mapstudio_password \
  -e MYSQL_DATABASE=mapstudio_dev \
  -e MYSQL_USER=mapstudio \
  -e MYSQL_PASSWORD=mapstudio_user_password \
  -p 3306:3306 \
  -d mysql:8.0

# 或者安装本地 MySQL 并创建数据库
mysql -u root -p
CREATE DATABASE mapstudio_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 3. 配置后端
```bash
cd backend

# 复制配置文件
cp appsettings.Development.json appsettings.Local.json

# 编辑配置文件
vim appsettings.Local.json
```

配置文件示例：
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Port=3306;Database=mapstudio_dev;Uid=mapstudio;Pwd=mapstudio_user_password;charset=utf8mb4;"
  },
  "JwtSettings": {
    "SecretKey": "your-super-secret-key-minimum-32-characters",
    "Issuer": "MapStudio.Api.Dev",
    "Audience": "MapStudio.Dev",
    "ExpirationInMinutes": 60
  }
}
```

#### 4. 启动后端服务
```bash
# 恢复包依赖
dotnet restore

# 运行数据库迁移
dotnet ef database update

# 启动服务
dotnet run --environment Development
```

#### 5. 配置前端
```bash
cd frontend

# 安装依赖
npm install

# 创建环境配置
cp .env.example .env.local
```

编辑 `.env.local`：
```
VITE_API_BASE_URL=http://localhost:5000/api
VITE_MOCK_MODE=false
```

#### 6. 启动前端服务
```bash
npm run dev
```

### 开发工具配置

#### VS Code 配置
创建 `.vscode/settings.json`：
```json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "eslint.autoFixOnSave": true,
  "vetur.validation.template": false
}
```

#### 调试配置
创建 `.vscode/launch.json`：
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch Backend",
      "type": "coreclr",
      "request": "launch",
      "program": "${workspaceFolder}/backend/bin/Debug/net9.0/MapStudio.Api.dll",
      "args": [],
      "cwd": "${workspaceFolder}/backend",
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  ]
}
```

## 🧪 测试环境部署

### 自动化部署

```bash
# 使用部署脚本
./deploy.sh test --clean --build

# 验证部署
curl -f http://localhost:5001/api/system/health
```

### 手动部署

#### 1. 配置测试数据库
```bash
# 创建测试数据库
docker run --name mapstudio-mysql-test \
  -e MYSQL_ROOT_PASSWORD=test_password \
  -e MYSQL_DATABASE=mapstudio_test \
  -p 3307:3306 \
  -d mysql:8.0
```

#### 2. 配置后端测试环境
```bash
cd backend

# 使用测试配置启动
export ASPNETCORE_ENVIRONMENT=Testing
dotnet run --urls "http://localhost:5001"
```

#### 3. 运行测试数据初始化
```bash
curl -X POST http://localhost:5001/api/test/initialize \
  -H "Content-Type: application/json" \
  -d '{"mode": "integration-test"}'
```

#### 4. 验证测试环境
```bash
# 检查API健康状态
curl http://localhost:5001/api/system/health

# 检查测试数据
curl http://localhost:5001/api/test/status
```

## 🏭 生产环境部署

### 预备工作

#### 1. 服务器准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装 Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### 2. 防火墙配置
```bash
# 开放必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

#### 3. SSL证书配置
```bash
# 安装 Let's Encrypt
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot certonly --standalone -d your-domain.com
```

### 生产部署

#### 1. 项目部署
```bash
# 克隆项目
git clone https://github.com/your-org/map-studio.git
cd map-studio

# 切换到生产分支
git checkout production
```

#### 2. 配置生产环境
创建 `.env.production`：
```
ENVIRONMENT=production
MYSQL_ROOT_PASSWORD=your_secure_password
MYSQL_PASSWORD=your_secure_user_password
JWT_SECRET_KEY=your_super_secure_jwt_key_64_characters_minimum
API_BASE_URL=https://your-domain.com/api
FRONTEND_URL=https://your-domain.com
```

#### 3. 配置数据库
```bash
# 生产数据库配置
docker run --name mapstudio-mysql-prod \
  -e MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD} \
  -e MYSQL_DATABASE=mapstudio_prod \
  -e MYSQL_USER=mapstudio \
  -e MYSQL_PASSWORD=${MYSQL_PASSWORD} \
  -v /data/mysql:/var/lib/mysql \
  -p 3306:3306 \
  --restart unless-stopped \
  -d mysql:8.0
```

#### 4. 配置 Nginx
创建 `/etc/nginx/sites-available/mapstudio`：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;

    # 前端静态文件
    location / {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 后端API
    location /api/ {
        proxy_pass http://localhost:5000/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # API超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 文件上传大小限制
    client_max_body_size 10M;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
}
```

#### 5. 启动生产服务
```bash
# 使用生产配置启动
./deploy.sh prod --build

# 或手动启动
docker-compose --profile production up -d
```

#### 6. 验证部署
```bash
# 检查服务状态
docker-compose ps

# 检查应用健康
curl -f https://your-domain.com/api/system/health

# 检查前端
curl -f https://your-domain.com
```

## 🐳 Docker部署

### Docker Compose 部署

#### 1. 完整环境部署
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

#### 2. 分环境部署
```bash
# 开发环境
docker-compose up -d mysql redis backend frontend

# 测试环境
docker-compose --profile testing up -d

# 生产环境
docker-compose --profile production up -d
```

#### 3. 服务管理
```bash
# 重启服务
docker-compose restart backend

# 更新服务
docker-compose pull
docker-compose up -d

# 扩展服务
docker-compose up -d --scale backend=3
```

### Kubernetes 部署

#### 1. 创建命名空间
```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: mapstudio
```

#### 2. 配置数据库
```yaml
# mysql-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql
  namespace: mapstudio
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mysql
  template:
    metadata:
      labels:
        app: mysql
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        env:
        - name: MYSQL_ROOT_PASSWORD
          value: "mapstudio_password"
        - name: MYSQL_DATABASE
          value: "mapstudio_prod"
        ports:
        - containerPort: 3306
        volumeMounts:
        - name: mysql-storage
          mountPath: /var/lib/mysql
      volumes:
      - name: mysql-storage
        persistentVolumeClaim:
          claimName: mysql-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: mysql-service
  namespace: mapstudio
spec:
  selector:
    app: mysql
  ports:
  - port: 3306
    targetPort: 3306
```

#### 3. 部署应用
```bash
# 应用配置
kubectl apply -f k8s/

# 查看部署状态
kubectl get pods -n mapstudio

# 查看服务
kubectl get svc -n mapstudio
```

## 📊 监控和维护

### 健康检查

#### 1. 应用健康检查
```bash
# 后端健康检查
curl -f http://localhost:5000/api/system/health

# 数据库连接检查
curl -f http://localhost:5000/api/system/health/database

# 服务依赖检查
curl -f http://localhost:5000/api/system/health/dependencies
```

#### 2. 系统监控
```bash
# CPU 和内存使用
docker stats

# 磁盘使用
df -h

# 网络连接
netstat -an | grep :5000
```

### 日志管理

#### 1. 应用日志
```bash
# 查看后端日志
docker-compose logs backend

# 查看前端日志
docker-compose logs frontend

# 实时日志
docker-compose logs -f --tail=100
```

#### 2. 日志轮转
配置 logrotate：
```bash
# /etc/logrotate.d/mapstudio
/var/log/mapstudio/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 app app
    postrotate
        docker-compose restart backend > /dev/null 2>&1 || true
    endscript
}
```

### 备份策略

#### 1. 数据库备份
```bash
# 创建备份脚本 backup.sh
#!/bin/bash
BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

docker exec mapstudio-mysql mysqldump \
  -uroot -p$MYSQL_ROOT_PASSWORD \
  --all-databases > $BACKUP_DIR/backup_$DATE.sql

# 压缩备份
gzip $BACKUP_DIR/backup_$DATE.sql

# 删除30天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete
```

#### 2. 文件备份
```bash
# 备份上传文件
rsync -av ./data/uploads/ /backup/uploads/

# 备份配置文件
tar -czf /backup/config_$(date +%Y%m%d).tar.gz \
  .env docker-compose.yml nginx/
```

#### 3. 自动化备份
```bash
# 添加到 crontab
crontab -e

# 每天凌晨2点备份
0 2 * * * /path/to/backup.sh

# 每周日凌晨1点备份文件
0 1 * * 0 /path/to/file_backup.sh
```

### 性能优化

#### 1. 数据库优化
```sql
-- 添加索引
CREATE INDEX idx_tenant_status ON ms_tenants(status);
CREATE INDEX idx_operation_log_date ON ms_operation_logs(operated_at);

-- 分析表
ANALYZE TABLE ms_auth_applications;
```

#### 2. 缓存配置
```bash
# Redis 性能调优
echo "maxmemory 1gb" >> /etc/redis/redis.conf
echo "maxmemory-policy allkeys-lru" >> /etc/redis/redis.conf
```

#### 3. 前端优化
```bash
# 启用 gzip 压缩
# 在 nginx.conf 中添加
gzip on;
gzip_types text/plain text/css application/json application/javascript;
```

## 🚨 故障排除

### 常见问题

#### 1. 服务无法启动
```bash
# 检查端口占用
sudo netstat -tulpn | grep :5000

# 检查 Docker 状态
docker ps -a

# 查看详细错误
docker-compose logs backend
```

#### 2. 数据库连接失败
```bash
# 检查数据库状态
docker exec mapstudio-mysql mysql -uroot -p -e "SELECT 1"

# 检查网络连接
docker network ls
docker network inspect mapstudio_mapstudio-network
```

#### 3. 前端页面无法加载
```bash
# 检查前端构建
docker exec mapstudio-frontend ls -la /usr/share/nginx/html

# 检查 Nginx 配置
docker exec mapstudio-frontend nginx -t
```

### 调试命令

```bash
# 进入容器调试
docker exec -it mapstudio-backend bash
docker exec -it mapstudio-frontend sh

# 查看容器资源使用
docker stats mapstudio-backend

# 查看容器网络
docker inspect mapstudio-backend | grep -i network
```

### 恢复操作

#### 1. 数据库恢复
```bash
# 从备份恢复
docker exec -i mapstudio-mysql mysql -uroot -p$MYSQL_ROOT_PASSWORD < backup.sql

# 重新初始化
curl -X POST http://localhost:5000/api/test/reset
```

#### 2. 服务重启
```bash
# 重启单个服务
docker-compose restart backend

# 重启所有服务
docker-compose down && docker-compose up -d

# 强制重新创建
docker-compose up -d --force-recreate
```

## 📋 最佳实践

### 安全配置

#### 1. 环境变量管理
```bash
# 使用 .env 文件管理敏感信息
# 永远不要将 .env 文件提交到版本控制

# 生产环境使用强密码
MYSQL_ROOT_PASSWORD=$(openssl rand -hex 32)
JWT_SECRET_KEY=$(openssl rand -hex 64)
```

#### 2. 网络安全
```bash
# 限制数据库访问
# 只允许应用服务器访问数据库端口

# 配置防火墙
sudo ufw deny 3306
sudo ufw allow from 10.0.0.0/8 to any port 3306
```

#### 3. SSL配置
```nginx
# 强制 HTTPS
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

# 禁用不安全的协议
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
```

### 部署流程

#### 1. 蓝绿部署
```bash
# 创建新版本环境
docker-compose -f docker-compose.blue.yml up -d

# 测试新版本
curl -f http://blue.your-domain.com/api/system/health

# 切换流量
# 更新负载均衡器配置

# 停止旧版本
docker-compose -f docker-compose.green.yml down
```

#### 2. 滚动更新
```bash
# 逐个更新服务实例
for i in {1..3}; do
  docker-compose stop backend_$i
  docker-compose up -d backend_$i
  sleep 30  # 等待服务就绪
done
```

### 维护计划

#### 1. 定期维护
- **每日**: 备份数据库和文件
- **每周**: 检查系统性能和日志
- **每月**: 更新安全补丁
- **每季度**: 容量规划和性能调优

#### 2. 监控指标
- **可用性**: > 99.9%
- **响应时间**: < 500ms (95%)
- **错误率**: < 0.1%
- **资源使用**: CPU < 80%, 内存 < 85%

### 版本管理

#### 1. 标签规范
```bash
# 使用语义化版本
git tag v1.0.0
git tag v1.1.0-beta
git tag v1.1.0
```

#### 2. 分支策略
- `main`: 生产环境代码
- `develop`: 开发环境代码
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

---

## 📞 支持和帮助

### 技术支持
- 📧 邮箱：<EMAIL>
- 📞 电话：400-000-0000
- 💬 在线客服：https://support.mapstudio.com

### 社区资源
- 📖 文档中心：https://docs.mapstudio.com
- 🔧 问题反馈：https://github.com/your-org/map-studio/issues
- 💡 功能建议：https://feedback.mapstudio.com

### 更新日志
查看 [CHANGELOG.md](./CHANGELOG.md) 了解版本更新信息。

---

*最后更新：2025年8月25日*