// 样式工具函数 - 完全复刻React版本的cn函数
export const cn = (...classes: (string | undefined | null | boolean)[]) => {
  return classes.filter(Boolean).join(' ');
};

// 条件样式类名工具
export const conditionalClass = (condition: boolean, trueClass: string, falseClass?: string) => {
  return condition ? trueClass : (falseClass || '');
};

// 主题颜色工具
export const themeColors = {
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8'
  },
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    900: '#111827'
  }
};