# Map Studio 前后端联调测试系统

![License](https://img.shields.io/badge/license-MIT-blue.svg)
![Version](https://img.shields.io/badge/version-1.0.0-green.svg)
![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)
![Coverage](https://img.shields.io/badge/coverage-85%25-yellow.svg)

## 📋 项目概述

Map Studio 是一个基于前后端分离架构的租户管理系统，提供完整的前后端联调测试解决方案。系统支持租户授权申请、审批管理、运营统计分析等核心功能，并包含全面的测试体系和自动化部署方案。

### 🎯 核心特性

- **🔐 完整的认证授权体系** - JWT身份认证、角色权限管理
- **👥 租户管理功能** - 申请提交、审批流程、进度跟踪、服务状态监控
- **📊 运营统计分析** - 日度/月度统计、操作日志、用户行为分析
- **🔄 前后端联调测试** - Mock数据切换、集成测试、E2E测试
- **🚀 一键部署** - Docker支持、多环境配置、自动化部署脚本
- **📱 响应式设计** - 支持桌面端和移动端访问

### 🏗️ 技术架构

```mermaid
graph TB
    subgraph "前端 Frontend"
        A[Vue 3 + TypeScript]
        B[Vite + Tailwind CSS]
        C[Pinia 状态管理]
        D[Vitest + Playwright]
    end
    
    subgraph "后端 Backend"
        E[.NET 9 Web API]
        F[SqlSugar ORM]
        G[JWT 认证]
        H[Swagger文档]
    end
    
    subgraph "数据存储 Storage"
        I[MySQL 8.0]
        J[Redis 缓存]
        K[文件存储]
    end
    
    subgraph "测试体系 Testing"
        L[单元测试]
        M[集成测试]
        N[E2E测试]
        O[性能测试]
    end
    
    A --> E
    E --> I
    E --> J
    E --> K
    D --> L
    D --> M
    D --> N
    D --> O
```

## 🚀 快速开始

### 环境要求

- **Node.js** 18.x+
- **.NET SDK** 9.0+
- **MySQL** 8.0+
- **Docker** 20.x+ (可选)

### 一键启动

```bash
# 克隆项目
git clone https://github.com/your-org/map-studio.git
cd map-studio

# 启动开发环境（Docker方式，推荐）
./deploy.sh dev --build

# 或在 Windows 上
./deploy.bat dev --build
```

访问应用：
- 🌐 **前端应用**: http://localhost:3001
- 🔧 **后端API**: http://localhost:5000
- 📚 **API文档**: http://localhost:5000/swagger

### 手动启动

如果不使用Docker，请按以下步骤：

<details>
<summary>点击展开手动启动步骤</summary>

#### 1. 启动数据库
```bash
# 使用Docker启动MySQL
docker run --name mapstudio-mysql \
  -e MYSQL_ROOT_PASSWORD=mapstudio_password \
  -e MYSQL_DATABASE=mapstudio_dev \
  -p 3306:3306 \
  -d mysql:8.0
```

#### 2. 启动后端
```bash
cd backend
dotnet restore
dotnet run --environment Development
```

#### 3. 启动前端
```bash
cd frontend
npm install
npm run dev
```

</details>

## 🧪 测试系统

Map Studio 包含完整的测试体系，支持多层级测试和Mock数据切换。

### 测试架构

```mermaid
graph LR
    A[单元测试] --> B[集成测试]
    B --> C[E2E测试]
    C --> D[性能测试]
    
    E[Mock数据] --> F[API切换]
    F --> G[真实API]
    
    H[前端测试] --> I[Vitest]
    H --> J[Playwright]
    
    K[后端测试] --> L[xUnit]
    K --> M[集成测试]
```

### 运行测试

```bash
# 前端单元测试
cd frontend
npm run test

# 前端集成测试  
npm run test:integration

# E2E端到端测试
npm run test:e2e

# 后端测试
cd backend
dotnet test

# 完整测试套件
./scripts/run-all-tests.sh
```

### Mock数据切换

系统支持Mock数据与真实API的无缝切换：

```typescript
// 在浏览器控制台中切换
import { toggleMockMode } from './config/api.config';

toggleMockMode(true);  // 使用Mock数据
toggleMockMode(false); // 使用真实API
```

### 测试覆盖率

| 测试类型 | 覆盖率目标 | 当前覆盖率 |
|---------|-----------|----------|
| 单元测试 | ≥ 80% | 85% |
| 集成测试 | ≥ 70% | 78% |
| E2E测试 | ≥ 60% | 65% |

## 📁 项目结构

```
map-studio/
├── 📂 frontend/                 # Vue 3 前端应用
│   ├── 📂 src/
│   │   ├── 📂 components/       # Vue组件
│   │   ├── 📂 services/         # API服务层
│   │   ├── 📂 config/          # 配置文件
│   │   ├── 📂 tests/           # 测试文件
│   │   └── 📂 utils/           # 工具函数
│   ├── 📄 package.json
│   ├── 📄 vite.config.ts
│   └── 📄 Dockerfile
├── 📂 backend/                  # .NET 9 后端API
│   ├── 📂 Controllers/         # API控制器
│   ├── 📂 Services/            # 业务服务
│   ├── 📂 Models/              # 数据模型
│   ├── 📂 Data/                # 数据访问层
│   ├── 📂 Middleware/          # 中间件
│   ├── 📄 Program.cs
│   ├── 📄 MapStudio.Api.csproj
│   └── 📄 Dockerfile
├── 📂 scripts/                  # 部署和工具脚本
├── 📄 docker-compose.yml       # Docker编排文件
├── 📄 deploy.sh                # 自动化部署脚本
├── 📄 TESTING_GUIDE.md         # 测试指南
├── 📄 DEPLOYMENT_GUIDE.md      # 部署指南
└── 📄 README.md                # 项目说明
```

## 🔧 核心功能

### 1. 租户管理流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant D as 数据库
    
    U->>F: 提交租户申请
    F->>B: POST /api/tenant/auth-applications
    B->>D: 保存申请数据
    D-->>B: 返回申请ID
    B-->>F: 申请提交成功
    F-->>U: 显示成功消息
    
    Note over F,B: 管理员审批
    U->>F: 查看申请列表
    F->>B: GET /api/tenant/auth-applications
    B->>D: 查询申请列表
    D-->>B: 返回申请数据
    B-->>F: 申请列表数据
    F-->>U: 显示申请列表
    
    U->>F: 审批申请
    F->>B: PUT /api/tenant/auth-applications/{id}/approve
    B->>D: 更新申请状态
    D-->>B: 更新成功
    B-->>F: 审批完成
    F-->>U: 显示审批结果
```

### 2. API服务架构

```typescript
// 服务层封装示例
export class TenantService {
  static async submitAuthApplication(data: AuthApplicationRequest) {
    return await httpClient.post('/tenant/auth-applications', data);
  }
  
  static async getAuthApplications(params: AuthApplicationListParams) {
    return await httpClient.get('/tenant/auth-applications', { params });
  }
  
  static async approveAuthApplication(id: string, data: ApprovalRequest) {
    return await httpClient.put(`/tenant/auth-applications/${id}/approve`, data);
  }
}
```

### 3. 统计分析功能

- **📊 日度统计** - 租户操作次数、服务使用情况
- **📈 月度趋势** - 同比环比分析、增长趋势
- **📋 操作日志** - 详细的用户操作记录
- **🏆 排行榜** - 操作类型排名、租户活跃度排名

## 🚀 部署方案

### 支持的部署方式

| 部署方式 | 适用场景 | 优势 |
|---------|---------|------|
| Docker Compose | 开发/测试环境 | 快速启动、环境一致 |
| Kubernetes | 生产环境 | 高可用、自动扩缩容 |
| 手动部署 | 小型项目 | 简单直接、资源占用小 |

### 环境配置

| 环境 | 前端端口 | 后端端口 | 数据库端口 | 说明 |
|-----|---------|---------|-----------|------|
| 开发环境 | 3001 | 5000 | 3306 | 本地开发 |
| 测试环境 | 3001 | 5001 | 3307 | 自动化测试 |
| 生产环境 | 80/443 | 5000 | 3306 | 生产部署 |

### 一键部署命令

```bash
# 开发环境
./deploy.sh dev --build

# 测试环境  
./deploy.sh test --clean

# 生产环境
./deploy.sh prod --build
```

## 📊 API文档

### 核心API端点

| 功能模块 | 端点 | 方法 | 说明 |
|---------|------|------|------|
| 认证 | `/api/auth/login` | POST | 用户登录 |
| 租户申请 | `/api/tenant/auth-applications` | POST | 提交申请 |
| 申请列表 | `/api/tenant/auth-applications` | GET | 获取申请列表 |
| 申请审批 | `/api/tenant/auth-applications/{id}/approve` | PUT | 审批申请 |
| 日度统计 | `/api/operation/daily-stats` | GET | 日度统计数据 |
| 操作日志 | `/api/operation/logs` | GET | 操作日志列表 |

### API认证

```bash
# 获取访问令牌
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'

# 使用令牌访问API
curl -X GET http://localhost:5000/api/tenant/auth-applications \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🔍 故障排除

### 常见问题

<details>
<summary>数据库连接失败</summary>

**问题**: 应用无法连接到数据库

**解决方案**:
```bash
# 检查MySQL服务状态
docker ps | grep mysql

# 查看数据库日志
docker logs mapstudio-mysql

# 重启数据库服务
docker restart mapstudio-mysql
```
</details>

<details>
<summary>前端页面无法加载</summary>

**问题**: 前端页面显示空白或加载失败

**解决方案**:
```bash
# 检查前端服务状态
curl http://localhost:3001

# 查看前端日志
docker logs mapstudio-frontend

# 重启前端服务
docker restart mapstudio-frontend
```
</details>

<details>
<summary>API调用超时</summary>

**问题**: API请求超时或响应缓慢

**解决方案**:
```typescript
// 增加超时时间
const apiConfig = {
  timeout: 30000, // 30秒
  retries: 3
};
```
</details>

### 调试工具

```bash
# 查看所有服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f backend

# 进入容器调试
docker exec -it mapstudio-backend bash

# 检查网络连接
curl -v http://localhost:5000/api/system/health
```

## 🤝 贡献指南

### 开发流程

1. **Fork 项目** 并创建你的分支
2. **安装依赖** 并配置开发环境
3. **编写代码** 并添加相应测试
4. **运行测试** 确保所有测试通过
5. **提交 PR** 并描述你的更改

### 代码规范

- **前端**: 使用 ESLint + Prettier
- **后端**: 遵循 .NET 编码规范
- **提交**: 使用 Conventional Commits 规范

### 分支策略

- `main` - 生产环境代码
- `develop` - 开发环境代码  
- `feature/*` - 功能分支
- `hotfix/*` - 热修复分支

## 📄 许可证

本项目使用 [MIT License](LICENSE) 许可证。

## 📞 支持与帮助

### 技术支持
- 📧 邮箱: <EMAIL>
- 💬 讨论区: [GitHub Discussions](https://github.com/your-org/map-studio/discussions)
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-org/map-studio/issues)

### 文档资源
- 📖 [测试指南](TESTING_GUIDE.md)
- 🚀 [部署指南](DEPLOYMENT_GUIDE.md)
- 📋 [API文档](http://localhost:5000/swagger)

### 社区
- 💼 [官方网站](https://mapstudio.com)
- 📱 微信群: 添加微信号 `mapstudio-helper`
- 🎯 QQ群: 123456789

## 🎉 致谢

感谢所有为 Map Studio 项目做出贡献的开发者和用户！

### 核心贡献者
- [@developer1](https://github.com/developer1) - 项目架构设计
- [@developer2](https://github.com/developer2) - 前端开发
- [@developer3](https://github.com/developer3) - 后端开发

### 技术支持
- Vue.js 团队提供的优秀前端框架
- Microsoft 提供的 .NET 平台
- 开源社区的各种优秀组件

---

<div align="center">

**🚀 Map Studio - 让前后端联调测试更简单！**

[⭐ 给项目点个星](https://github.com/your-org/map-studio) | [📖 查看文档](TESTING_GUIDE.md) | [🚀 快速部署](DEPLOYMENT_GUIDE.md)

</div>

---

*最后更新: 2025年8月25日*