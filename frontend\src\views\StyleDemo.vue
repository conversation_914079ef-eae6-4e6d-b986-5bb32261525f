<template>
  <div class="container-fluid section-padding">
    <div class="max-w-6xl mx-auto">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">样式统一演示页面</h1>
        <p class="text-gray-600">展示Vue版本与React版本的样式一致性</p>
      </div>

      <!-- 按钮组件演示 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-900 mb-6">按钮组件</h2>
        <div class="grid-responsive">
          <div class="card">
            <div class="card-header">
              <h3 class="text-lg font-medium">按钮变体</h3>
            </div>
            <div class="card-body">
              <div class="flex-responsive">
                <BaseButton variant="primary">主要按钮</BaseButton>
                <BaseButton variant="secondary">次要按钮</BaseButton>
                <BaseButton variant="outline">边框按钮</BaseButton>
                <BaseButton variant="ghost">幽灵按钮</BaseButton>
                <BaseButton variant="danger">危险按钮</BaseButton>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="text-lg font-medium">按钮尺寸</h3>
            </div>
            <div class="card-body">
              <div class="flex-responsive">
                <BaseButton size="sm">小按钮</BaseButton>
                <BaseButton size="md">中按钮</BaseButton>
                <BaseButton size="lg">大按钮</BaseButton>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="text-lg font-medium">按钮状态</h3>
            </div>
            <div class="card-body">
              <div class="flex-responsive">
                <BaseButton icon="fa-plus">带图标</BaseButton>
                <BaseButton :loading="true">加载中</BaseButton>
                <BaseButton :disabled="true">禁用状态</BaseButton>
                <BaseButton full-width>全宽按钮</BaseButton>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 输入框组件演示 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-900 mb-6">输入框组件</h2>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="card">
            <div class="card-header">
              <h3 class="text-lg font-medium">基础输入框</h3>
            </div>
            <div class="card-body space-y-4">
              <BaseInput
                v-model="demoForm.name"
                label="姓名"
                placeholder="请输入姓名"
                required
              />
              <BaseInput
                v-model="demoForm.email"
                type="email"
                label="邮箱"
                placeholder="请输入邮箱地址"
                prefix-icon="fa-envelope"
              />
              <BaseInput
                v-model="demoForm.password"
                type="password"
                label="密码"
                placeholder="请输入密码"
                suffix-icon="fa-eye"
              />
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="text-lg font-medium">输入框状态</h3>
            </div>
            <div class="card-body space-y-4">
              <BaseInput
                v-model="demoForm.search"
                type="search"
                label="搜索"
                placeholder="搜索内容..."
                prefix-icon="fa-search"
                clearable
              />
              <BaseInput
                v-model="demoForm.error"
                label="错误状态"
                placeholder="输入内容"
                error="这是一个错误提示"
              />
              <BaseInput
                v-model="demoForm.disabled"
                label="禁用状态"
                placeholder="禁用的输入框"
                disabled
              />
            </div>
          </div>
        </div>
      </section>

      <!-- 卡片和布局演示 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-900 mb-6">卡片和布局</h2>
        <div class="grid-responsive">
          <div class="card">
            <div class="card-header">
              <h3 class="text-lg font-medium">基础卡片</h3>
            </div>
            <div class="card-body">
              <p class="text-gray-600">这是一个基础卡片的内容区域。</p>
            </div>
            <div class="card-footer">
              <BaseButton size="sm">操作</BaseButton>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="text-lg font-medium">统计卡片</h3>
            </div>
            <div class="card-body">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-2xl font-bold text-blue-600">1,234</p>
                  <p class="text-sm text-gray-500">总访问量</p>
                </div>
                <div class="text-blue-500">
                  <i class="fa-solid fa-chart-line text-2xl"></i>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="text-lg font-medium">进度卡片</h3>
            </div>
            <div class="card-body">
              <div class="mb-2">
                <div class="flex justify-between text-sm">
                  <span>完成进度</span>
                  <span>75%</span>
                </div>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full" style="width: 75%"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 标签和状态演示 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-900 mb-6">标签和状态</h2>
        <div class="card">
          <div class="card-header">
            <h3 class="text-lg font-medium">状态标签</h3>
          </div>
          <div class="card-body">
            <div class="flex flex-wrap gap-3">
              <span class="badge-primary">主要</span>
              <span class="badge-success">成功</span>
              <span class="badge-warning">警告</span>
              <span class="badge-danger">危险</span>
              <span class="badge-gray">默认</span>
            </div>
          </div>
        </div>
      </section>

      <!-- 表格演示 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-900 mb-6">表格组件</h2>
        <div class="card">
          <div class="card-header">
            <h3 class="text-lg font-medium">数据表格</h3>
          </div>
          <div class="card-body p-0">
            <div class="overflow-x-auto">
              <table class="table">
                <thead class="table-header">
                  <tr>
                    <th class="table-header-cell">姓名</th>
                    <th class="table-header-cell">邮箱</th>
                    <th class="table-header-cell">状态</th>
                    <th class="table-header-cell">操作</th>
                  </tr>
                </thead>
                <tbody class="table-body">
                  <tr class="table-row" v-for="user in demoUsers" :key="user.id">
                    <td class="table-cell font-medium">{{ user.name }}</td>
                    <td class="table-cell text-gray-500">{{ user.email }}</td>
                    <td class="table-cell">
                      <span :class="user.status === '活跃' ? 'badge-success' : 'badge-gray'">
                        {{ user.status }}
                      </span>
                    </td>
                    <td class="table-cell">
                      <BaseButton size="sm" variant="outline">编辑</BaseButton>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      <!-- 图表演示 */
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-900 mb-6">图表组件</h2>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="chart-wrapper">
            <h3 class="chart-title">访问趋势</h3>
            <p class="chart-subtitle">最近7天的访问数据</p>
            <ChartBase
              :option="lineChartOption"
              class="chart-container"
            />
          </div>

          <div class="chart-wrapper">
            <h3 class="chart-title">用户分布</h3>
            <p class="chart-subtitle">按地区统计的用户分布</p>
            <ChartBase
              :option="pieChartOption"
              class="chart-container"
            />
          </div>
        </div>
      </section>

      <!-- 响应式演示 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-900 mb-6">响应式布局</h2>
        <div class="alert-info mb-6">
          <i class="fa-solid fa-info-circle mr-2"></i>
          调整浏览器窗口大小查看响应式效果
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <div class="card" v-for="i in 8" :key="i">
            <div class="card-body text-center">
              <div class="text-2xl text-blue-500 mb-2">
                <i class="fa-solid fa-cube"></i>
              </div>
              <h4 class="font-medium">响应式卡片 {{ i }}</h4>
              <p class="text-sm text-gray-500 mt-1">自适应布局</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import BaseButton from '@/components/BaseButton.vue';
import BaseInput from '@/components/BaseInput.vue';
import ChartBase from '@/components/ChartBase.vue';

// 演示表单数据
const demoForm = ref({
  name: '',
  email: '',
  password: '',
  search: '',
  error: '错误的输入',
  disabled: '禁用的值'
});

// 演示用户数据
const demoUsers = ref([
  { id: 1, name: '张三', email: '<EMAIL>', status: '活跃' },
  { id: 2, name: '李四', email: '<EMAIL>', status: '活跃' },
  { id: 3, name: '王五', email: '<EMAIL>', status: '非活跃' },
  { id: 4, name: '赵六', email: '<EMAIL>', status: '活跃' },
]);

// 折线图配置
const lineChartOption = ref({
  title: {
    text: '',
    left: 'center',
    textStyle: {
      fontSize: 16,
      fontWeight: 'normal'
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    axisLine: {
      lineStyle: {
        color: '#e5e7eb'
      }
    },
    axisLabel: {
      color: '#6b7280'
    }
  },
  yAxis: {
    type: 'value',
    axisLine: {
      lineStyle: {
        color: '#e5e7eb'
      }
    },
    axisLabel: {
      color: '#6b7280'
    },
    splitLine: {
      lineStyle: {
        color: '#f3f4f6'
      }
    }
  },
  series: [
    {
      name: '访问量',
      type: 'line',
      data: [820, 932, 901, 934, 1290, 1330, 1320],
      smooth: true,
      lineStyle: {
        color: '#3b82f6',
        width: 3
      },
      itemStyle: {
        color: '#3b82f6'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
            { offset: 1, color: 'rgba(59, 130, 246, 0.05)' }
          ]
        }
      }
    }
  ]
});

// 饼图配置
const pieChartOption = ref({
  title: {
    text: '',
    left: 'center',
    textStyle: {
      fontSize: 16,
      fontWeight: 'normal'
    }
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'horizontal',
    bottom: '0%',
    textStyle: {
      color: '#6b7280'
    }
  },
  series: [
    {
      name: '用户分布',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '45%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '18',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: [
        { value: 335, name: '北京', itemStyle: { color: '#3b82f6' } },
        { value: 310, name: '上海', itemStyle: { color: '#10b981' } },
        { value: 234, name: '广州', itemStyle: { color: '#f59e0b' } },
        { value: 135, name: '深圳', itemStyle: { color: '#ef4444' } },
        { value: 148, name: '其他', itemStyle: { color: '#6b7280' } }
      ]
    }
  ]
});
</script>

<style scoped>
/* 组件特定样式 */
.demo-section {
  @apply mb-12;
}

.demo-title {
  @apply text-2xl font-semibold text-gray-900 mb-6;
}

.demo-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

/* 响应式网格演示 */
@media (max-width: 640px) {
  .grid-responsive {
    @apply grid-cols-1;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .grid-responsive {
    @apply grid-cols-2;
  }
}

@media (min-width: 1025px) {
  .grid-responsive {
    @apply grid-cols-3;
  }
}
</style>