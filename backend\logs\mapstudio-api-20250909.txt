2025-09-09 11:16:41.331 +08:00 [INF] 开始初始化数据库...
2025-09-09 11:16:47.083 +08:00 [INF] 数据库表创建完成
2025-09-09 11:16:47.578 +08:00 [INF] 数据库初始化完成
2025-09-09 11:16:47.612 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-09 11:16:47.841 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-09 11:16:47.842 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-09 11:16:47.901 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-09 11:16:47.903 +08:00 [INF] Hosting environment: Development
2025-09-09 11:16:47.903 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-09 11:25:40.642 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-09 11:25:40.722 +08:00 [INF] CORS policy execution successful.
2025-09-09 11:25:40.728 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 91.0414ms
2025-09-09 11:25:40.733 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-09 11:25:40.753 +08:00 [INF] CORS policy execution successful.
2025-09-09 11:25:40.758 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-09 11:25:40.781 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-09 11:25:41.119 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-09 11:25:41.144 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 357.9007ms
2025-09-09 11:25:41.148 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-09 11:25:41.400 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 667.1694ms
2025-09-09 11:25:47.478 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - null null
2025-09-09 11:25:47.483 +08:00 [INF] CORS policy execution successful.
2025-09-09 11:25:47.484 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - 204 null null 5.3062ms
2025-09-09 11:25:47.486 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - null null
2025-09-09 11:25:47.489 +08:00 [INF] CORS policy execution successful.
2025-09-09 11:25:47.490 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api)'
2025-09-09 11:25:47.502 +08:00 [INF] Route matched with {action = "GetAuthApplications", controller = "Tenant"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.AuthApplicationListResponse]]] GetAuthApplications(Int32, Int32, System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.TenantController (MapStudio.Api).
2025-09-09 11:25:47.854 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.AuthApplicationListResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-09 11:25:47.861 +08:00 [INF] Executed action MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api) in 357.3975ms
2025-09-09 11:25:47.862 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api)'
2025-09-09 11:25:47.998 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - 200 null application/json; charset=utf-8 512.5873ms
2025-09-09 11:25:52.815 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - null null
2025-09-09 11:25:52.819 +08:00 [INF] CORS policy execution successful.
2025-09-09 11:25:52.820 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - 204 null null 4.8113ms
2025-09-09 11:25:52.822 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - null null
2025-09-09 11:25:52.827 +08:00 [INF] CORS policy execution successful.
2025-09-09 11:25:52.828 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api)'
2025-09-09 11:25:52.829 +08:00 [INF] Route matched with {action = "GetAuthApplications", controller = "Tenant"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.AuthApplicationListResponse]]] GetAuthApplications(Int32, Int32, System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.TenantController (MapStudio.Api).
2025-09-09 11:25:53.050 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.AuthApplicationListResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-09 11:25:53.051 +08:00 [INF] Executed action MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api) in 220.2274ms
2025-09-09 11:25:53.052 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api)'
2025-09-09 11:25:53.157 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - 200 null application/json; charset=utf-8 335.0291ms
2025-09-09 11:34:43.161 +08:00 [INF] 开始初始化数据库...
2025-09-09 11:34:48.418 +08:00 [INF] 数据库表创建完成
2025-09-09 11:34:48.842 +08:00 [INF] 数据库初始化完成
2025-09-09 11:34:48.861 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-09 11:34:49.056 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-09 11:34:49.057 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-09 11:34:49.109 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-09 11:34:49.111 +08:00 [INF] Hosting environment: Development
2025-09-09 11:34:49.112 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-09 11:35:40.495 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-09 11:35:40.551 +08:00 [INF] CORS policy execution successful.
2025-09-09 11:35:40.556 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 63.1326ms
2025-09-09 11:35:40.560 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-09 11:35:40.572 +08:00 [INF] CORS policy execution successful.
2025-09-09 11:35:40.576 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-09 11:35:40.591 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-09 11:35:40.725 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-09 11:35:40.742 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 146.6353ms
2025-09-09 11:35:40.745 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-09 11:35:40.933 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 373.4047ms
2025-09-09 11:36:59.317 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - null null
2025-09-09 11:36:59.321 +08:00 [INF] CORS policy execution successful.
2025-09-09 11:36:59.322 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - 204 null null 27.992ms
2025-09-09 11:36:59.323 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - null null
2025-09-09 11:36:59.326 +08:00 [INF] CORS policy execution successful.
2025-09-09 11:36:59.326 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api)'
2025-09-09 11:36:59.338 +08:00 [INF] Route matched with {action = "GetAuthApplications", controller = "Tenant"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.AuthApplicationListResponse]]] GetAuthApplications(Int32, Int32, System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.TenantController (MapStudio.Api).
2025-09-09 11:36:59.636 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.AuthApplicationListResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-09 11:36:59.642 +08:00 [INF] Executed action MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api) in 301.7629ms
2025-09-09 11:36:59.643 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.TenantController.GetAuthApplications (MapStudio.Api)'
2025-09-09 11:36:59.752 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Tenant/auth-applications?page=1&pageSize=10&status=pending - 200 null application/json; charset=utf-8 428.7758ms
2025-09-09 11:37:45.184 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-09 11:37:45.193 +08:00 [INF] CORS policy execution successful.
2025-09-09 11:37:45.194 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 9.4404ms
2025-09-09 11:37:45.195 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-09 11:37:45.199 +08:00 [INF] CORS policy execution successful.
2025-09-09 11:37:45.200 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-09 11:37:45.201 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-09 11:37:45.325 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-09 11:37:45.328 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 124.9619ms
2025-09-09 11:37:45.330 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-09 11:37:45.503 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 307.8309ms
2025-09-09 14:23:04.844 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-09 14:23:04.860 +08:00 [INF] CORS policy execution failed.
2025-09-09 14:23:04.862 +08:00 [INF] Request origin http://localhost:3003 does not have permission to access the resource.
2025-09-09 14:23:04.864 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 22.1209ms
2025-09-09 14:23:05.873 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-09 14:23:05.875 +08:00 [INF] CORS policy execution failed.
2025-09-09 14:23:05.876 +08:00 [INF] Request origin http://localhost:3003 does not have permission to access the resource.
2025-09-09 14:23:05.879 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 5.8306ms
2025-09-09 14:23:07.887 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-09 14:23:07.889 +08:00 [INF] CORS policy execution failed.
2025-09-09 14:23:07.889 +08:00 [INF] Request origin http://localhost:3003 does not have permission to access the resource.
2025-09-09 14:23:07.890 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 3.1006ms
2025-09-09 14:23:11.897 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-09 14:23:11.899 +08:00 [INF] CORS policy execution failed.
2025-09-09 14:23:11.899 +08:00 [INF] Request origin http://localhost:3003 does not have permission to access the resource.
2025-09-09 14:23:11.901 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 3.7554ms
2025-09-09 15:22:51.034 +08:00 [INF] 开始初始化数据库...
2025-09-09 15:22:57.435 +08:00 [INF] 数据库表创建完成
2025-09-09 15:22:58.664 +08:00 [INF] 数据库初始化完成
2025-09-09 15:22:58.696 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-09 15:22:59.010 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-09 15:22:59.011 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-09 15:22:59.078 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-09 15:22:59.080 +08:00 [INF] Hosting environment: Development
2025-09-09 15:22:59.081 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-09 17:45:23.215 +08:00 [INF] 开始初始化数据库...
2025-09-09 17:45:29.353 +08:00 [INF] 数据库表创建完成
2025-09-09 17:45:29.818 +08:00 [INF] 数据库初始化完成
2025-09-09 17:45:29.840 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-09 17:45:30.070 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-09 17:45:30.071 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-09 17:45:30.142 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-09 17:45:30.144 +08:00 [INF] Hosting environment: Development
2025-09-09 17:45:30.145 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-09 17:46:39.730 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-09 17:46:39.833 +08:00 [INF] CORS policy execution failed.
2025-09-09 17:46:39.835 +08:00 [INF] Request origin http://localhost:3004 does not have permission to access the resource.
2025-09-09 17:46:39.844 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 117.1562ms
2025-09-09 17:46:40.930 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-09 17:46:40.937 +08:00 [INF] CORS policy execution failed.
2025-09-09 17:46:40.939 +08:00 [INF] Request origin http://localhost:3004 does not have permission to access the resource.
2025-09-09 17:46:40.941 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 11.1967ms
2025-09-09 17:46:42.039 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-09 17:46:42.041 +08:00 [INF] CORS policy execution failed.
2025-09-09 17:46:42.042 +08:00 [INF] Request origin http://localhost:3004 does not have permission to access the resource.
2025-09-09 17:46:42.043 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 4.6168ms
2025-09-09 17:46:44.047 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-09 17:46:44.050 +08:00 [INF] CORS policy execution failed.
2025-09-09 17:46:44.050 +08:00 [INF] Request origin http://localhost:3004 does not have permission to access the resource.
2025-09-09 17:46:44.051 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 3.9609ms
2025-09-09 17:46:48.918 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-09 17:46:48.921 +08:00 [INF] CORS policy execution failed.
2025-09-09 17:46:48.922 +08:00 [INF] Request origin http://localhost:3004 does not have permission to access the resource.
2025-09-09 17:46:48.923 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 4.8133ms
2025-09-09 17:53:18.711 +08:00 [INF] 开始初始化数据库...
2025-09-09 17:53:25.031 +08:00 [INF] 数据库表创建完成
2025-09-09 17:53:25.529 +08:00 [INF] 数据库初始化完成
2025-09-09 17:53:25.560 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-09 17:53:25.802 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-09 17:53:25.803 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-09 17:53:25.872 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-09 17:53:25.873 +08:00 [INF] Hosting environment: Development
2025-09-09 17:53:25.874 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-09 18:12:44.965 +08:00 [INF] 开始初始化数据库...
2025-09-09 18:12:51.767 +08:00 [INF] 数据库表创建完成
2025-09-09 18:12:52.371 +08:00 [INF] 数据库初始化完成
2025-09-09 18:12:52.401 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-09 18:12:52.665 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-09 18:12:52.667 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-09 18:12:52.736 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-09 18:12:52.738 +08:00 [INF] Hosting environment: Development
2025-09-09 18:12:52.739 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
