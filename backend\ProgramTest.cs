using System;
using System.Xml.Serialization;
using System.IO;
using System.Text;
using MapStudio.Api.Models.DTOs;

namespace TestSmsFunction
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("开始测试短信发送功能...");
            
            // 创建测试数据
            var xmlRequest = new XmlSmsRequest
            {
                Type = "待办",
                Modular = "网优平台3.0",
                Function = "ceshi",
                ReceiveMans = "diaowen",
                ReceivePns = "13222222222",
                CcMans = "wsj",
                CcPns = "18842939234",
                SendMan = "admin",
                Message = "ceshi"
            };
            
            // 序列化为XML格式
            string xmlContent;
            using (var stringWriter = new StringWriter())
            {
                var serializer = new XmlSerializer(typeof(XmlSmsRequest));
                serializer.Serialize(stringWriter, xmlRequest);
                xmlContent = stringWriter.ToString();
            }
            
            Console.WriteLine("生成的XML内容:");
            Console.WriteLine(xmlContent);
            
            // 验证XML是否符合预期格式
            string expectedXmlPart = "<Sms>";
            string expectedTypePart = "<type>待办</type>";
            string expectedModularPart = "<modular>网优平台3.0</modular>";
            string expectedFunctionPart = "<function>ceshi</function>";
            
            if (xmlContent.Contains(expectedXmlPart) && 
                xmlContent.Contains(expectedTypePart) && 
                xmlContent.Contains(expectedModularPart) && 
                xmlContent.Contains(expectedFunctionPart))
            {
                Console.WriteLine("✅ XML序列化测试通过!");
            }
            else
            {
                Console.WriteLine("❌ XML序列化测试失败!");
            }
            
            Console.WriteLine("测试完成。");
            Console.ReadKey();
        }
    }
}