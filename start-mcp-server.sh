#!/bin/bash

# MySQL MCP Server 启动脚本
# 
# 使用方法：
# ./start-mcp-server.sh
#
# 或者直接使用：
# uvx mcp-server-mysql --host ************* --port 3307 --username dtauser --password dtauser --database MTNOH_AAA_Platform

echo "正在启动 MySQL MCP Server..."
echo "数据库连接信息:"
echo "  主机: *************"
echo "  端口: 3307"
echo "  数据库: MTNOH_AAA_Platform"
echo "  用户名: dtauser"
echo ""

# 设置环境变量
export MYSQL_HOST="*************"
export MYSQL_PORT="3307"
export MYSQL_USER="dtauser"
export MYSQL_PASSWORD="dtauser"
export MYSQL_DATABASE="MTNOH_AAA_Platform"

# 启动 MCP server
uvx mcp-server-mysql \
  --host "$MYSQL_HOST" \
  --port "$MYSQL_PORT" \
  --username "$MYSQL_USER" \
  --password "$MYSQL_PASSWORD" \
  --database "$MYSQL_DATABASE"