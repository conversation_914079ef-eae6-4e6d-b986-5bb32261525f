using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MapStudio.Api.Services.Interfaces;
using MapStudio.Api.Models.DTOs;
using MapStudio.Api.Models.ViewModels;
using MapStudio.Api.Attributes;

namespace MapStudio.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class FilesController : ControllerBase
{
    private readonly IFileService _fileService;
    private readonly ILogger<FilesController> _logger;

    public FilesController(IFileService fileService, ILogger<FilesController> logger)
    {
        _fileService = fileService;
        _logger = logger;
    }

    /// <summary>
    /// 文件上传
    /// </summary>
    /// <param name="file">文件</param>
    /// <param name="fileType">文件类型</param>
    [HttpPost("upload")]
    public async Task<ActionResult<ApiResponse<FileUploadResponse>>> UploadFile(IFormFile file, [FromForm] string fileType)
    {
        try
        {
            var result = await _fileService.UploadFileAsync(file, fileType);
            return Ok(ApiResponse<FileUploadResponse>.SuccessResult(result, "文件上传成功"));
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ApiResponse<FileUploadResponse>.ErrorResult(ex.Message, "INVALID_FILE"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "文件上传失败");
            return StatusCode(500, ApiResponse<FileUploadResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 文件下载
    /// </summary>
    /// <param name="fileId">文件ID</param>
    [HttpGet("download/{fileId}")]
    public async Task<IActionResult> DownloadFile(string fileId)
    {
        try
        {
            var fileStream = await _fileService.DownloadFileAsync(fileId);
            return File(fileStream, "application/octet-stream");
        }
        catch (FileNotFoundException)
        {
            return NotFound("文件不存在");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "文件下载失败: {FileId}", fileId);
            return StatusCode(500, "服务器内部错误");
        }
    }

    /// <summary>
    /// 文件预览
    /// </summary>
    /// <param name="fileId">文件ID</param>
    [HttpGet("preview/{fileId}")]
    public async Task<ActionResult<ApiResponse<FilePreviewResponse>>> GetFilePreview(string fileId)
    {
        try
        {
            var result = await _fileService.GetFilePreviewAsync(fileId);
            return Ok(ApiResponse<FilePreviewResponse>.SuccessResult(result, "获取文件预览成功"));
        }
        catch (FileNotFoundException)
        {
            return NotFound(ApiResponse<FilePreviewResponse>.ErrorResult("文件不存在", "FILE_NOT_FOUND"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取文件预览失败: {FileId}", fileId);
            return StatusCode(500, ApiResponse<FilePreviewResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 删除文件
    /// </summary>
    /// <param name="fileId">文件ID</param>
    [HttpDelete("{fileId}")]
    [ConditionalAuthorize]
    public async Task<ActionResult<ApiResponse>> DeleteFile(string fileId)
    {
        try
        {
            var success = await _fileService.DeleteFileAsync(fileId);
            if (success)
            {
                return Ok(ApiResponse.SuccessResult("文件删除成功"));
            }
            else
            {
                return NotFound(ApiResponse.ErrorResult("文件不存在", "FILE_NOT_FOUND"));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除文件失败: {FileId}", fileId);
            return StatusCode(500, ApiResponse.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }
}

[ApiController]
[Route("api/[controller]")]
public class MetadataController : ControllerBase
{
    private readonly IMetadataService _metadataService;
    private readonly ILogger<MetadataController> _logger;

    public MetadataController(IMetadataService metadataService, ILogger<MetadataController> logger)
    {
        _metadataService = metadataService;
        _logger = logger;
    }

    /// <summary>
    /// 获取服务类型列表
    /// </summary>
    [HttpGet("service-types")]
    public async Task<ActionResult<ApiResponse<List<ServiceTypeOption>>>> GetServiceTypes()
    {
        try
        {
            var result = await _metadataService.GetServiceTypesAsync();
            return Ok(ApiResponse<List<ServiceTypeOption>>.SuccessResult(result, "获取服务类型成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取服务类型失败");
            return StatusCode(500, ApiResponse<List<ServiceTypeOption>>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取权限范围列表
    /// </summary>
    [HttpGet("permission-scopes")]
    public async Task<ActionResult<ApiResponse<List<PermissionScopeOption>>>> GetPermissionScopes()
    {
        try
        {
            var result = await _metadataService.GetPermissionScopesAsync();
            return Ok(ApiResponse<List<PermissionScopeOption>>.SuccessResult(result, "获取权限范围成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取权限范围失败");
            return StatusCode(500, ApiResponse<List<PermissionScopeOption>>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取授权期限列表
    /// </summary>
    [HttpGet("auth-periods")]
    public async Task<ActionResult<ApiResponse<List<AuthPeriodOption>>>> GetAuthPeriods()
    {
        try
        {
            var result = await _metadataService.GetAuthPeriodsAsync();
            return Ok(ApiResponse<List<AuthPeriodOption>>.SuccessResult(result, "获取授权期限成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取授权期限失败");
            return StatusCode(500, ApiResponse<List<AuthPeriodOption>>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取租户规模列表
    /// </summary>
    [HttpGet("tenant-sizes")]
    public async Task<ActionResult<ApiResponse<List<TenantSizeOption>>>> GetTenantSizes()
    {
        try
        {
            var result = await _metadataService.GetTenantSizesAsync();
            return Ok(ApiResponse<List<TenantSizeOption>>.SuccessResult(result, "获取租户规模成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取租户规模失败");
            return StatusCode(500, ApiResponse<List<TenantSizeOption>>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取行业列表
    /// </summary>
    [HttpGet("industries")]
    public async Task<ActionResult<ApiResponse<List<IndustryOption>>>> GetIndustries()
    {
        try
        {
            var result = await _metadataService.GetIndustriesAsync();
            return Ok(ApiResponse<List<IndustryOption>>.SuccessResult(result, "获取行业列表成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取行业列表失败");
            return StatusCode(500, ApiResponse<List<IndustryOption>>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
        }
    }
}