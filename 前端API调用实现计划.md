# 前端API调用实现计划：服务状态统计

## 概述
本文档描述了如何在前端实现服务状态统计的API调用，包括类型定义、端点配置和服务方法的创建。

## 实现步骤

### 1. 添加类型定义
在`frontend/src/services/types.ts`中添加服务状态统计相关的类型定义：

```typescript
// =============================================================================
// 服务状态统计相关类型
// =============================================================================

export interface ServiceStatusStatisticsResponse {
  /**
   * 总用户数
   */
  totalUsers: number;
  
  /**
   * 活跃用户数
   */
  activeUsers: number;
  
  /**
   * 今日访问量
   */
  todayVisits: number;
  
  /**
   * 待审批申请数
   */
  pendingApplications: number;
}

export interface ServiceStatusStatisticsApiResponse {
  success: boolean;
  data: ServiceStatusStatisticsResponse;
  message: string;
}
```

### 2. 添加端点配置
在`frontend/src/config/endpoints.ts`中添加新的API端点：

```typescript
// 租户管理
TENANT: {
  // 授权申请相关
  AUTH_APPLICATIONS: '/Tenant/auth-applications',
  AUTH_APPLICATION_DETAIL: (id: string) => `/Tenant/auth-applications/${id}`,
  APPROVE_APPLICATION: (id: string) => `/Tenant/auth-applications/${id}/approve`,
  
  // 服务状态
  SERVICE_STATUS: '/Tenant/service-status',
  SERVICE_STATUS_STATISTICS: '/Tenant/service-status-statistics', // 新增
  
  // 授权销毁
  DESTROY_AUTH: (id: string) => `/Tenant/auth-applications/${id}/destroy`,
  
  // 授权进度查询
  AUTH_PROGRESS: `/Tenant/auth-progress`,
  AUTH_PROGRESS_DETAIL: (id: string) =>`/Tenant/auth-progress/${id}`,
}
```

### 3. 创建服务方法
创建`frontend/src/services/tenantService.ts`文件（如果不存在）或在现有文件中添加新的服务方法：

```typescript
import { API_ENDPOINTS } from '@/config/endpoints'
import { 
  ServiceStatusStatisticsApiResponse 
} from '@/services/types'
import { apiClient } from '@/utils/apiClient'

/**
 * 获取服务状态统计数据
 */
export const getServiceStatusStatistics = async (): Promise<ServiceStatusStatisticsApiResponse> => {
  const response = await apiClient.get<ServiceStatusStatisticsApiResponse>(
    API_ENDPOINTS.TENANT.SERVICE_STATUS_STATISTICS
  )
  return response.data
}
```

### 4. 更新导入和导出
确保新添加的服务方法在适当的文件中被导出，以便在组件中使用：

```typescript
// 在tenantService.ts文件末尾添加
export * from './tenantService'
```

或者在现有的导出文件中添加：

```typescript
// 在适当的index.ts或services.ts文件中
export * from './tenantService'
```

### 5. 错误处理
在API调用中添加适当的错误处理：

```typescript
/**
 * 获取服务状态统计数据
 */
export const getServiceStatusStatistics = async (): Promise<ServiceStatusStatisticsApiResponse> => {
  try {
    const response = await apiClient.get<ServiceStatusStatisticsApiResponse>(
      API_ENDPOINTS.TENANT.SERVICE_STATUS_STATISTICS
    )
    return response.data
  } catch (error) {
    console.error('获取服务状态统计数据失败:', error)
    // 返回默认值或抛出错误
    throw error
  }
}
```

### 6. TypeScript类型检查
确保所有类型定义都正确无误，并且与后端DTO保持一致：

- 字段名称匹配
- 数据类型匹配
- 可选字段正确标记

### 7. API客户端配置
确保`apiClient`已经正确配置：
- 基础URL设置正确
- 认证token自动添加
- 错误处理机制
- 超时设置

## 使用示例

### 在Vue组件中使用
```typescript
import { getServiceStatusStatistics } from '@/services/tenantService'

// 在组件的setup函数或methods中
const loadServiceStatusData = async () => {
  try {
    const response = await getServiceStatusStatistics()
    if (response.success) {
      // 处理返回的数据
      console.log('服务状态统计数据:', response.data)
    }
  } catch (error) {
    console.error('获取数据失败:', error)
  }
}
```

## 测试计划

### 单元测试
为新添加的服务方法编写单元测试：

```typescript
// tenantService.test.ts
import { getServiceStatusStatistics } from '@/services/tenantService'

describe('tenantService', () => {
  describe('getServiceStatusStatistics', () => {
    it('should return service status statistics', async () => {
      // 模拟API响应
      const mockResponse = {
        success: true,
        data: {
          totalUsers: 100,
          activeUsers: 50,
          todayVisits: 25,
          pendingApplications: 5
        },
        message: '获取服务状态统计数据成功'
      }
      
      // 测试逻辑
      // ...
    })
  })
})
```

## 部署注意事项
1. 确保所有类型定义与后端API保持一致
2. 验证API端点URL配置正确
3. 测试认证token的传递
4. 确保错误处理机制正常工作