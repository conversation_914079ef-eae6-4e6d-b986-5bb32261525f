# API端点设计：服务状态统计

## 概述
本文档描述了服务状态统计功能的API端点设计，包括端点URL、请求方法、参数和响应格式。

## 端点详情

### 获取服务状态统计
- **URL**: `/api/Tenant/service-status-statistics`
- **方法**: `GET`
- **描述**: 获取服务状态统计信息，包括总用户数、活跃用户数、今日访问量和待审批申请数

### 请求参数
无请求参数

### 响应格式

#### 成功响应
```json
{
  "success": true,
  "data": {
    "totalUsers": 128,
    "activeUsers": 5732,
    "todayVisits": 16,
    "pendingApplications": 3
  },
  "message": "获取服务状态统计成功"
}
```

#### 错误响应
```json
{
  "success": false,
  "message": "服务器内部错误",
  "code": "INTERNAL_ERROR"
}
```

### 响应字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| success | boolean | 请求是否成功 |
| data | object | 返回的数据 |
| data.totalUsers | integer | 总用户数 |
| data.activeUsers | integer | 活跃用户数 |
| data.todayVisits | integer | 今日访问量 |
| data.pendingApplications | integer | 待审批申请数 |
| message | string | 响应消息 |

## 权限要求
该端点需要认证访问，只有登录用户才能调用。

## 使用示例

### JavaScript/Fetch
```javascript
fetch('/api/Tenant/service-status-statistics', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer <token>',
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => {
  console.log('服务状态统计:', data.data);
})
.catch(error => {
  console.error('获取服务状态统计失败:', error);
});
```

### 前端Vue组件使用
```vue
<script setup>
import { ref, onMounted } from 'vue'
import { getServiceStatusStatistics } from '@/services/tenantService'

const statistics = ref({
  totalUsers: 0,
  activeUsers: 0,
  todayVisits: 0,
  pendingApplications: 0
})

onMounted(async () => {
  try {
    const response = await getServiceStatusStatistics()
    if (response.success) {
      statistics.value = response.data
    }
  } catch (error) {
    console.error('获取服务状态统计失败:', error)
  }
})
</script>
```

## 错误处理
- 500 Internal Server Error: 服务器内部错误
- 401 Unauthorized: 未认证访问
- 403 Forbidden: 权限不足

## 性能考虑
- 响应时间应控制在500ms以内
- 使用数据库索引优化查询性能
- 考虑添加缓存机制以提高响应速度