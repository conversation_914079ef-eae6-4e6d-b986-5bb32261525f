{"format": 1, "restore": {"D:\\路网通项目SVN\\1_后台专题类模块\\map-studio\\backend\\MapStudio.Api.csproj": {}}, "projects": {"D:\\路网通项目SVN\\1_后台专题类模块\\map-studio\\backend\\MapStudio.Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\路网通项目SVN\\1_后台专题类模块\\map-studio\\backend\\MapStudio.Api.csproj", "projectName": "MapStudio.Api", "projectPath": "D:\\路网通项目SVN\\1_后台专题类模块\\map-studio\\backend\\MapStudio.Api.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\路网通项目SVN\\1_后台专题类模块\\map-studio\\backend\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://www.nuget.org/api/v2/": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.AspNetCore.Cors": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.8, )"}, "MySql.Data": {"target": "Package", "version": "[9.1.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.3, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}, "SqlSugar": {"target": "Package", "version": "[5.1.4.156, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[7.1.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.2.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}