import { test, expect } from '@playwright/test';

test.describe('基础功能测试', () => {
  test('应该能正常访问首页', async ({ page }) => {
    // 访问首页
    await page.goto('http://localhost:3001');

    // 检查页面是否加载成功
    await expect(page).toHaveTitle('地图工作室SaaS平台');

    // 等待页面完全加载
    await page.waitForLoadState('networkidle');

    // 检查页面基本结构
    await expect(page.locator('body')).toBeVisible();
    await expect(page.locator('#app')).toBeVisible();
  });

  test('应该显示基本页面内容', async ({ page }) => {
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');

    // 检查是否有任何可见的文本内容
    const bodyText = await page.locator('body').textContent();
    expect(bodyText).toBeTruthy();
    expect(bodyText!.length).toBeGreaterThan(0);
  });

  test('应该没有控制台错误和编译错误', async ({ page }) => {
    const consoleErrors: string[] = [];
    const jsErrors: string[] = [];

    // 监听控制台错误
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    // 监听JavaScript错误（包括模块导入错误）
    page.on('pageerror', error => {
      jsErrors.push(error.message);
    });

    // 监听网络请求错误
    page.on('requestfailed', request => {
      if (request.url().includes('.ts') || request.url().includes('.js')) {
        jsErrors.push(`Failed to load module: ${request.url()}`);
      }
    });

    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');

    // 等待一会儿确保所有异步操作完成
    await page.waitForTimeout(3000);

    // 检查模块导入错误
    const moduleErrors = [...consoleErrors, ...jsErrors].filter(error =>
      error.includes('does not provide an export') ||
      error.includes('SyntaxError') ||
      error.includes('Failed to resolve import') ||
      error.includes('Cannot resolve module')
    );

    if (moduleErrors.length > 0) {
      console.log('Module import errors found:', moduleErrors);
      throw new Error(`Found ${moduleErrors.length} module import errors: ${moduleErrors.join(', ')}`);
    }

    // 检查是否有严重错误（忽略一些常见的开发环境警告）
    const seriousErrors = [...consoleErrors, ...jsErrors].filter(error =>
      !error.includes('Warning') &&
      !error.includes('[DEV]') &&
      !error.includes('favicon') &&
      !error.includes('source map') &&
      !error.includes('HMR') &&
      !error.includes('vite:hmr')
    );

    if (seriousErrors.length > 0) {
      console.log('Serious errors found:', seriousErrors);
    }

    // 允许少量非关键错误
    expect(seriousErrors.length).toBeLessThanOrEqual(2);
  });

  test('应该能进行基本交互', async ({ page }) => {
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');

    // 查找可交互元素
    const buttons = page.locator('button');
    const links = page.locator('a');

    const buttonCount = await buttons.count();
    const linkCount = await links.count();

    // 页面应该有一些交互元素
    expect(buttonCount + linkCount).toBeGreaterThan(0);

    // 如果有按钮，测试第一个是否可见
    if (buttonCount > 0) {
      await expect(buttons.first()).toBeVisible();
    }

    // 如果有链接，测试第一个是否可见
    if (linkCount > 0) {
      await expect(links.first()).toBeVisible();
    }
  });

  test('页面应该是响应式的', async ({ page }) => {
    // 测试桌面端
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
    await expect(page.locator('body')).toBeVisible();

    // 测试移动端
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();
    await page.waitForLoadState('networkidle');
    await expect(page.locator('body')).toBeVisible();

    // 检查页面内容是否适应新的视口
    const bodyBox = await page.locator('body').boundingBox();
    expect(bodyBox?.width).toBeLessThanOrEqual(375);
  });
});
