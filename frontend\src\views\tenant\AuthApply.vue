<template>
  <div class="p-6 space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">租户授权申请</h1>
      <p class="mt-1 text-gray-500">请填写以下信息提交租户授权申请</p>
    </div>

    <!-- 表单卡片 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
      <div class="p-6">
        <form @submit.prevent="handleSubmit" class="space-y-8">
          <!-- 租户基本信息 -->
          <div>
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <i class="fa-solid fa-building-circle-info text-blue-600 mr-2"></i>
              租户基本信息
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="space-y-2">
                <label for="tenantName" class="block text-sm font-medium text-gray-700">
                  租户名称 <span class="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="tenantName"
                  name="tenantName"
                  v-model="formData.tenantName"
                  :class="[
                    'w-full px-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500',
                    errors.tenantName ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'
                  ]"
                />
                <p v-if="errors.tenantName" class="text-sm text-red-500">{{ errors.tenantName }}</p>
              </div>

              <div class="space-y-2">
                <label for="tenantId" class="block text-sm font-medium text-gray-700">
                  租户ID <span class="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="tenantId"
                  name="tenantId"
                  v-model="formData.tenantId"
                  :class="[
                    'w-full px-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500',
                    errors.tenantId ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'
                  ]"
                />
                <p v-if="errors.tenantId" class="text-sm text-red-500">{{ errors.tenantId }}</p>
              </div>

              <div class="space-y-2">
                <label for="contactPerson" class="block text-sm font-medium text-gray-700">
                  联系人 <!-- <span class="text-red-500">*</span>-->
                </label>
                <input
                  type="text"
                  id="contactPerson"
                  name="contactPerson"
                  v-model="formData.contactPerson"
                  :class="[
                    'w-full px-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500',
                    errors.contactPerson ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'
                  ]"
                />
                <p v-if="errors.contactPerson" class="text-sm text-red-500">{{ errors.contactPerson }}</p>
              </div>

              <div class="space-y-2">
                <label for="contactEmail" class="block text-sm font-medium text-gray-700">
                  联系邮箱 <!-- <span class="text-red-500">*</span>-->
                </label>
                <input
                  type="email"
                  id="contactEmail"
                  name="contactEmail"
                  v-model="formData.contactEmail"
                  :class="[
                    'w-full px-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500',
                    errors.contactEmail ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'
                  ]"
                />
                <p v-if="errors.contactEmail" class="text-sm text-red-500">{{ errors.contactEmail }}</p>
              </div>

              <div class="space-y-2">
                <label for="contactPhone" class="block text-sm font-medium text-gray-700">
                  联系电话 <!-- <span class="text-red-500">*</span>-->
                </label>
                <input
                  type="tel"
                  id="contactPhone"
                  name="contactPhone"
                  v-model="formData.contactPhone"
                  :class="[
                    'w-full px-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500',
                    errors.contactPhone ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'
                  ]"
                />
                <p v-if="errors.contactPhone" class="text-sm text-red-500">{{ errors.contactPhone }}</p>
              </div>
            </div>
          </div>

          <!-- 服务申请信息 -->
          <div>
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <i class="fa-solid fa-file-circle-plus text-blue-600 mr-2"></i>
              服务申请信息
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div class="space-y-2">
                <label for="serviceType" class="block text-sm font-medium text-gray-700">
                  权限类型 <span class="text-red-500">*</span>
                </label>
                <select
                  id="serviceType"
                  name="serviceType"
                  v-model="formData.serviceType"
                  :class="[
                    'w-full px-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500',
                    errors.serviceType ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'
                  ]"
                >
                  <option value="">请选择权限类型</option>
                  <option v-for="type in serviceTypes" :key="type.id" :value="type.id">{{ type.name }}</option>
                </select>
                <p v-if="errors.serviceType" class="text-sm text-red-500">{{ errors.serviceType }}</p>
              </div>

              <div class="space-y-2">
                <label for="authPeriod" class="block text-sm font-medium text-gray-700">
                  过期时间 <span class="text-red-500">*</span>
                </label>
                <select
                  id="authPeriod"
                  name="authPeriod"
                  v-model="formData.authPeriod"
                  :class="[
                    'w-full px-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500',
                    errors.authPeriod ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'
                  ]"
                >
                  <option value="">请选择授权期限</option>
                  <option value="3months">3个月</option>
                  <option value="6months">6个月</option>
                  <option value="1year">1年</option>
                  <option value="2years">2年</option>
                  <option value="3years">3年</option>
                </select>
                <p v-if="errors.authPeriod" class="text-sm text-red-500">{{ errors.authPeriod }}</p>
              </div>

              <div class="space-y-2">
                <label for="permissionScope" class="block text-sm font-medium text-gray-700">
                  权限等级 <span class="text-red-500">*</span>
                </label>
                <select
                  id="permissionScope"
                  name="permissionScope"
                  v-model="formData.permissionScope"
                  :class="[
                    'w-full px-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500',
                    errors.permissionScope ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'
                  ]"
                >
                  <option value="">请选择权限等级</option>
                  <option v-for="scope in permissionScopes" :key="scope.id" :value="scope.id">{{ scope.name }}</option>
                </select>
                <p v-if="errors.permissionScope" class="text-sm text-red-500">{{ errors.permissionScope }}</p>
              </div>
            </div>

            <div class="mt-6 space-y-2">
              <label for="description" class="block text-sm font-medium text-gray-700">
                申请描述
              </label>
              <textarea
                id="description"
                name="description"
                rows="4"
                v-model="formData.description"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                placeholder="请简要描述申请用途和需求..."
              ></textarea>
            </div>
          </div>

          <!-- 资质文件上传 -->
          <!--<div>
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <i class="fa-solid fa-file-certificate text-blue-600 mr-2"></i>
              资质文件上传
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="space-y-2">
                <label for="businessLicense" class="block text-sm font-medium text-gray-700">
                  企业营业执照 <span class="text-red-500">*</span>
                </label>
                <div
                  :class="[
                    'border-2 border-dashed rounded-lg p-6 text-center',
                    formData.businessLicense
                      ? 'border-green-300 bg-green-50'
                      : errors.businessLicense
                        ? 'border-red-300 bg-red-50'
                        : 'border-gray-300 hover:border-blue-400 transition-colors'
                  ]"
                >
                  <div v-if="formData.businessLicense" class="space-y-3">
                    <div class="flex justify-center">
                      <i class="fa-solid fa-file-pdf text-green-500 text-4xl"></i>
                    </div>
                    <div>
                      <p class="text-sm font-medium text-gray-900 truncate">{{ formData.businessLicense.name }}</p>
                      <p class="text-xs text-gray-500 mt-1">
                        {{ Math.round(formData.businessLicense.size / 1024) }} KB
                      </p>
                    </div>
                    <div v-if="uploadProgress.businessLicense < 100" class="w-full bg-gray-200 rounded-full h-2">
                      <div
                        class="bg-green-500 h-2 rounded-full"
                        :style="{ width: `${uploadProgress.businessLicense}%` }"
                      ></div>
                    </div>
                  </div>
                  <div v-else class="space-y-2">
                    <i class="fa-solid fa-cloud-upload-alt text-gray-400 text-3xl"></i>
                    <p class="text-sm text-gray-500">点击或拖拽文件至此处上传</p>
                    <p class="text-xs text-gray-400">支持 PDF, JPG, PNG 格式，最大 10MB</p>
                    <input
                      id="businessLicense"
                      name="businessLicense"
                      type="file"
                      accept=".pdf,.jpg,.jpeg,.png"
                      @change="handleFileChange"
                      class="hidden"
                    />
                    <label
                      for="businessLicense"
                      class="inline-block px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 cursor-pointer"
                    >
                      选择文件
                    </label>
                  </div>
                </div>
                <p v-if="errors.businessLicense" class="text-sm text-red-500">{{ errors.businessLicense }}</p>
              </div>

              <div class="space-y-2">
                <label for="organizationCode" class="block text-sm font-medium text-gray-700">
                  组织机构代码证 <span class="text-red-500">*</span>
                </label>
                <div
                  :class="[
                    'border-2 border-dashed rounded-lg p-6 text-center',
                    formData.organizationCode
                      ? 'border-green-300 bg-green-50'
                      : errors.organizationCode
                        ? 'border-red-300 bg-red-50'
                        : 'border-gray-300 hover:border-blue-400 transition-colors'
                  ]"
                >
                  <div v-if="formData.organizationCode" class="space-y-3">
                    <div class="flex justify-center">
                      <i class="fa-solid fa-file-pdf text-green-500 text-4xl"></i>
                    </div>
                    <div>
                      <p class="text-sm font-medium text-gray-900 truncate">{{ formData.organizationCode.name }}</p>
                      <p class="text-xs text-gray-500 mt-1">
                        {{ Math.round(formData.organizationCode.size / 1024) }} KB
                      </p>
                    </div>
                    <div v-if="uploadProgress.organizationCode < 100" class="w-full bg-gray-200 rounded-full h-2">
                      <div
                        class="bg-green-500 h-2 rounded-full"
                        :style="{ width: `${uploadProgress.organizationCode}%` }"
                      ></div>
                    </div>
                  </div>
                  <div v-else class="space-y-2">
                    <i class="fa-solid fa-cloud-upload-alt text-gray-400 text-3xl"></i>
                    <p class="text-sm text-gray-500">点击或拖拽文件至此处上传</p>
                    <p class="text-xs text-gray-400">支持 PDF, JPG, PNG 格式，最大 10MB</p>
                    <input
                      id="organizationCode"
                      name="organizationCode"
                      type="file"
                      accept=".pdf,.jpg,.jpeg,.png"
                      @change="handleFileChange"
                      class="hidden"
                    />
                    <label
                      for="organizationCode"
                      class="inline-block px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 cursor-pointer"
                    >
                      选择文件
                    </label>
                  </div>
                </div>
                <p v-if="errors.organizationCode" class="text-sm text-red-500">{{ errors.organizationCode }}</p>
              </div>
            </div>
          </div>-->

          <!-- 提交按钮 -->
          <div class="flex justify-end space-x-4 pt-4 border-t border-gray-200">
            <!-- <button
              type="button"
              class="px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              保存草稿
            </button> -->
            <button
              type="submit"
              :disabled="isSubmitting"
              class="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <template v-if="isSubmitting">
                <i class="fa-solid fa-spinner fa-spin mr-2"></i>
                提交中...
              </template>
              <template v-else>
                提交申请
              </template>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useToast } from '@/composables/useToast';
import { cn } from '@/lib/utils';

// 定义文件类型接口
interface FormDataType {
  tenantName: string;
  tenantId: string;
  contactPerson: string;
  contactEmail: string;
  contactPhone: string;
  serviceType: string;
  authPeriod: string;
  permissionScope: string;
  description: string;
  businessLicense: File | null;
  organizationCode: File | null;
}

// 服务类型选项
const serviceTypes = [
  { id: 'basic', name: '基础地图服务' },
  { id: 'advanced', name: '高级地图服务' },
  { id: 'enterprise', name: '企业级地图服务' },
  { id: 'custom', name: '定制化地图服务' },
];

// 权限范围选项
const permissionScopes = [
  { id: 'read', name: '只读权限' },
  { id: 'write', name: '读写权限' },
  { id: 'admin', name: '管理员权限' },
];

// 表单状态
const formData = reactive<FormDataType>({
  tenantName: '',
  tenantId: '',
  contactPerson: '',
  contactEmail: '',
  contactPhone: '',
  serviceType: '',
  authPeriod: '',
  permissionScope: '',
  description: '',
  businessLicense: null,
  organizationCode: null,
});

// 错误状态
const errors = reactive<Record<string, string>>({});

// 文件上传状态
const uploadProgress = reactive({
  businessLicense: 0,
  organizationCode: 0,
});

// 表单提交状态
const isSubmitting = ref(false);

// 处理文件上传
const handleFileChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  const name = target.name;
  const files = target.files;

  if (files && files[0]) {
    if (name === 'businessLicense' || name === 'organizationCode') {
      formData[name] = files[0];

      // 模拟文件上传进度
      simulateUploadProgress(name);

      // 清除对应字段的错误
      if (errors[name]) {
        delete errors[name];
      }
    }
  }
};

// 模拟文件上传进度
const simulateUploadProgress = (field: keyof typeof uploadProgress) => {
  let progress = 0;
  const interval = setInterval(() => {
    progress += Math.random() * 20;
    if (progress >= 100) {
      progress = 100;
      clearInterval(interval);
    }
    uploadProgress[field] = progress;
  }, 300);
};

// 表单验证
const validateForm = (): boolean => {
  const newErrors: Record<string, string> = {};

  // 必填字段验证
  if (!formData.tenantName.trim()) newErrors.tenantName = '租户名称不能为空';
  if (!formData.tenantId.trim()) newErrors.tenantId = '租户ID不能为空';
  // if (!formData.contactPerson.trim()) newErrors.contactPerson = '联系人不能为空';
  // if (!formData.contactEmail.trim()) {
  //   newErrors.contactEmail = '联系邮箱不能为空';
  // } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.contactEmail)) {
  //   newErrors.contactEmail = '请输入有效的邮箱地址';
  // }
  // if (!formData.contactPhone.trim()) {
  //   newErrors.contactPhone = '联系电话不能为空';
  // } else if (!/^1[3-9]\d{9}$/.test(formData.contactPhone)) {
  //   newErrors.contactPhone = '请输入有效的手机号码';
  // }
  //if (!formData.serviceType) newErrors.serviceType = '请选择服务类型';
  //if (!formData.authPeriod) newErrors.authPeriod = '请选择授权期限';
  //if (!formData.permissionScope) newErrors.permissionScope = '请选择权限范围';
  //if (!formData.businessLicense) newErrors.businessLicense = '请上传企业营业执照';
  //if (!formData.organizationCode) newErrors.organizationCode = '请上传组织机构代码证';

  // 更新错误状态
  Object.assign(errors, newErrors);
  return Object.keys(newErrors).length === 0;
};

// 处理表单提交
const handleSubmit = async () => {
  // 表单验证
  if (!validateForm()) return;

  // 真实API提交
  isSubmitting.value = true;

  try {
    // 构建申请数据
    const applicationData = {
      tenantName: formData.tenantName,
      tenantId: formData.tenantId,
      contactPerson: formData.contactPerson,
      contactEmail: formData.contactEmail,
      contactPhone: formData.contactPhone,
      serviceType: formData.serviceType,
      authPeriod: formData.authPeriod,
      permissionScope: formData.permissionScope,
      description: formData.description,
      // 文件上传部分暂时传入文件名，后续需要先上传文件获取文件ID
      //businessLicense: formData.businessLicense?.name || '',
      //organizationCode: formData.organizationCode?.name || ''
    };

    console.log('📝 提交租户授权申请...', applicationData);

    // 调用API提交申请
    const { TenantService } = await import('@/services');
    const response = await TenantService.submitAuthApplication(applicationData);

    console.log('✅ 申请提交成功:', response);

    // 显示成功提示
    alert(`租户授权申请提交成功！\n申请ID: ${response.id}\n您的申请已提交至管理员审批，请耐心等待。`);

    // 重置表单
    Object.assign(formData, {
      tenantName: '',
      tenantId: '',
      contactPerson: '',
      contactEmail: '',
      contactPhone: '',
      serviceType: '',
      authPeriod: '',
      permissionScope: '',
      description: '',
      businessLicense: null,
      organizationCode: null,
    });

    Object.assign(uploadProgress, {
      businessLicense: 0,
      organizationCode: 0,
    });

    // 清除所有错误
    Object.keys(errors).forEach(key => delete errors[key]);

  } catch (error: any) {
    console.error('❌ 申请提交失败:', error);
    alert(`申请提交失败：${error.message || '网络错误，请稍后重试'}`);
  } finally {
    isSubmitting.value = false;
  }
};
</script>
