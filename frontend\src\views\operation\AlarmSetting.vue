<template>
  <div class="p-6 space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">租户服务运行异常告警设置</h1>
      <p class="mt-1 text-gray-500">配置服务异常告警短信模板和接收手机号列表</p>
    </div>
    
    <!-- 标签页导航 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
      <div class="border-b border-gray-200">
        <div class="flex">
          <button
            @click="handleTabChange('template')"
            :class="[
              'px-6 py-4 text-sm font-medium border-b-2 hidden',
              activeTab === 'template'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            <i class="fa-solid fa-file-code mr-2"></i>
            告警邮件模板
          </button>
          <button
            @click="handleTabChange('email')"
            :class="[
              'px-6 py-4 text-sm font-medium border-b-2 hidden',
              activeTab === 'email'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            <i class="fa-solid fa-envelope mr-2"></i>
            接收邮箱配置
          </button>
          <button
            @click="handleTabChange('smsTemplate')"
            :class="[
              'px-6 py-4 text-sm font-medium border-b-2',
              activeTab === 'smsTemplate'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            <i class="fa-solid fa-message mr-2"></i>
            告警短信模板
          </button>
          <button
            @click="handleTabChange('sms')"
            :class="[
              'px-6 py-4 text-sm font-medium border-b-2',
              activeTab === 'sms'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            <i class="fa-solid fa-mobile-screen mr-2"></i>
            接收短信配置
          </button>
        </div>
      </div>
      
      <!-- 模板配置内容 -->
      <div v-if="activeTab === 'template'" class="p-6 space-y-6">
        <div>
          <h2 class="text-lg font-semibold text-gray-900 mb-4">邮件模板编辑</h2>
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="lg:col-span-2">
              <div class="space-y-2">
                <label for="emailTemplate" class="block text-sm font-medium text-gray-700">
                  邮件内容
                </label>
                <textarea
                  id="emailTemplate"
                  v-model="emailTemplate"
                  @input="handleTemplateChange"
                  rows="15"
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm font-mono"
                ></textarea>
                <p class="text-xs text-gray-500">
                  使用下方变量按钮插入动态内容，或直接输入 &#123;&#123;tenantId&#125;&#125; 格式的变量
                </p>
              </div>
            </div>
            
            <div>
              <h3 class="text-base font-medium text-gray-900 mb-3">可用变量</h3>
              <div class="space-y-2 border border-gray-200 rounded-lg p-3 max-h-96 overflow-y-auto">
                <button
                  v-for="variable in templateVariables"
                  :key="variable.key"
                  @click="handleVariableButtonClick(variable.key)"
                  class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 rounded-md transition-colors"
                >
                  <div class="flex justify-between items-center">
                    <span class="font-mono text-blue-600">{{ variable.key }}</span>
                    <i class="fa-solid fa-arrow-right text-gray-400"></i>
                  </div>
                  <p class="text-xs text-gray-500 mt-1">{{ variable.description }}</p>
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <div class="flex justify-end pt-4 border-t border-gray-200">
          <button
            type="button"
            :disabled="isSubmitting"
            @click="handleSaveTemplate"
            class="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <template v-if="isSubmitting">
              <i class="fa-solid fa-spinner fa-spin mr-2"></i>
              保存中...
            </template>
            <template v-else>
              <i class="fa-solid fa-save mr-2"></i>
              保存模板
            </template>
          </button>
        </div>
      </div>
      
      <!-- 邮箱配置内容 -->
      <div v-if="activeTab === 'email'" class="p-6 space-y-6">
        <div>
          <h2 class="text-lg font-semibold text-gray-900 mb-4">添加接收邮箱</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label for="newEmail" class="block text-sm font-medium text-gray-700 mb-2">
                邮箱地址
              </label>
              <input
                type="email"
                id="newEmail"
                v-model="newEmail"
                placeholder="请输入邮箱地址"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                接收告警级别
              </label>
              <div class="flex flex-wrap gap-2 p-2 border border-gray-300 rounded-md">
                <label 
                  v-for="level in alarmLevelOptions" 
                  :key="level.id" 
                  class="inline-flex items-center"
                >
                  <input
                    type="checkbox"
                    :value="level.id"
                    v-model="newEmailLevels"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span class="ml-2 text-sm text-gray-700">{{ level.name }}</span>
                </label>
              </div>
            </div>
            
            <div class="flex items-end">
              <button
                type="button"
                @click="handleAddEmail"
                class="w-full px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <i class="fa-solid fa-plus mr-2"></i>
                添加邮箱
              </button>
            </div>
          </div>
        </div>
        
        <div>
          <h2 class="text-lg font-semibold text-gray-900 mb-4">接收邮箱列表</h2>
          <div class="border border-gray-200 rounded-lg overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    邮箱地址
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    接收级别
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <template v-if="emailList.length > 0">
                  <tr 
                    v-for="email in emailList" 
                    :key="email.id" 
                    class="hover:bg-gray-50"
                  >
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ email.email }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex flex-wrap gap-1">
                        <span 
                          v-for="levelId in email.level" 
                          :key="levelId"
                          :class="getLevelBadgeClass(levelId)"
                          class="px-2 py-1 text-xs font-medium rounded-full"
                        >
                          {{ getLevelName(levelId) }}
                        </span>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <label class="inline-flex items-center">
                        <input
                          type="checkbox"
                          :checked="email.enabled"
                          @change="handleEmailToggle(email.id)"
                          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span class="ml-2 text-sm text-gray-700">
                          {{ email.enabled ? '启用' : '禁用' }}
                        </span>
                      </label>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        @click="handleEmailDelete(email.id)"
                        class="text-red-600 hover:text-red-900"
                      >
                        删除
                      </button>
                    </td>
                  </tr>
                </template>
                <template v-else>
                  <tr>
                    <td colspan="4" class="px-6 py-12 text-center">
                      <div class="flex flex-col items-center">
                        <i class="fa-solid fa-envelope-open text-gray-300 text-5xl mb-3"></i>
                        <h3 class="text-lg font-medium text-gray-900">暂无接收邮箱</h3>
                        <p class="mt-1 text-gray-500">请添加接收告警的邮箱地址</p>
                      </div>
                    </td>
                  </tr>
                </template>
              </tbody>
            </table>
          </div>
        </div>
        
        <div class="flex justify-end pt-4 border-t border-gray-200">
          <button
            type="button"
            :disabled="isSubmitting"
            @click="handleSaveEmailList"
            class="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <template v-if="isSubmitting">
              <i class="fa-solid fa-spinner fa-spin mr-2"></i>
              保存中...
            </template>
            <template v-else>
              <i class="fa-solid fa-save mr-2"></i>
              保存邮箱列表
            </template>
          </button>
        </div>
      </div>
      
      <!-- 短信模板配置内容 -->
      <div v-if="activeTab === 'smsTemplate'" class="p-6 space-y-6">
        <div>
          <h2 class="text-lg font-semibold text-gray-900 mb-4">短信模板编辑</h2>
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="lg:col-span-2">
              <div class="space-y-2">
                <label for="smsTemplate" class="block text-sm font-medium text-gray-700">
                  短信内容
                </label>
                <textarea
                  id="smsTemplate"
                  v-model="smsTemplate"
                  @input="handleSmsTemplateChange"
                  rows="8"
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm font-mono"
                ></textarea>
                <p class="text-xs text-gray-500">
                  使用下方变量按钮插入动态内容，或直接输入 &#123;&#123;tenantName&#125;&#125; 格式的变量
                </p>
              </div>
            </div>
            
            <div>
              <h3 class="text-base font-medium text-gray-900 mb-3">可用变量</h3>
              <div class="space-y-2 border border-gray-200 rounded-lg p-3 max-h-96 overflow-y-auto">
                <button
                  v-for="variable in smsTemplateVariables"
                  :key="variable.key"
                  @click="handleSmsVariableButtonClick(variable.key)"
                  class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 rounded-md transition-colors"
                >
                  <div class="flex justify-between items-center">
                    <span class="font-mono text-blue-600">{{ variable.key }}</span>
                    <i class="fa-solid fa-arrow-right text-gray-400"></i>
                  </div>
                  <p class="text-xs text-gray-500 mt-1">{{ variable.description }}</p>
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <div class="flex justify-end pt-4 border-t border-gray-200">
          <button
            type="button"
            :disabled="isSubmitting"
            @click="handleSaveSmsTemplate"
            class="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <template v-if="isSubmitting">
              <i class="fa-solid fa-spinner fa-spin mr-2"></i>
              保存中...
            </template>
            <template v-else>
              <i class="fa-solid fa-save mr-2"></i>
              保存模板
            </template>
          </button>
        </div>
      </div>
      
      <!-- 短信配置内容 -->
      <div v-if="activeTab === 'sms'" class="p-6 space-y-6">
        <div>
          <h2 class="text-lg font-semibold text-gray-900 mb-4">添加接收手机号</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label for="newPhone" class="block text-sm font-medium text-gray-700 mb-2">
                手机号码
              </label>
              <input
                type="tel"
                id="newPhone"
                v-model="newPhone"
                placeholder="请输入手机号码"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                接收告警级别
              </label>
              <div class="flex flex-wrap gap-2 p-2 border border-gray-300 rounded-md">
                <label
                  v-for="level in smsLevelOptions"
                  :key="level.id"
                  class="inline-flex items-center"
                >
                  <input
                    type="checkbox"
                    :value="level.id"
                    v-model="newPhoneLevels"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span class="ml-2 text-sm text-gray-700">{{ level.name }}</span>
                </label>
              </div>
            </div>
            
            <div class="flex items-end">
              <button
                type="button"
                @click="handleAddPhone"
                class="w-full px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <i class="fa-solid fa-plus mr-2"></i>
                添加手机号
              </button>
            </div>
          </div>
        </div>
        
        <div>
          <h2 class="text-lg font-semibold text-gray-900 mb-4">接收手机号列表</h2>
          <div class="border border-gray-200 rounded-lg overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    手机号码
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    接收级别
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <template v-if="phoneList.length > 0">
                  <tr
                    v-for="phone in phoneList"
                    :key="phone.id"
                    class="hover:bg-gray-50"
                  >
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ phone.phone }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex flex-wrap gap-1">
                        <span
                          v-for="levelId in phone.level"
                          :key="levelId"
                          :class="getSmsLevelBadgeClass(levelId)"
                          class="px-2 py-1 text-xs font-medium rounded-full"
                        >
                          {{ getSmsLevelName(levelId) }}
                        </span>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <label class="inline-flex items-center">
                        <input
                          type="checkbox"
                          :checked="phone.enabled"
                          @change="handlePhoneToggle(phone.id, $event)"
                          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span class="ml-2 text-sm text-gray-700">
                          启用
                        </span>
                      </label>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        @click="handlePhoneDelete(phone.id)"
                        class="text-red-600 hover:text-red-900 mr-3"
                      >
                        删除
                      </button>
                      <button
                        v-if="phone.enabled"
                        @click="handleSendSms(phone.phone, phone.level[0] || 'test')"
                        class="text-blue-600 hover:text-blue-900"
                      >
                        发送
                      </button>
                    </td>
                  </tr>
                </template>
                <template v-else>
                  <tr>
                    <td colspan="4" class="px-6 py-12 text-center">
                      <div class="flex flex-col items-center">
                        <i class="fa-solid fa-mobile-screen text-gray-300 text-5xl mb-3"></i>
                        <h3 class="text-lg font-medium text-gray-900">暂无接收手机号</h3>
                        <p class="mt-1 text-gray-500">请添加接收告警的手机号码</p>
                      </div>
                    </td>
                  </tr>
                </template>
              </tbody>
            </table>
          </div>
        </div>
        
        <!-- 保存按钮已隐藏，状态勾选后直接调用接口更新 -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { OperationService } from '@/services/operation.service'

// 告警级别选项 - 与React版本完全一致
const alarmLevelOptions = [
  { id: 'info', name: '信息', description: '一般提示信息，不影响服务运行' },
  { id: 'warning', name: '警告', description: '需要关注的潜在问题，可能影响服务性能' },
  { id: 'error', name: '错误', description: '服务功能异常，但仍可部分使用' },
  { id: 'critical', name: '严重', description: '服务严重故障，无法正常使用' },
]

// 邮件模板变量 - 与React版本完全一致
// 注意：以下变量是邮件模板中的占位符，不是组件的响应式属性
// 这些变量将在后端处理邮件发送时被实际值替换
const templateVariables = [
  { key: '{{tenantId}}', description: '租户ID' },
  { key: '{{tenantName}}', description: '租户名称' },
  { key: '{{serviceName}}', description: '服务名称' },
  { key: '{{errorType}}', description: '错误类型' },
  { key: '{{errorMessage}}', description: '错误信息' },
  { key: '{{occurTime}}', description: '发生时间' },
  { key: '{{recoveryTime}}', description: '恢复时间' },
  { key: '{{duration}}', description: '持续时间' },
  { key: '{{impact}}', description: '影响范围' },
  { key: '{{solution}}', description: '建议解决方案' },
]

// 注意：模板中的 {{tenantId}} 是占位符，不是组件属性
// 它将在后端处理邮件发送时被实际的租户ID替换

// 初始邮件模板 - 与React版本完全一致
const initialTemplate = `【平台告警通知】`

// 初始邮箱列表 - 与React版本完全一致
const initialEmailList = [
  { id: 1, email: '<EMAIL>', level: ['critical', 'error'], enabled: true },
  { id: 2, email: '<EMAIL>', level: ['critical', 'error', 'warning'], enabled: true },
  { id: 3, email: '<EMAIL>', level: ['critical', 'error', 'warning', 'info'], enabled: false },
]

// 短信告警级别选项
const smsLevelOptions = OperationService.getSmsLevelOptions()

// 短信模板变量
const smsTemplateVariables = OperationService.getSmsTemplateVariables()

// 初始短信模板
const initialSmsTemplate = `类型：{{type}}，模组：{{modular}}，功能：{{function}}，接收人：{{receiveMans}}，接收人号码：{{receivePns}}，抄送人：{{ccMans}}，抄送人号码：{{ccPns}}，发送人：{{sendMan}}，发送内容：{{message}}`

// 初始手机号列表（空数组）
const initialPhoneList: any[] = []

// 响应式状态
const activeTab = ref('smsTemplate')
const emailTemplate = ref(initialTemplate)
const emailList = ref([...initialEmailList])
const newEmail = ref('')
const newEmailLevels = ref(['critical', 'error'])
// 短信模板数据
const smsTemplate = ref('')
// 短信模板ID，用于判断是新增还是更新
const smsTemplateId = ref<number | null>(null)
const phoneList = ref([...initialPhoneList])
const newPhone = ref('')
const newPhoneLevels = ref(['system_failure', 'service_exception'])
const isSubmitting = ref(false)

// 获取短信接收手机号列表
const fetchSmsPhones = async () => {
  try {
    const phones = await OperationService.getSmsPhones()
    console.log('获取到的手机号列表:', phones)
    // 转换数据格式以匹配现有phoneList结构
    phoneList.value = phones.map(phone => ({
      id: phone.id,
      phone: phone.phone,
      level: phone.levels,
      enabled: phone.status,
      addedAt: phone.createDate
    }))
    console.log('转换后的手机号列表:', phoneList.value)
  } catch (error) {
    console.error('获取短信接收手机号列表失败:', error)
    // 如果获取失败，使用初始数据
    phoneList.value = [...initialPhoneList]
  }
}

// 获取短信模板数据
const fetchSmsTemplate = async () => {
  try {
    const response = await OperationService.getSmsTemplate()
    console.log('获取到的短信模板:', response)
    
    if (response.success && response.data && response.data.length > 0) {
      // 如果有数据，使用第一条数据
      const template = response.data[0]
      smsTemplate.value = template.templateName
      smsTemplateId.value = template.tempId
    } else {
      // 如果没有数据，使用初始模板
      smsTemplate.value = initialSmsTemplate
      smsTemplateId.value = null
    }
  } catch (error) {
    console.error('获取短信模板失败:', error)
    // 如果获取失败，使用初始模板
    smsTemplate.value = initialSmsTemplate
    smsTemplateId.value = null
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchSmsPhones()
  fetchSmsTemplate()
})

// 切换标签页
const handleTabChange = (tab: string) => {
  activeTab.value = tab
}

// 处理模板变化
const handleTemplateChange = () => {
  // 模板内容已通过v-model自动更新
}

// 插入模板变量
const insertVariable = (variable: string) => {
  const textarea = document.getElementById('emailTemplate') as HTMLTextAreaElement
  if (textarea) {
    const startPos = textarea.selectionStart
    const endPos = textarea.selectionEnd
    
    // 在光标位置插入变量
    emailTemplate.value = emailTemplate.value.substring(0, startPos) + variable + emailTemplate.value.substring(endPos)
    
    // 设置光标位置到插入变量之后
    setTimeout(() => {
      textarea.focus()
      textarea.setSelectionRange(startPos + variable.length, startPos + variable.length)
    }, 0)
  }
}

// 处理变量按钮点击
const handleVariableButtonClick = (variable: string) => {
  insertVariable(variable)
}

// 处理新邮箱添加
const handleAddEmail = () => {
  if (!newEmail.value.trim() || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newEmail.value)) {
    alert('请输入有效的邮箱地址\n邮箱格式不正确，请重新输入')
    return
  }
  
  // 检查邮箱是否已存在
  if (emailList.value.some(item => item.email === newEmail.value)) {
    alert('邮箱已存在\n该邮箱地址已在列表中，请直接编辑')
    return
  }
  
  // 添加新邮箱
  emailList.value.push({
    id: Date.now(),
    email: newEmail.value,
    level: [...newEmailLevels.value],
    enabled: true,
  })
  
  // 清空输入
  newEmail.value = ''
  newEmailLevels.value = ['critical', 'error']
  
  alert('邮箱添加成功\n新邮箱已添加到告警接收列表')
}

// 处理邮箱启用状态切换
const handleEmailToggle = (id: number) => {
  const email = emailList.value.find(item => item.id === id)
  if (email) {
    email.enabled = !email.enabled
  }
}

// 处理邮箱删除
const handleEmailDelete = (id: number) => {
  if (confirm('确定要删除这个邮箱吗？')) {
    emailList.value = emailList.value.filter(item => item.id !== id)
    alert('邮箱已删除\n该邮箱已从告警接收列表中移除')
  }
}

// 处理模板保存
const handleSaveTemplate = () => {
  isSubmitting.value = true
  
  // 模拟保存延迟
  setTimeout(() => {
    isSubmitting.value = false
    alert('邮件模板保存成功\n告警邮件模板已更新并生效')
  }, 800)
}

// 处理邮箱列表保存
const handleSaveEmailList = () => {
  isSubmitting.value = true
  
  // 模拟保存延迟
  setTimeout(() => {
    isSubmitting.value = false
    alert('邮箱列表保存成功\n告警接收邮箱列表已更新并生效')
  }, 800)
}

// 获取级别名称
const getLevelName = (levelId: string) => {
  return alarmLevelOptions.find(level => level.id === levelId)?.name || levelId
}

// 获取级别徽章样式
const getLevelBadgeClass = (levelId: string) => {
  switch (levelId) {
    case 'critical':
      return 'bg-red-100 text-red-800'
    case 'error':
      return 'bg-orange-100 text-orange-800'
    case 'warning':
      return 'bg-yellow-100 text-yellow-800'
    case 'info':
      return 'bg-blue-100 text-blue-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 处理短信模板变化
const handleSmsTemplateChange = () => {
  // 模板内容已通过v-model自动更新
}

// 插入短信模板变量
const insertSmsVariable = (variable: string) => {
  const textarea = document.getElementById('smsTemplate') as HTMLTextAreaElement
  if (textarea) {
    const startPos = textarea.selectionStart
    const endPos = textarea.selectionEnd
    
    // 在光标位置插入变量
    smsTemplate.value = smsTemplate.value.substring(0, startPos) + variable + smsTemplate.value.substring(endPos)
    
    // 设置光标位置到插入变量之后
    setTimeout(() => {
      textarea.focus()
      textarea.setSelectionRange(startPos + variable.length, startPos + variable.length)
    }, 0)
  }
}

// 处理短信变量按钮点击
const handleSmsVariableButtonClick = (variable: string) => {
  insertSmsVariable(variable)
}

// 处理新手机号添加
const handleAddPhone = async () => {
  debugger;
  if (!newPhone.value.trim() || !/^1[3-9]\d{9}$/.test(newPhone.value)) {
    alert('请输入有效的手机号码\n手机号码格式不正确，请重新输入')
    return
  }
  
  // 检查手机号是否已存在
  if (phoneList.value.some(item => item.phone === newPhone.value)) {
    alert('手机号已存在\n该手机号码已在列表中，请直接编辑')
    return
  }
  
  try {
    // 调用API添加新手机号
    await OperationService.addSmsPhone(newPhone.value, [...newPhoneLevels.value])
    
    // 重新获取手机号列表
    await fetchSmsPhones()
    
    // 清空输入
    newPhone.value = ''
    newPhoneLevels.value = ['system_failure', 'service_exception']
    
    alert('手机号添加成功\n新手机号已添加到告警接收列表')
  } catch (error) {
    console.error('添加手机号失败:', error)
    alert('手机号添加失败\n' + (error as Error).message)
  }
}

// 处理手机号启用状态切换
const handlePhoneToggle = async (id: number, event: Event) => {
  debugger;
  const phone = phoneList.value.find(item => item.id === id)
  if (phone) {
    try {
      // 从事件对象中获取复选框的实际状态
      const target = event.target as HTMLInputElement;
      const newStatus = target.checked;
      
      console.log('状态切换 - ID:', id, '原状态:', phone.enabled, '新状态:', newStatus);
      
      // 调用API更新手机号状态
      await OperationService.updateSmsPhone(id.toString(), newStatus, phone.level)
      
      // 更新本地状态
      phone.enabled = newStatus
      
      // 根据状态显示不同的提示信息
      if (newStatus) {
        alert(`手机号 ${phone.phone} 已设置启用`)
      } else {
        alert(`手机号 ${phone.phone} 已设置禁用`)
      }
    } catch (error) {
      console.error('更新手机号状态失败:', error)
      alert('手机号状态更新失败\n' + (error as Error).message)
    }
  }
}

// 处理发送短信
const handleSendSms = async (phoneNumber: string, level: string) => {
  if (!phoneNumber) {
    alert('手机号码不能为空')
    return
  }

  try {
    // 准备短信数据
    const smsData = {
      phoneNumber: phoneNumber,
      level: level
    }

    // 调用API发送告警短信
    const result = await OperationService.sendAlertSms(smsData)
    
    console.log('发送告警短信成功:', result)
    alert(`告警短信已发送至 ${phoneNumber}\n请检查手机是否收到短信`)
  } catch (error) {
    console.error('发送告警短信失败:', error)
    alert('告警短信发送失败\n' + (error as Error).message)
  }
}

// 处理手机号删除
const handlePhoneDelete = async (id: number) => {
  if (confirm('确定要删除这个手机号吗？')) {
    try {
      // 调用API删除手机号
      const result = await OperationService.deleteSmsPhone(id.toString())
      
      // 重新获取手机号列表
      await fetchSmsPhones()
      
      alert('手机号已删除\n该手机号已从告警接收列表中移除')
    } catch (error) {
      console.error('删除手机号失败:', error)
      alert('手机号删除失败\n' + (error as Error).message)
    }
  }
}

// 处理短信模板保存
const handleSaveSmsTemplate = async () => {
  console.log('🚀 开始保存短信模板...')
  
  if (!smsTemplate.value.trim()) {
    console.log('❌ 短信模板为空')
    alert('短信模板不能为空\n请输入短信模板内容')
    return
  }
  
  console.log('✅ 短信模板内容验证通过:', smsTemplate.value)
  isSubmitting.value = true
  
  try {
    // 准备请求数据
    const requestData = {
      templateName: smsTemplate.value.trim()
    }
    
    // 如果有模板ID，则更新；否则新增
    if (smsTemplateId.value !== null) {
      requestData.tempId = smsTemplateId.value
    }
    
    console.log('📤 准备发送请求数据:', requestData)
    
    // 调用API保存短信模板
    console.log('🌐 调用 OperationService.saveSmsTemplate...')
    const response = await OperationService.saveSmsTemplate(requestData)
    console.log('📥 收到API响应:', response)
    
    if (response.success) {
      // 更新模板ID（如果是新增操作）
      if (smsTemplateId.value === null && response.data) {
        smsTemplateId.value = response.data.tempId
        console.log('🆔 更新模板ID:', smsTemplateId.value)
      }
      
      console.log('✅ 短信模板保存成功')
      alert('短信模板保存成功\n告警短信模板已更新并生效')
    } else {
      console.log('❌ API返回失败:', response.message)
      throw new Error(response.message || '保存短信模板失败')
    }
  } catch (error) {
    console.error('❌ 保存短信模板失败:', error)
    alert('短信模板保存失败\n' + (error as Error).message)
  } finally {
    isSubmitting.value = false
  }
}

// 处理手机号列表保存
const handleSavePhoneList = async () => {
  isSubmitting.value = true
  
  try {
    // 状态勾选已经直接调用API更新，这里只需要提示用户
    alert('手机号列表保存成功\n告警接收手机号列表已更新并生效')
  } catch (error) {
    console.error('保存手机号列表失败:', error)
    alert('手机号列表保存失败\n' + (error as Error).message)
  } finally {
    isSubmitting.value = false
  }
}

// 获取短信级别名称
const getSmsLevelName = (levelId: string) => {
  return OperationService.formatSmsLevelName(levelId)
}

// 获取短信级别徽章样式
const getSmsLevelBadgeClass = (levelId: string) => {
  return OperationService.getSmsLevelBadgeClass(levelId)
}
</script>

<style scoped>
/* 与React版本完全一致的样式 */
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* 悬停效果 */
.hover\:bg-gray-50:hover {
  background-color: #f9fafb;
}

.hover\:bg-blue-50:hover {
  background-color: #eff6ff;
}

.hover\:bg-blue-700:hover {
  background-color: #1d4ed8;
}

.hover\:text-gray-700:hover {
  color: #374151;
}

.hover\:border-gray-300:hover {
  border-color: #d1d5db;
}

.hover\:text-red-900:hover {
  color: #7f1d1d;
}

/* 焦点样式 */
.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-blue-500:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.focus\:border-blue-500:focus {
  border-color: #3b82f6;
}

.focus\:ring-2:focus {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.focus\:ring-offset-2:focus {
  box-shadow: 0 0 0 2px #fff, 0 0 0 4px rgba(59, 130, 246, 0.2);
}

/* 禁用状态 */
.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

/* 表格样式 */
.divide-y > :not([hidden]) ~ :not([hidden]) {
  border-top-width: 1px;
  border-color: #e5e7eb;
}

.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  border-color: #e5e7eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-1.md\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .grid-cols-1.lg\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

/* 字体样式 */
.font-mono {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>