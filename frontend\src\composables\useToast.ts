// Toast提示实现，模拟React版本的sonner toast功能
export const useToast = () => {
  // 创建toast容器
  const ensureToastContainer = () => {
    let container = document.getElementById('toast-container');
    if (!container) {
      container = document.createElement('div');
      container.id = 'toast-container';
      container.className = 'fixed top-4 right-4 z-50 flex flex-col gap-2';
      document.body.appendChild(container);
    }
    return container;
  };

  // 创建toast元素
  const createToast = (message: string, options?: { description?: string }, type: string = 'default') => {
    const container = ensureToastContainer();
    
    const toast = document.createElement('div');
    toast.className = `bg-white rounded-lg shadow-lg p-4 min-w-[300px] max-w-[500px] transform transition-all duration-300 ease-in-out translate-x-0 opacity-0`;
    
    // 根据类型设置边框颜色
    switch (type) {
      case 'success':
        toast.classList.add('border-l-4', 'border-green-500');
        break;
      case 'error':
        toast.classList.add('border-l-4', 'border-red-500');
        break;
      case 'warning':
        toast.classList.add('border-l-4', 'border-yellow-500');
        break;
      case 'info':
        toast.classList.add('border-l-4', 'border-blue-500');
        break;
      default:
        toast.classList.add('border-l-4', 'border-gray-500');
    }
    
    const title = document.createElement('div');
    title.className = 'font-medium text-gray-900';
    title.textContent = message;
    
    toast.appendChild(title);
    
    if (options?.description) {
      const description = document.createElement('div');
      description.className = 'text-sm text-gray-500 mt-1';
      description.textContent = options.description;
      toast.appendChild(description);
    }
    
    container.appendChild(toast);
    
    // 动画显示
    setTimeout(() => {
      toast.classList.remove('translate-x-0', 'opacity-0');
      toast.classList.add('translate-x-0', 'opacity-100');
    }, 10);
    
    // 自动关闭
    setTimeout(() => {
      toast.classList.remove('opacity-100');
      toast.classList.add('opacity-0', 'translate-x-full');
      
      setTimeout(() => {
        container.removeChild(toast);
        // 如果没有更多toast，移除容器
        if (container.children.length === 0) {
          document.body.removeChild(container);
        }
      }, 300);
    }, 5000);
  };

  const success = (message: string, options?: { description?: string }) => {
    createToast(message, options, 'success');
  };

  const error = (message: string, options?: { description?: string }) => {
    createToast(message, options, 'error');
  };

  const info = (message: string, options?: { description?: string }) => {
    createToast(message, options, 'info');
  };

  const warning = (message: string, options?: { description?: string }) => {
    createToast(message, options, 'warning');
  };

  return {
    success,
    error,
    info,
    warning
  };
}
