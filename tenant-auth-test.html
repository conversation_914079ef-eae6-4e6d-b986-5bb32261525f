<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>租户授权申请接口测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 10px 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 10px 0;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 租户授权申请接口联调测试</h1>
        
        <div class="status info" id="currentStatus">
            <strong>测试状态：</strong>准备就绪
        </div>

        <!-- 认证测试 -->
        <div class="test-section">
            <h3>🔑 1. 认证测试</h3>
            <p>由于GET /api/Tenant/auth-applications 需要JWT token，我们需要先进行认证测试。</p>
            
            <div class="form-group">
                <label>认证方式：</label>
                <select id="authMethod">
                    <option value="mock">使用Mock Token</option>
                    <option value="login">真实登录获取Token</option>
                    <option value="manual">手动输入Token</option>
                </select>
            </div>
            
            <div id="loginForm" style="display: none;">
                <div class="form-row">
                    <div>
                        <label>邮箱：</label>
                        <input type="email" id="email" value="<EMAIL>">
                    </div>
                    <div>
                        <label>密码：</label>
                        <input type="password" id="password" value="123456">
                    </div>
                </div>
            </div>
            
            <div id="manualTokenForm" style="display: none;">
                <label>JWT Token：</label>
                <input type="text" id="manualToken" placeholder="Bearer token...">
            </div>
            
            <button onclick="generateToken()">获取Token</button>
            <button onclick="testAuth()">测试认证</button>
            
            <div id="tokenResult"></div>
        </div>

        <!-- POST测试 -->
        <div class="test-section">
            <h3>📝 2. POST /api/Tenant/auth-applications 测试</h3>
            <p>提交租户授权申请（无需认证）</p>
            
            <div class="form-row">
                <div>
                    <label>租户名称：</label>
                    <input type="text" id="tenantName" value="测试租户公司">
                </div>
                <div>
                    <label>租户ID：</label>
                    <input type="text" id="tenantId" value="test-tenant-001">
                </div>
            </div>
            
            <div class="form-row">
                <div>
                    <label>联系人：</label>
                    <input type="text" id="contactPerson" value="张三">
                </div>
                <div>
                    <label>联系电话：</label>
                    <input type="text" id="contactPhone" value="13800138000">
                </div>
            </div>
            
            <div class="form-row">
                <div>
                    <label>联系邮箱：</label>
                    <input type="email" id="contactEmail" value="<EMAIL>">
                </div>
                <div>
                    <label>服务类型：</label>
                    <select id="serviceType">
                        <option value="basic">基础服务</option>
                        <option value="advanced">高级服务</option>
                        <option value="enterprise">企业服务</option>
                    </select>
                </div>
            </div>
            
            <div class="form-row">
                <div>
                    <label>授权期限：</label>
                    <select id="authPeriod">
                        <option value="1-month">1个月</option>
                        <option value="3-months">3个月</option>
                        <option value="1-year">1年</option>
                        <option value="3-years">3年</option>
                    </select>
                </div>
                <div>
                    <label>权限范围：</label>
                    <select id="permissionScope">
                        <option value="read">只读权限</option>
                        <option value="write">读写权限</option>
                        <option value="admin">管理权限</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label>申请描述：</label>
                <textarea id="description" rows="3" placeholder="请描述申请用途...">测试租户授权申请，用于系统联调测试。</textarea>
            </div>
            
            <button onclick="submitApplication()">提交申请</button>
            <div id="postResult"></div>
        </div>

        <!-- GET测试 -->
        <div class="test-section">
            <h3>📋 3. GET /api/Tenant/auth-applications 测试</h3>
            <p>获取授权申请列表（需要认证）</p>
            
            <div class="form-row">
                <div>
                    <label>页码：</label>
                    <input type="number" id="page" value="1" min="1">
                </div>
                <div>
                    <label>页大小：</label>
                    <input type="number" id="pageSize" value="10" min="1" max="100">
                </div>
            </div>
            
            <div class="form-row">
                <div>
                    <label>搜索关键词：</label>
                    <input type="text" id="searchTerm" placeholder="租户名称、ID或联系人...">
                </div>
                <div>
                    <label>服务类型筛选：</label>
                    <select id="filterServiceType">
                        <option value="">全部</option>
                        <option value="basic">基础服务</option>
                        <option value="advanced">高级服务</option>
                        <option value="enterprise">企业服务</option>
                    </select>
                </div>
            </div>
            
            <button onclick="getApplications()">获取申请列表</button>
            <div id="getResult"></div>
        </div>

        <!-- 其他端点测试 -->
        <div class="test-section">
            <h3>🔍 4. 其他端点测试</h3>
            <button onclick="testGetDetail()">测试获取详情</button>
            <button onclick="testApprove()">测试审批</button>
            <button onclick="testDelete()">测试删除</button>
            <div id="otherResult"></div>
        </div>

        <!-- 日志区域 -->
        <div class="test-section">
            <h3>📋 测试日志</h3>
            <button onclick="clearLogs()">清空日志</button>
            <pre id="logs"></pre>
        </div>
    </div>

    <script>
        let currentToken = null;
        let logs = [];
        let lastApplicationId = null;

        // 日志函数
        function addLog(level, message, data = null) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${level}: ${message}`;
            logs.push(logEntry);
            
            if (data) {
                logs.push(JSON.stringify(data, null, 2));
            }
            
            document.getElementById('logs').textContent = logs.join('\n');
        }

        // 切换认证方式
        document.getElementById('authMethod').addEventListener('change', function() {
            const method = this.value;
            document.getElementById('loginForm').style.display = method === 'login' ? 'block' : 'none';
            document.getElementById('manualTokenForm').style.display = method === 'manual' ? 'block' : 'none';
        });

        // 生成或获取Token
        async function generateToken() {
            const method = document.getElementById('authMethod').value;
            
            try {
                if (method === 'mock') {
                    // 生成Mock Token
                    currentToken = `mock_token_${Date.now()}`;
                    addLog('INFO', '生成Mock Token成功');
                    updateTokenResult('success', 'Mock Token已生成', currentToken);
                    
                } else if (method === 'login') {
                    // 真实登录
                    const email = document.getElementById('email').value;
                    const password = document.getElementById('password').value;
                    
                    addLog('INFO', `尝试登录: ${email}`);
                    
                    const response = await fetch('http://localhost:5172/api/Auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ email, password })
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success && data.data.token) {
                            currentToken = data.data.token;
                            addLog('SUCCESS', '登录成功', data.data);
                            updateTokenResult('success', '登录成功获取Token', currentToken);
                        } else {
                            throw new Error('登录响应异常');
                        }
                    } else {
                        throw new Error(`登录失败: ${response.status}`);
                    }
                    
                } else if (method === 'manual') {
                    currentToken = document.getElementById('manualToken').value;
                    if (currentToken) {
                        addLog('INFO', '手动设置Token');
                        updateTokenResult('info', '手动Token已设置', currentToken);
                    } else {
                        throw new Error('请输入Token');
                    }
                }
                
            } catch (error) {
                addLog('ERROR', `获取Token失败: ${error.message}`);
                updateTokenResult('error', error.message);
            }
        }

        // 更新Token结果显示
        function updateTokenResult(type, message, token = null) {
            const result = document.getElementById('tokenResult');
            result.className = `status ${type}`;
            result.innerHTML = `
                <strong>${message}</strong>
                ${token ? `<br><small>Token: ${token.substring(0, 50)}...</small>` : ''}
            `;
        }

        // 测试认证
        async function testAuth() {
            if (!currentToken) {
                addLog('ERROR', '请先获取Token');
                return;
            }
            
            try {
                addLog('INFO', '测试Token有效性...');
                
                const response = await fetch('http://localhost:5172/api/Auth/profile', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addLog('SUCCESS', 'Token有效', data);
                    updateTokenResult('success', 'Token验证通过');
                } else {
                    addLog('ERROR', `Token验证失败: ${response.status}`);
                    updateTokenResult('error', `Token验证失败: ${response.status}`);
                }
            } catch (error) {
                addLog('ERROR', `认证测试失败: ${error.message}`);
                updateTokenResult('error', error.message);
            }
        }

        // 提交申请
        async function submitApplication() {
            try {
                const requestData = {
                    tenantName: document.getElementById('tenantName').value,
                    tenantId: document.getElementById('tenantId').value,
                    contactPerson: document.getElementById('contactPerson').value,
                    contactPhone: document.getElementById('contactPhone').value,
                    contactEmail: document.getElementById('contactEmail').value,
                    serviceType: document.getElementById('serviceType').value,
                    authPeriod: document.getElementById('authPeriod').value,
                    permissionScope: document.getElementById('permissionScope').value,
                    description: document.getElementById('description').value
                };
                
                addLog('INFO', 'POST /api/Tenant/auth-applications', requestData);
                
                const response = await fetch('http://localhost:5172/api/Tenant/auth-applications', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (response.ok) {
                    const data = await response.json();
                    lastApplicationId = data.data?.id;
                    addLog('SUCCESS', 'POST请求成功', data);
                    updateResult('postResult', 'success', 'POST请求成功', data);
                } else {
                    const errorData = await response.text();
                    addLog('ERROR', `POST请求失败: ${response.status}`, errorData);
                    updateResult('postResult', 'error', `POST请求失败: ${response.status}`, errorData);
                }
            } catch (error) {
                addLog('ERROR', `POST请求异常: ${error.message}`);
                updateResult('postResult', 'error', error.message);
            }
        }

        // 获取申请列表
        async function getApplications() {
            if (!currentToken) {
                addLog('ERROR', '获取申请列表需要Token，请先进行认证');
                updateResult('getResult', 'error', '需要先获取Token');
                return;
            }
            
            try {
                const page = document.getElementById('page').value;
                const pageSize = document.getElementById('pageSize').value;
                const searchTerm = document.getElementById('searchTerm').value;
                const serviceType = document.getElementById('filterServiceType').value;
                
                let url = `http://localhost:5172/api/Tenant/auth-applications?page=${page}&pageSize=${pageSize}`;
                if (searchTerm) url += `&searchTerm=${encodeURIComponent(searchTerm)}`;
                if (serviceType) url += `&serviceType=${serviceType}`;
                
                addLog('INFO', `GET ${url}`);
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addLog('SUCCESS', 'GET请求成功', data);
                    updateResult('getResult', 'success', 'GET请求成功', data);
                } else {
                    const errorData = await response.text();
                    addLog('ERROR', `GET请求失败: ${response.status}`, errorData);
                    updateResult('getResult', 'error', `GET请求失败: ${response.status}`, errorData);
                }
            } catch (error) {
                addLog('ERROR', `GET请求异常: ${error.message}`);
                updateResult('getResult', 'error', error.message);
            }
        }

        // 测试获取详情
        async function testGetDetail() {
            if (!currentToken || !lastApplicationId) {
                addLog('ERROR', '需要Token和申请ID，请先提交申请');
                return;
            }
            
            try {
                const url = `http://localhost:5172/api/Tenant/auth-applications/${lastApplicationId}`;
                addLog('INFO', `GET ${url}`);
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addLog('SUCCESS', '获取详情成功', data);
                    updateResult('otherResult', 'success', '获取详情成功', data);
                } else {
                    const errorData = await response.text();
                    addLog('ERROR', `获取详情失败: ${response.status}`, errorData);
                    updateResult('otherResult', 'error', `获取详情失败: ${response.status}`, errorData);
                }
            } catch (error) {
                addLog('ERROR', `获取详情异常: ${error.message}`);
                updateResult('otherResult', 'error', error.message);
            }
        }

        // 测试审批
        async function testApprove() {
            if (!currentToken || !lastApplicationId) {
                addLog('ERROR', '需要Token和申请ID，请先提交申请');
                return;
            }
            
            try {
                const url = `http://localhost:5172/api/Tenant/auth-applications/${lastApplicationId}/approve`;
                const requestData = { action: 'approve', comment: '测试审批通过' };
                
                addLog('INFO', `PUT ${url}`, requestData);
                
                const response = await fetch(url, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addLog('SUCCESS', '审批成功', data);
                    updateResult('otherResult', 'success', '审批成功', data);
                } else {
                    const errorData = await response.text();
                    addLog('ERROR', `审批失败: ${response.status}`, errorData);
                    updateResult('otherResult', 'error', `审批失败: ${response.status}`, errorData);
                }
            } catch (error) {
                addLog('ERROR', `审批异常: ${error.message}`);
                updateResult('otherResult', 'error', error.message);
            }
        }

        // 测试删除
        async function testDelete() {
            if (!currentToken || !lastApplicationId) {
                addLog('ERROR', '需要Token和申请ID，请先提交申请');
                return;
            }
            
            if (!confirm('确定要删除申请吗？')) {
                return;
            }
            
            try {
                const url = `http://localhost:5172/api/Tenant/auth-applications/${lastApplicationId}`;
                addLog('INFO', `DELETE ${url}`);
                
                const response = await fetch(url, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addLog('SUCCESS', '删除成功', data);
                    updateResult('otherResult', 'success', '删除成功', data);
                    lastApplicationId = null; // 清除已删除的ID
                } else {
                    const errorData = await response.text();
                    addLog('ERROR', `删除失败: ${response.status}`, errorData);
                    updateResult('otherResult', 'error', `删除失败: ${response.status}`, errorData);
                }
            } catch (error) {
                addLog('ERROR', `删除异常: ${error.message}`);
                updateResult('otherResult', 'error', error.message);
            }
        }

        // 更新结果显示
        function updateResult(elementId, type, message, data = null) {
            const element = document.getElementById(elementId);
            element.className = `status ${type}`;
            element.innerHTML = `
                <strong>${message}</strong>
                ${data ? `<br><pre>${JSON.stringify(data, null, 2)}</pre>` : ''}
            `;
        }

        // 清空日志
        function clearLogs() {
            logs = [];
            document.getElementById('logs').textContent = '';
            addLog('INFO', '日志已清空');
        }

        // 页面加载时初始化
        window.onload = function() {
            addLog('INFO', '租户授权申请接口测试工具已加载');
            addLog('INFO', '测试地址: http://localhost:5172/api/Tenant/auth-applications');
        };
    </script>
</body>
</html>