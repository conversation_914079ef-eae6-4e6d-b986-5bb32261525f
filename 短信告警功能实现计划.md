# 短信告警功能实现计划

## 1. 概述

本文档详细说明了如何实现短信告警功能，包括数据库实体、DTO对象、服务层和控制器层的扩展。

## 2. 实现步骤

### 2.1 创建SmsAlarmSetting实体类

文件路径: `backend/Models/Entities/SmsAlarmSetting.cs`

```csharp
using SqlSugar;

namespace MapStudio.Api.Models.Entities
{
    /// <summary>
    /// 短信告警设置实体，对应数据库表 ms_sms_alarm_settings
    /// </summary>
    [SugarTable("ms_sms_alarm_settings")]
    public class SmsAlarmSetting
    {
        /// <summary>
        /// 设置ID (主键)
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, Length = 36)]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 短信模板
        /// </summary>
        [SugarColumn(Length = 2000, IsNullable = false)]
        public string SmsTemplate { get; set; } = string.Empty;

        /// <summary>
        /// 手机号列表 (JSON格式)
        /// </summary>
        [SugarColumn(ColumnDataType = "json", IsNullable = true)]
        public string? PhoneList { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime UpdatedAt { get; set; }
    }
}
```

### 2.2 扩展OperationDTOs.cs文件

文件路径: `backend/Models/DTOs/OperationDTOs.cs`

在文件末尾添加以下DTO对象：

```csharp
// 短信告警相关DTO
public class SmsPhoneDto
{
    public string Id { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public bool Enabled { get; set; }
    public List<string> Levels { get; set; } = new();
    public DateTime AddedAt { get; set; }
}

public class AlarmSmsResponse
{
    public string Id { get; set; } = string.Empty;
    public string SmsTemplate { get; set; } = string.Empty;
    public List<SmsPhoneDto> PhoneList { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class SmsTemplateUpdateRequest
{
    public string SmsTemplate { get; set; } = string.Empty;
}

public class SmsPhoneRequest
{
    public string Phone { get; set; } = string.Empty;
    public List<string> Levels { get; set; } = new();
}

public class SmsPhoneUpdateRequest
{
    public bool Enabled { get; set; }
    public List<string> Levels { get; set; } = new();
}

public class SmsTemplateUpdateResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime UpdatedAt { get; set; }
}

public class SmsPhoneResponse
{
    public string Id { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public bool Enabled { get; set; }
    public List<string> Levels { get; set; } = new();
    public string Message { get; set; } = string.Empty;
}

public class SmsPhoneUpdateResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
}
```

### 2.3 扩展IOperationService接口

文件路径: `backend/Services/Interfaces/IOperationService.cs`

添加以下方法签名：

```csharp
Task<AlarmSmsResponse> GetAlarmSmsSettingsAsync();
Task<SmsTemplateUpdateResponse> UpdateSmsTemplateAsync(string smsTemplate);
Task<SmsPhoneResponse> AddSmsPhoneAsync(string phone, string[] levels);
Task<SmsPhoneUpdateResponse> UpdateSmsPhoneAsync(string id, bool enabled, string[] levels);
Task DeleteSmsPhoneAsync(string id);
```

### 2.4 实现OperationService中的短信告警相关方法

文件路径: `backend/Services/Implementations/OperationService.cs`

添加以下方法实现：

```csharp
public async Task<AlarmSmsResponse> GetAlarmSmsSettingsAsync()
{
    try
    {
        var setting = await _db.Queryable<SmsAlarmSetting>()
            .OrderBy(s => s.UpdatedAt, OrderByType.Desc)
            .FirstAsync();

        if (setting == null)
        {
            setting = new SmsAlarmSetting
            {
                Id = Guid.NewGuid().ToString(),
                SmsTemplate = "【地图工作室】{{tenantName}}的{{serviceName}}发生{{alarmLevel}}：{{briefDesc}}，时间：{{occurTime}}",
                PhoneList = "[]",
                IsActive = true,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };
            
            await _db.Insertable(setting).ExecuteCommandAsync();
        }

        var phoneList = new List<SmsPhoneDto>();
        if (!string.IsNullOrEmpty(setting.PhoneList))
        {
            phoneList = JsonSerializer.Deserialize<List<SmsPhoneDto>>(setting.PhoneList) ?? new List<SmsPhoneDto>();
        }

        return new AlarmSmsResponse
        {
            Id = setting.Id,
            SmsTemplate = setting.SmsTemplate,
            PhoneList = phoneList,
            IsActive = setting.IsActive,
            UpdatedAt = setting.UpdatedAt
        };
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "获取短信告警设置失败");
        throw;
    }
}

public async Task<SmsTemplateUpdateResponse> UpdateSmsTemplateAsync(string smsTemplate)
{
    try
    {
        var setting = await _db.Queryable<SmsAlarmSetting>()
            .OrderBy(s => s.UpdatedAt, OrderByType.Desc)
            .FirstAsync();

        if (setting == null)
        {
            setting = new SmsAlarmSetting
            {
                Id = Guid.NewGuid().ToString(),
                SmsTemplate = smsTemplate,
                PhoneList = "[]",
                IsActive = true,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };
        }
        else
        {
            setting.SmsTemplate = smsTemplate;
            setting.UpdatedAt = DateTime.Now;
        }

        await _db.Storageable(setting).ExecuteCommandAsync();

        return new SmsTemplateUpdateResponse
        {
            Success = true,
            Message = "短信模板更新成功",
            UpdatedAt = setting.UpdatedAt
        };
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "更新短信模板失败");
        throw;
    }
}

public async Task<SmsPhoneResponse> AddSmsPhoneAsync(string phone, string[] levels)
{
    try
    {
        var setting = await _db.Queryable<SmsAlarmSetting>()
            .OrderBy(s => s.UpdatedAt, OrderByType.Desc)
            .FirstAsync();

        if (setting == null)
        {
            setting = new SmsAlarmSetting
            {
                Id = Guid.NewGuid().ToString(),
                SmsTemplate = "【地图工作室】{{tenantName}}的{{serviceName}}发生{{alarmLevel}}：{{briefDesc}}，时间：{{occurTime}}",
                PhoneList = "[]",
                IsActive = true,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };
        }

        var phoneList = new List<SmsPhoneDto>();
        if (!string.IsNullOrEmpty(setting.PhoneList))
        {
            phoneList = JsonSerializer.Deserialize<List<SmsPhoneDto>>(setting.PhoneList) ?? new List<SmsPhoneDto>();
        }

        var newPhone = new SmsPhoneDto
        {
            Id = Guid.NewGuid().ToString(),
            Phone = phone,
            Enabled = true,
            Levels = levels.ToList(),
            AddedAt = DateTime.Now
        };

        phoneList.Add(newPhone);

        setting.PhoneList = JsonSerializer.Serialize(phoneList);
        setting.UpdatedAt = DateTime.Now;

        await _db.Storageable(setting).ExecuteCommandAsync();

        return new SmsPhoneResponse
        {
            Id = newPhone.Id,
            Phone = newPhone.Phone,
            Enabled = newPhone.Enabled,
            Levels = newPhone.Levels,
            Message = "手机号添加成功"
        };
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "添加手机号失败");
        throw;
    }
}

public async Task<SmsPhoneUpdateResponse> UpdateSmsPhoneAsync(string id, bool enabled, string[] levels)
{
    try
    {
        var setting = await _db.Queryable<SmsAlarmSetting>()
            .OrderBy(s => s.UpdatedAt, OrderByType.Desc)
            .FirstAsync();

        if (setting == null || string.IsNullOrEmpty(setting.PhoneList))
        {
            return new SmsPhoneUpdateResponse
            {
                Success = false,
                Message = "未找到相关设置"
            };
        }

        var phoneList = JsonSerializer.Deserialize<List<SmsPhoneDto>>(setting.PhoneList) ?? new List<SmsPhoneDto>();
        var phoneItem = phoneList.FirstOrDefault(p => p.Id == id);

        if (phoneItem == null)
        {
            return new SmsPhoneUpdateResponse
            {
                Success = false,
                Message = "未找到指定的手机号"
            };
        }

        phoneItem.Enabled = enabled;
        phoneItem.Levels = levels.ToList();
        phoneItem.AddedAt = DateTime.Now;

        setting.PhoneList = JsonSerializer.Serialize(phoneList);
        setting.UpdatedAt = DateTime.Now;

        await _db.Storageable(setting).ExecuteCommandAsync();

        return new SmsPhoneUpdateResponse
        {
            Success = true,
            Message = "手机号更新成功"
        };
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "更新手机号失败");
        throw;
    }
}

public async Task DeleteSmsPhoneAsync(string id)
{
    try
    {
        var setting = await _db.Queryable<SmsAlarmSetting>()
            .OrderBy(s => s.UpdatedAt, OrderByType.Desc)
            .FirstAsync();

        if (setting == null || string.IsNullOrEmpty(setting.PhoneList))
        {
            return;
        }

        var phoneList = JsonSerializer.Deserialize<List<SmsPhoneDto>>(setting.PhoneList) ?? new List<SmsPhoneDto>();
        var updatedList = phoneList.Where(p => p.Id != id).ToList();

        setting.PhoneList = JsonSerializer.Serialize(updatedList);
        setting.UpdatedAt = DateTime.Now;

        await _db.Storageable(setting).ExecuteCommandAsync();
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "删除手机号失败");
        throw;
    }
}
```

### 2.5 扩展OperationController，添加相关API接口

文件路径: `backend/Controllers/OperationController.cs`

添加以下控制器方法：

```csharp
/// <summary>
/// 获取短信告警设置
/// </summary>
[HttpGet("alarm-settings/sms")]
public async Task<ActionResult<ApiResponse<AlarmSmsResponse>>> GetAlarmSmsSettings()
{
    try
    {
        var result = await _operationService.GetAlarmSmsSettingsAsync();
        return Ok(ApiResponse<AlarmSmsResponse>.SuccessResult(result, "获取短信告警设置成功"));
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "获取短信告警设置失败");
        return StatusCode(500, ApiResponse<AlarmSmsResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
    }
}

/// <summary>
/// 更新短信模板
/// </summary>
[HttpPut("alarm-settings/sms-template")]
public async Task<ActionResult<ApiResponse<SmsTemplateUpdateResponse>>> UpdateSmsTemplate([FromBody] SmsTemplateUpdateRequest request)
{
    try
    {
        var result = await _operationService.UpdateSmsTemplateAsync(request.SmsTemplate);
        return Ok(ApiResponse<SmsTemplateUpdateResponse>.SuccessResult(result, "更新短信模板成功"));
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "更新短信模板失败");
        return StatusCode(500, ApiResponse<SmsTemplateUpdateResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
    }
}

/// <summary>
/// 添加接收手机号
/// </summary>
[HttpPost("alarm-settings/sms-phones")]
public async Task<ActionResult<ApiResponse<SmsPhoneResponse>>> AddSmsPhone([FromBody] SmsPhoneRequest request)
{
    try
    {
        // 验证手机号格式
        if (string.IsNullOrEmpty(request.Phone) || !IsValidPhoneNumber(request.Phone))
        {
            return BadRequest(ApiResponse<SmsPhoneResponse>.ErrorResult("手机号格式不正确", "INVALID_PHONE"));
        }

        var result = await _operationService.AddSmsPhoneAsync(request.Phone, request.Levels.ToArray());
        return Ok(ApiResponse<SmsPhoneResponse>.SuccessResult(result, "添加手机号成功"));
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "添加手机号失败");
        return StatusCode(500, ApiResponse<SmsPhoneResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
    }
}

/// <summary>
/// 更新接收手机号
/// </summary>
[HttpPut("alarm-settings/sms-phones/{id}")]
public async Task<ActionResult<ApiResponse<SmsPhoneUpdateResponse>>> UpdateSmsPhone(string id, [FromBody] SmsPhoneUpdateRequest request)
{
    try
    {
        var result = await _operationService.UpdateSmsPhoneAsync(id, request.Enabled, request.Levels.ToArray());
        return Ok(ApiResponse<SmsPhoneUpdateResponse>.SuccessResult(result, "更新手机号成功"));
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "更新手机号失败");
        return StatusCode(500, ApiResponse<SmsPhoneUpdateResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
    }
}

/// <summary>
/// 删除接收手机号
/// </summary>
[HttpDelete("alarm-settings/sms-phones/{id}")]
public async Task<ActionResult<ApiResponse>> DeleteSmsPhone(string id)
{
    try
    {
        await _operationService.DeleteSmsPhoneAsync(id);
        return Ok(ApiResponse.SuccessResult("删除手机号成功"));
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "删除手机号失败");
        return StatusCode(500, ApiResponse.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
    }
}

/// <summary>
/// 验证手机号格式
/// </summary>
private bool IsValidPhoneNumber(string phoneNumber)
{
    return System.Text.RegularExpressions.Regex.IsMatch(phoneNumber, @"^1[3-9]\d{9}$");
}
```

### 2.6 更新DatabaseInitializer，添加新表的初始化

文件路径: `backend/Data/Context/DatabaseInitializer.cs`

在`InitializeAsync`方法中添加以下代码：

```csharp
// 在现有代码后添加
_db.CodeFirst.InitTables<SmsAlarmSetting>(); // 创建ms_sms_alarm_settings表
```

在`SeedDataAsync`方法中添加以下代码：

```csharp
// 在现有代码后添加
var existingSmsAlarmSettings = await _db.Queryable<SmsAlarmSetting>().CountAsync();
if (existingSmsAlarmSettings == 0)
{
    // 初始化默认短信告警设置
    var defaultSmsAlarmSetting = new SmsAlarmSetting
    {
        Id = Guid.NewGuid().ToString(),
        SmsTemplate = "【地图工作室】{{tenantName}}的{{serviceName}}发生{{alarmLevel}}：{{briefDesc}}，时间：{{occurTime}}",
        PhoneList = "[]",
        IsActive = true,
        CreatedAt = DateTime.Now,
        UpdatedAt = DateTime.Now
    };

    await _db.Insertable(defaultSmsAlarmSetting).ExecuteCommandAsync();
    _logger.LogInformation("默认短信告警设置创建完成");
}
```

## 3. 测试验证

### 3.1 单元测试

为每个新添加的服务方法编写单元测试。

### 3.2 集成测试

使用API测试工具（如Postman）测试所有新添加的API接口。

### 3.3 数据库测试

验证数据是否正确存储和检索。

## 4. 部署说明

1. 更新数据库结构
2. 部署新的后端代码
3. 验证功能是否正常工作