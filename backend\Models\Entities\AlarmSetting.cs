using SqlSugar;

namespace MapStudio.Api.Models.Entities
{
    /// <summary>
    /// 告警设置实体，对应数据库表 ms_alarm_settings
    /// </summary>
    [SugarTable("ms_alarm_settings")]
    public class AlarmSetting
    {
        /// <summary>
        /// 设置ID (主键)
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, Length = 36)]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 告警名称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 邮件模板
        /// </summary>
        [SugarColumn(Length = 2000, IsNullable = true)]
        public string? EmailTemplate { get; set; }

        /// <summary>
        /// 邮件列表 (JSON格式)
        /// </summary>
        [SugarColumn(ColumnDataType = "json", IsNullable = true)]
        public string? EmailList { get; set; }

        /// <summary>
        /// 告警类型
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        public string AlarmType { get; set; } = string.Empty;

        /// <summary>
        /// 告警条件 (JSON格式)
        /// </summary>
        [SugarColumn(ColumnDataType = "json", IsNullable = true)]
        public string? AlarmConditions { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime UpdatedAt { get; set; }
    }
}