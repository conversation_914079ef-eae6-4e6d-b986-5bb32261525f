using MapStudio.Api.Models.DTOs;

namespace MapStudio.Api.Services.Interfaces
{
    /// <summary>
    /// 短信接收手机号服务接口
    /// </summary>
    public interface ISmsPhoneService
    {
        /// <summary>
        /// 获取短信接收手机号列表，按创建时间倒序排序
        /// </summary>
        /// <returns>短信接收手机号列表</returns>
        Task<SmsPhoneSettingListResponse> GetSmsPhoneSettingsAsync();

        /// <summary>
        /// 添加短信接收手机号
        /// </summary>
        /// <param name="phone">手机号</param>
        /// <param name="levels">告警级别</param>
        /// <returns>添加结果</returns>
        Task<SmsPhoneSettingResponse> AddSmsPhoneSettingAsync(string phone, string[] levels);

        /// <summary>
        /// 更新短信接收手机号状态
        /// </summary>
        /// <param name="id">设置ID</param>
        /// <param name="status">状态</param>
        /// <param name="levels">告警级别</param>
        /// <returns>更新结果</returns>
        Task<SmsPhoneSettingResponse> UpdateSmsPhoneSettingAsync(int id, bool status, string[] levels);

        /// <summary>
        /// 删除短信接收手机号
        /// </summary>
        /// <param name="id">设置ID</param>
        /// <returns>删除结果</returns>
        Task<SmsPhoneSettingResponse> DeleteSmsPhoneSettingAsync(int id);
    }
}