import { test, expect } from '@playwright/test';

test.describe('首页功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 访问首页
    await page.goto('/');
  });

  test('应该正确加载首页', async ({ page }) => {
    // 检查页面标题
    await expect(page).toHaveTitle(/地图工作室SaaS平台/);

    // 检查主标题
    await expect(page.locator('h1')).toContainText('地图工作室SaaS平台');

    // 检查副标题
    await expect(page.locator('p')).toContainText('租户管理与运营中心');
  });

  test('应该显示核心指标卡片', async ({ page }) => {
    // 检查四个指标卡片是否存在
    const metricsCards = page.locator('.grid .bg-white.rounded-xl');
    await expect(metricsCards).toHaveCount(4);

    // 检查具体指标
    await expect(page.locator('text=活跃租户')).toBeVisible();
    await expect(page.locator('text=今日访问量')).toBeVisible();
    await expect(page.locator('text=待审批申请')).toBeVisible();
    await expect(page.locator('text=服务可用性')).toBeVisible();
  });

  test('应该显示API连接状态', async ({ page }) => {
    // 检查API连接状态指示器
    await expect(page.locator('text=API:')).toBeVisible();

    // 检查测试按钮
    const testButton = page.locator('button:has-text("测试")');
    await expect(testButton).toBeVisible();

    // 点击测试按钮
    await testButton.click();

    // 等待测试完成（按钮文本变化）
    await expect(testButton).toContainText('测试中...', { timeout: 1000 });
  });

  test('应该显示快速访问卡片', async ({ page }) => {
    // 检查快速访问区域
    await expect(page.locator('h2:has-text("快速访问")')).toBeVisible();

    // 检查四个快速访问链接
    await expect(page.locator('text=租户授权申请')).toBeVisible();
    await expect(page.locator('text=待审批清单')).toBeVisible();
    await expect(page.locator('text=服务状态监控')).toBeVisible();
    await expect(page.locator('text=访问统计报表')).toBeVisible();
  });

  test('应该正确显示图表区域', async ({ page }) => {
    // 检查访问趋势图表
    await expect(page.locator('h2:has-text("访问趋势")')).toBeVisible();

    // 检查图表容器
    const chartContainer = page.locator('.h-80');
    await expect(chartContainer).toBeVisible();

    // 检查租户状态分布图
    await expect(page.locator('h2:has-text("租户服务状态分布")')).toBeVisible();
  });

  test('快速访问链接应该可以点击', async ({ page }) => {
    // 测试租户授权申请链接
    const authApplyLink = page.locator('a[href="/tenant/auth-apply"]');
    await expect(authApplyLink).toBeVisible();

    // 点击链接（不导航，只检查是否可点击）
    await authApplyLink.hover();
    await expect(authApplyLink).toHaveClass(/hover:border-blue-200/);
  });

  test('应该显示最近申请表格', async ({ page }) => {
    // 检查最近申请标题
    await expect(page.locator('h2:has-text("最近授权申请")')).toBeVisible();

    // 检查表格头部
    await expect(page.locator('th:has-text("申请单号")')).toBeVisible();
    await expect(page.locator('th:has-text("租户名称")')).toBeVisible();
    await expect(page.locator('th:has-text("申请日期")')).toBeVisible();
    await expect(page.locator('th:has-text("状态")')).toBeVisible();

    // 检查查看全部链接
    await expect(page.locator('a:has-text("查看全部")')).toBeVisible();
  });
});
