{"Version": 1, "WorkspaceRootPath": "D:\\路网通项目SVN\\1_后台专题类模块\\map-studio\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A81335CF-9385-E71E-015D-CF0AE2B901BD}|backend\\MapStudio.Api.csproj|d:\\路网通项目svn\\1_后台专题类模块\\map-studio\\backend\\controllers\\operationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A81335CF-9385-E71E-015D-CF0AE2B901BD}|backend\\MapStudio.Api.csproj|solutionrelative:backend\\controllers\\operationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A81335CF-9385-E71E-015D-CF0AE2B901BD}|backend\\MapStudio.Api.csproj|d:\\路网通项目svn\\1_后台专题类模块\\map-studio\\backend\\services\\implementations\\operationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A81335CF-9385-E71E-015D-CF0AE2B901BD}|backend\\MapStudio.Api.csproj|solutionrelative:backend\\services\\implementations\\operationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A81335CF-9385-E71E-015D-CF0AE2B901BD}|backend\\MapStudio.Api.csproj|d:\\路网通项目svn\\1_后台专题类模块\\map-studio\\backend\\services\\interfaces\\ioperationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A81335CF-9385-E71E-015D-CF0AE2B901BD}|backend\\MapStudio.Api.csproj|solutionrelative:backend\\services\\interfaces\\ioperationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A81335CF-9385-E71E-015D-CF0AE2B901BD}|backend\\MapStudio.Api.csproj|d:\\路网通项目svn\\1_后台专题类模块\\map-studio\\backend\\controllers\\sendmsgcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A81335CF-9385-E71E-015D-CF0AE2B901BD}|backend\\MapStudio.Api.csproj|solutionrelative:backend\\controllers\\sendmsgcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A81335CF-9385-E71E-015D-CF0AE2B901BD}|backend\\MapStudio.Api.csproj|d:\\路网通项目svn\\1_后台专题类模块\\map-studio\\backend\\models\\dtos\\operationdtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A81335CF-9385-E71E-015D-CF0AE2B901BD}|backend\\MapStudio.Api.csproj|solutionrelative:backend\\models\\dtos\\operationdtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A81335CF-9385-E71E-015D-CF0AE2B901BD}|backend\\MapStudio.Api.csproj|d:\\路网通项目svn\\1_后台专题类模块\\map-studio\\backend\\models\\dtos\\tenantdtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A81335CF-9385-E71E-015D-CF0AE2B901BD}|backend\\MapStudio.Api.csproj|solutionrelative:backend\\models\\dtos\\tenantdtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 5, "Children": [{"$type": "Document", "DocumentIndex": 3, "Title": "SendMsgController.cs", "DocumentMoniker": "D:\\路网通项目SVN\\1_后台专题类模块\\map-studio\\backend\\Controllers\\SendMsgController.cs", "RelativeDocumentMoniker": "backend\\Controllers\\SendMsgController.cs", "ToolTip": "D:\\路网通项目SVN\\1_后台专题类模块\\map-studio\\backend\\Controllers\\SendMsgController.cs", "RelativeToolTip": "backend\\Controllers\\SendMsgController.cs", "ViewState": "AgIAAH4BAAAAAAAAAAAkwFwBAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-12T07:23:24.896Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "IOperationService.cs", "DocumentMoniker": "D:\\路网通项目SVN\\1_后台专题类模块\\map-studio\\backend\\Services\\Interfaces\\IOperationService.cs", "RelativeDocumentMoniker": "backend\\Services\\Interfaces\\IOperationService.cs", "ToolTip": "D:\\路网通项目SVN\\1_后台专题类模块\\map-studio\\backend\\Services\\Interfaces\\IOperationService.cs", "RelativeToolTip": "backend\\Services\\Interfaces\\IOperationService.cs", "ViewState": "AgIAABUAAAAAAAAAAAAgwCAAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-12T07:05:32.037Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "OperationService.cs", "DocumentMoniker": "D:\\路网通项目SVN\\1_后台专题类模块\\map-studio\\backend\\Services\\Implementations\\OperationService.cs", "RelativeDocumentMoniker": "backend\\Services\\Implementations\\OperationService.cs", "ToolTip": "D:\\路网通项目SVN\\1_后台专题类模块\\map-studio\\backend\\Services\\Implementations\\OperationService.cs", "RelativeToolTip": "backend\\Services\\Implementations\\OperationService.cs", "ViewState": "AgIAAB0CAAAAAAAAAAAUwCwCAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-12T07:05:37.88Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "OperationDTOs.cs", "DocumentMoniker": "D:\\路网通项目SVN\\1_后台专题类模块\\map-studio\\backend\\Models\\DTOs\\OperationDTOs.cs", "RelativeDocumentMoniker": "backend\\Models\\DTOs\\OperationDTOs.cs", "ToolTip": "D:\\路网通项目SVN\\1_后台专题类模块\\map-studio\\backend\\Models\\DTOs\\OperationDTOs.cs", "RelativeToolTip": "backend\\Models\\DTOs\\OperationDTOs.cs", "ViewState": "AgIAAAoBAAAAAAAAAAAUwBcBAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-12T07:03:05.798Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "TenantDTOs.cs", "DocumentMoniker": "D:\\路网通项目SVN\\1_后台专题类模块\\map-studio\\backend\\Models\\DTOs\\TenantDTOs.cs", "RelativeDocumentMoniker": "backend\\Models\\DTOs\\TenantDTOs.cs", "ToolTip": "D:\\路网通项目SVN\\1_后台专题类模块\\map-studio\\backend\\Models\\DTOs\\TenantDTOs.cs", "RelativeToolTip": "backend\\Models\\DTOs\\TenantDTOs.cs", "ViewState": "AgIAAEAAAAAAAAAAAAAuwEUAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-12T07:02:46.311Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "OperationController.cs", "DocumentMoniker": "D:\\路网通项目SVN\\1_后台专题类模块\\map-studio\\backend\\Controllers\\OperationController.cs", "RelativeDocumentMoniker": "backend\\Controllers\\OperationController.cs", "ToolTip": "D:\\路网通项目SVN\\1_后台专题类模块\\map-studio\\backend\\Controllers\\OperationController.cs", "RelativeToolTip": "backend\\Controllers\\OperationController.cs", "ViewState": "AgIAAE8BAAAAAAAAAAAIwHUBAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-12T03:43:50.568Z", "EditorCaption": ""}]}]}]}