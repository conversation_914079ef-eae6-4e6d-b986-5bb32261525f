import { test, expect } from '@playwright/test';

test.describe('响应式设计测试', () => {
  const viewports = [
    { name: '桌面端', width: 1920, height: 1080 },
    { name: '笔记本', width: 1366, height: 768 },
    { name: '平板端', width: 768, height: 1024 },
    { name: '手机端', width: 375, height: 667 },
    { name: '小屏手机', width: 320, height: 568 }
  ];

  viewports.forEach(({ name, width, height }) => {
    test(`${name} (${width}x${height}) - 首页应该正常显示`, async ({ page }) => {
      // 设置视口大小
      await page.setViewportSize({ width, height });

      // 访问首页
      await page.goto('/');

      // 等待页面加载
      await page.waitForLoadState('networkidle');

      // 检查主要内容是否可见
      await expect(page.locator('h1')).toBeVisible();

      // 检查页面是否有滚动条（内容没有溢出）
      const bodyOverflow = await page.evaluate(() => {
        const body = document.body;
        const computedStyle = window.getComputedStyle(body);
        return {
          overflowX: computedStyle.overflowX,
          scrollWidth: body.scrollWidth,
          clientWidth: body.clientWidth
        };
      });

      // 确保没有水平滚动条
      expect(bodyOverflow.scrollWidth).toBeLessThanOrEqual(bodyOverflow.clientWidth + 20); // 允许小的误差
    });
  });

  test('桌面端 - 所有组件应该正确布局', async ({ page }) => {
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.goto('/');

    // 检查网格布局
    const gridContainers = page.locator('.grid');
    if (await gridContainers.count() > 0) {
      // 桌面端应该有多列布局
      const gridContainer = gridContainers.first();
      await expect(gridContainer).toBeVisible();

      // 检查是否有合适的列数
      const gridItems = gridContainer.locator('> *');
      const itemCount = await gridItems.count();

      if (itemCount >= 2) {
        // 验证多列布局
        const firstItem = gridItems.first();
        const secondItem = gridItems.nth(1);

        const firstBox = await firstItem.boundingBox();
        const secondBox = await secondItem.boundingBox();

        if (firstBox && secondBox) {
          // 第二个元素应该在第一个元素的右侧（多列）或下方（单列）
          const isMultiColumn = Math.abs(firstBox.y - secondBox.y) < 50; // 大致同一行
          const isSingleColumn = secondBox.y > firstBox.y + firstBox.height - 50; // 明显在下方

          expect(isMultiColumn || isSingleColumn).toBeTruthy();
        }
      }
    }
  });

  test('移动端 - 应该显示移动优化布局', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');

    // 移动端应该是单列布局
    const gridContainers = page.locator('.grid');
    if (await gridContainers.count() > 0) {
      const gridContainer = gridContainers.first();
      const gridItems = gridContainer.locator('> *');

      if (await gridItems.count() >= 2) {
        const firstItem = gridItems.first();
        const secondItem = gridItems.nth(1);

        const firstBox = await firstItem.boundingBox();
        const secondBox = await secondItem.boundingBox();

        if (firstBox && secondBox) {
          // 移动端应该是垂直堆叠
          expect(secondBox.y).toBeGreaterThan(firstBox.y);
        }
      }
    }

    // 检查是否有移动端特定的样式
    const mobileStyles = page.locator('.mobile, .sm\\:, .md\\:hidden');
    // 这些选择器可能需要根据实际的CSS类名调整
  });

  test('平板端 - 应该有适中的布局', async ({ page }) => {
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.goto('/');

    // 检查主要内容是否适合屏幕
    const mainContent = page.locator('main, .main-content, .container');
    if (await mainContent.count() > 0) {
      await expect(mainContent.first()).toBeVisible();

      const contentBox = await mainContent.first().boundingBox();
      if (contentBox) {
        // 内容不应该超出视口宽度
        expect(contentBox.width).toBeLessThanOrEqual(768);
      }
    }
  });

  test('所有视口下文本应该可读', async ({ page }) => {
    for (const { width, height } of viewports) {
      await page.setViewportSize({ width, height });
      await page.goto('/');

      // 检查主标题字体大小
      const mainTitle = page.locator('h1').first();
      if (await mainTitle.count() > 0) {
        const fontSize = await mainTitle.evaluate(el => {
          return window.getComputedStyle(el).fontSize;
        });

        const fontSizeNum = parseInt(fontSize);
        // 字体大小应该至少16px，移动端可能更大
        expect(fontSizeNum).toBeGreaterThanOrEqual(width < 768 ? 18 : 16);
      }

      // 检查正文文本
      const bodyText = page.locator('p').first();
      if (await bodyText.count() > 0) {
        const fontSize = await bodyText.evaluate(el => {
          return window.getComputedStyle(el).fontSize;
        });

        const fontSizeNum = parseInt(fontSize);
        // 正文字体至少14px
        expect(fontSizeNum).toBeGreaterThanOrEqual(14);
      }
    }
  });

  test('触摸目标应该足够大', async ({ page }) => {
    // 移动端测试
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');

    // 检查按钮和链接的大小
    const interactiveElements = page.locator('button, a, input[type="button"], input[type="submit"]');
    const elementCount = await interactiveElements.count();

    for (let i = 0; i < Math.min(elementCount, 10); i++) { // 检查前10个元素
      const element = interactiveElements.nth(i);
      if (await element.isVisible()) {
        const box = await element.boundingBox();
        if (box) {
          // 触摸目标应该至少44x44px（WCAG建议）
          expect(Math.max(box.width, box.height)).toBeGreaterThanOrEqual(44);
        }
      }
    }
  });

  test('图片应该是响应式的', async ({ page }) => {
    await page.goto('/');

    // 检查所有图片
    const images = page.locator('img');
    const imageCount = await images.count();

    for (let i = 0; i < Math.min(imageCount, 5); i++) { // 检查前5张图片
      const img = images.nth(i);
      if (await img.isVisible()) {
        // 检查图片是否有响应式属性
        const maxWidth = await img.evaluate(el => {
          return window.getComputedStyle(el).maxWidth;
        });

        // 图片最大宽度应该是100%或具体的像素值
        expect(maxWidth).toMatch(/100%|\d+px/);
      }
    }
  });

  test('导航在不同屏幕下应该适配', async ({ page }) => {
    // 桌面端 - 应该显示完整导航
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.goto('/');

    // 查找导航元素
    const navigation = page.locator('nav, .navigation, .navbar');
    if (await navigation.count() > 0) {
      await expect(navigation.first()).toBeVisible();
    }

    // 移动端 - 可能有汉堡菜单或折叠导航
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();

    // 检查是否有移动端导航
    const mobileNav = page.locator('.mobile-menu, .hamburger, [aria-label*="menu"], .menu-toggle');

    // 移动端要么显示折叠菜单，要么显示简化版导航
    const hasHamburger = await mobileNav.count() > 0;
    const hasVisibleNav = await navigation.count() > 0 && await navigation.first().isVisible();

    expect(hasHamburger || hasVisibleNav).toBeTruthy();
  });

  test('表格应该在移动端可滚动', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');

    // 查找表格
    const tables = page.locator('table');
    if (await tables.count() > 0) {
      const table = tables.first();
      await expect(table).toBeVisible();

      // 检查表格容器是否可滚动
      const tableContainer = table.locator('xpath=..');
      const overflowX = await tableContainer.evaluate(el => {
        return window.getComputedStyle(el).overflowX;
      });

      // 表格应该可以水平滚动
      expect(['auto', 'scroll']).toContain(overflowX);
    }
  });
});
