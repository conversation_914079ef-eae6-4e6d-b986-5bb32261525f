/**
 * 租户管理服务
 * 处理租户授权申请、审批、进度跟踪等功能
 */

import { httpClient } from '../utils/http.client';
import { API_ENDPOINTS } from '../config/endpoints';
import type {
  AuthApplicationRequest,
  AuthApplicationResponse,
  AuthApplicationListParams,
  AuthApplicationItem,
  AuthApplicationDetail,
  ApprovalRequest,
  ApprovalResponse,
  PaginationResponse,
  AuthApplicationProgessItem,
  AuthApplicationProgessDetail,
  AuthApplicationDestroyItem
} from './types';

export class TenantService {
  /**
   * 提交租户授权申请
   * @param applicationData 申请数据
   */
  static async submitAuthApplication(
    applicationData: AuthApplicationRequest
  ): Promise<AuthApplicationResponse> {
    try {
      console.log('📝 提交租户授权申请...');

      // 验证必填字段
      this.validateApplicationData(applicationData);

      const response = await httpClient.post<AuthApplicationResponse>(
        API_ENDPOINTS.TENANT.AUTH_APPLICATIONS,
        applicationData
      );

      console.log('✅ 申请提交成功:', response.id);
      return response;
    } catch (error: any) {
      console.error('❌ 申请提交失败:', error.message);
      throw new Error(error.message || '申请提交失败，请检查填写的信息');
    }
  }

  /**
   * 获取授权申请列表
   * @param params 查询参数
   */
  static async getAuthApplications(
    params: AuthApplicationListParams
  ): Promise<PaginationResponse<AuthApplicationItem>> {
    try {
      console.log('📋 获取授权申请列表...');

      const queryParams = {
        page: params.page || 1,
        pageSize: params.pageSize || 10,
        ...(params.searchTerm && { searchTerm: params.searchTerm }),
        ...(params.serviceType && { serviceType: params.serviceType }),
        ...(params.authPeriod && { authPeriod: params.authPeriod }),
        ...(params.status && { status: params.status })
      };

      const response = await httpClient.get<PaginationResponse<AuthApplicationItem>>(
        API_ENDPOINTS.TENANT.AUTH_APPLICATIONS,
        { params: queryParams }
      );

      console.log(`✅ 获取申请列表成功: ${response.totalCount} 条记录`);
      return response;
    } catch (error: any) {
      console.error('❌ 获取申请列表失败:', error.message);
      throw new Error(error.message || '获取申请列表失败');
    }
  }

  /**
   * 获取授权申请详情
   * @param applicationId 申请ID
   */
  static async getAuthApplicationDetail(
    applicationId: string
  ): Promise<AuthApplicationDetail> {
    try {
      console.log('🔍 获取申请详情:', applicationId);

      if (!applicationId) {
        throw new Error('申请ID不能为空');
      }

      const response = await httpClient.get<AuthApplicationDetail>(
        API_ENDPOINTS.TENANT.AUTH_APPLICATION_DETAIL(applicationId)
      );
      response.id = applicationId;
      console.log('✅ 获取申请详情成功');
      return response;
    } catch (error: any) {
      console.error('❌ 获取申请详情失败:', error.message);
      throw new Error(error.message || '获取申请详情失败');
    }
  }

  /**
   * 审批授权申请
   * @param applicationId 申请ID
   * @param approvalData 审批数据
   */
  static async approveAuthApplication(
    applicationId: string,
    approvalData: ApprovalRequest
  ): Promise<ApprovalResponse> {
    try {
      console.log(`📋 ${approvalData.action === 'approve' ? '审批通过' : '审批拒绝'}:`, applicationId);

      if (!applicationId) {
        throw new Error('申请ID不能为空');
      }

      if (!approvalData.comments?.trim()) {
        throw new Error('审批意见不能为空');
      }

      const response = await httpClient.put<ApprovalResponse>(
        API_ENDPOINTS.TENANT.APPROVE_APPLICATION(applicationId),
        approvalData
      );

      console.log('✅ 审批操作完成:', response.status);
      return response;
    } catch (error: any) {
      console.error('❌ 审批操作失败:', error.message);
      throw new Error(error.message || '审批操作失败');
    }
  }

  /**
   * 删除/撤销授权申请
   * @param applicationId 申请ID
   */
  static async deleteAuthApplication(applicationId: string): Promise<void> {
    try {
      console.log('🗑️ 删除申请:', applicationId);

      if (!applicationId) {
        throw new Error('申请ID不能为空');
      }

      await httpClient.delete(
        API_ENDPOINTS.TENANT.AUTH_APPLICATION_DETAIL(applicationId)
      );

      console.log('✅ 申请删除成功');
    } catch (error: any) {
      console.error('❌ 申请删除失败:', error.message);
      throw new Error(error.message || '删除申请失败');
    }
  }

  /**
   * 获取服务状态
   * @param tenantId 租户ID（可选）
   */
  static async getServiceStatus(tenantId?: string): Promise<any> {
    try {
      console.log('📊 获取服务状态...');

      const params = tenantId ? { tenantId } : {};

      const response = await httpClient.get(
        API_ENDPOINTS.TENANT.SERVICE_STATUS,
        { params }
      );

      console.log('✅ 获取服务状态成功');
      return response;
    } catch (error: any) {
      console.error('❌ 获取服务状态失败:', error.message);
      throw new Error(error.message || '获取服务状态失败');
    }
  }

  /**
   * 销毁授权
   * @param applicationId 申请ID
   */
  static async destroyAuthorization(applicationId: string): Promise<void> {
    try {
      console.log('💥 销毁授权:', applicationId);

      if (!applicationId) {
        throw new Error('申请ID不能为空');
      }

      await httpClient.post(
        API_ENDPOINTS.TENANT.DESTROY_AUTH(applicationId)
      );

      console.log('✅ 授权销毁成功');
    } catch (error: any) {
      console.error('❌ 授权销毁失败:', error.message);
      throw new Error(error.message || '授权销毁失败');
    }
  }

  /**
   * 验证申请数据
   * @param data 申请数据
   */
  private static validateApplicationData(data: AuthApplicationRequest): void {
    const requiredFields = [
      'tenantName',
      'tenantId',
      // 'contactPerson',
      // 'contactEmail',
      // 'contactPhone',
      'serviceType',
      'authPeriod',
      'permissionScope'
    ];

    for (const field of requiredFields) {
      if (!data[field as keyof AuthApplicationRequest]) {
        throw new Error(`${this.getFieldName(field)}不能为空`);
      }
    }

    // 验证邮箱格式
    // const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    // if (!emailRegex.test(data.contactEmail)) {
    //   throw new Error('联系邮箱格式不正确');
    // }

    // 验证手机号格式
    // const phoneRegex = /^1[3-9]\d{9}$/;
    // if (!phoneRegex.test(data.contactPhone)) {
    //   throw new Error('联系电话格式不正确');
    // }

    // 验证租户ID格式（字母、数字、下划线）
    const tenantIdRegex = /^[A-Z0-9_]{3,20}$/;
    if (!tenantIdRegex.test(data.tenantId)) {
      throw new Error('租户ID只能包含大写字母、数字和下划线，长度3-20位');
    }
  }

  /**
   * 获取字段中文名称
   * @param field 字段名
   */
  private static getFieldName(field: string): string {
    const fieldNames: { [key: string]: string } = {
      tenantName: '租户名称',
      tenantId: '租户ID',
      contactPerson: '联系人',
      contactEmail: '联系邮箱',
      contactPhone: '联系电话',
      serviceType: '服务类型',
      authPeriod: '授权期限',
      permissionScope: '权限范围'
    };

    return fieldNames[field] || field;
  }

  /**
   * 格式化申请状态
   * @param status 状态值
   */
  static formatApplicationStatus(status: string): { text: string; color: string } {
    const statusMap: { [key: string]: { text: string; color: string } } = {
      pending: { text: '待审批', color: '#F7BA1E' },
      approved: { text: '已通过', color: '#00B42A' },
      rejected: { text: '已拒绝', color: '#F53F3F' },
      expired: { text: '已过期', color: '#86909C' }
    };

    return statusMap[status] || { text: '未知状态', color: '#86909C' };
  }

  /**
   * 格式化服务类型
   * @param serviceType 服务类型
   */
  static formatServiceType(serviceType: string): string {
    const typeMap: { [key: string]: string } = {
      basic: '基础地图服务',
      advanced: '高级地图服务',
      enterprise: '企业级地图服务',
      custom: '定制化地图服务'
    };

    return typeMap[serviceType] || serviceType;
  }

  /**
   * 格式化授权期限
   * @param authPeriod 授权期限
   */
  static formatAuthPeriod(authPeriod: string): string {
    const periodMap: { [key: string]: string } = {
      '3months': '3个月',
      '6months': '6个月',
      '1year': '1年',
      '2years': '2年',
      '3years': '3年'
    };

    return periodMap[authPeriod] || authPeriod;
  }

    /**
   * 获取授权进度查询
   * @param tenantId 租户ID（可选）
   */
  static async getAuthProgress(  params: AuthApplicationListParams
  ): Promise<PaginationResponse<AuthApplicationProgessItem>>{
    try {
      console.log('📊 获取授权进度查询...');

      const queryParams = {
        page: params.page || 1,
        pageSize: params.pageSize || 10,
        ...(params.searchTerm && { searchTerm: params.searchTerm }),
        ...(params.serviceType && { serviceType: params.serviceType }),
        ...(params.authPeriod && { authPeriod: params.authPeriod }),
        ...(params.status && { status: params.status })
      };

      const response = await httpClient.get<PaginationResponse<AuthApplicationProgessItem>>(
        API_ENDPOINTS.TENANT.AUTH_PROGRESS,
        { params: queryParams }
      );

      console.log('✅ 获取授权进度成功');
      return response;
    } catch (error: any) {
      console.error('❌ 获取授权进度查询失败:', error.message);
      throw new Error(error.message || '获取授权进度查询失败');
    }
  }

  static async getAuthProgressDetail(  applicationId: string
  ): Promise<AuthApplicationProgessDetail>{
    try {
      console.log('📊 获取授权进度详情...');

      const response = await httpClient.get<AuthApplicationProgessDetail>(
        API_ENDPOINTS.TENANT.AUTH_PROGRESS_DETAIL(applicationId)
      );

      console.log('✅ 获取授权进度详情成功');
      return response;
    } catch (error: any) {
      console.error('❌ 获取授权进度详情失败:', error.message);
      throw new Error(error.message || '获取授权进度详情失败');
    }
  }
  
  static async getAuthDestroyDetail(  
    params: AuthApplicationListParams
  ): Promise<PaginationResponse<AuthApplicationDestroyItem>> {
    try {
      console.log('📋 获取授权申请列表...');

      const queryParams = {
        page: params.page || 1,
        pageSize: params.pageSize || 10,
        ...(params.searchTerm && { searchTerm: params.searchTerm }),
        ...(params.serviceType && { serviceType: params.serviceType }),
        ...(params.authPeriod && { authPeriod: params.authPeriod }),
        ...(params.status && { status: params.status })
      };

      const response = await httpClient.get<PaginationResponse<AuthApplicationDestroyItem>>(
        API_ENDPOINTS.TENANT.DESTROY_AUTH_DETAIL,
        { params: queryParams }
      );

      console.log(`✅ 获取授权销毁列表成功: ${response.totalCount} 条记录`);
      return response;
    } catch (error: any) {
      console.error('❌ 获取授权销毁列表失败:', error.message);
      throw new Error(error.message || '获取授权销毁列表失败');
    }
  }
}
