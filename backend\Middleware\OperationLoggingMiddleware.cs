using MapStudio.Api.Models.Entities;
using MapStudio.Api.Services.Implementations;
using MapStudio.Api.Services.Interfaces;
using MySqlX.XDevAPI.Common;
using SqlSugar;
using System.Diagnostics;
using System.Security.Claims;

namespace MapStudio.Api.Middleware;

public class OperationLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<OperationLoggingMiddleware> _logger;
    private readonly ISqlSugarClient _db;

    public OperationLoggingMiddleware(RequestDelegate next, IServiceProvider serviceProvider, ILogger<OperationLoggingMiddleware> logger, ISqlSugarClient db)
    {
        _next = next;
        _serviceProvider = serviceProvider;
        _logger = logger;
        _db = db;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        var originalResponseBodyStream = context.Response.Body;

        try
        {
            using var responseBody = new MemoryStream();
            context.Response.Body = responseBody;

            await _next(context);

            stopwatch.Stop();

            // 记录操作日志
            await LogOperationAsync(context, stopwatch.ElapsedMilliseconds);

            // 复制响应体到原始流
            responseBody.Seek(0, SeekOrigin.Begin);
            await responseBody.CopyToAsync(originalResponseBodyStream);
        }
        finally
        {
            context.Response.Body = originalResponseBodyStream;
        }
    }

    private async Task LogOperationAsync(HttpContext context, long elapsedMilliseconds)
    {
        // 仅记录API操作，排除静态资源和swagger文档
        if (!context.Request.Path.StartsWithSegments("/api") ||
            context.Request.Path.StartsWithSegments("/api/swagger") ||
            context.Request.Path.StartsWithSegments("/swagger"))
            return;

        // 排除健康检查和心跳接口
        if (context.Request.Path.StartsWithSegments("/health") ||
            context.Request.Path.StartsWithSegments("/ping"))
            return;

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var operationService = scope.ServiceProvider.GetRequiredService<IOperationService>();

            // 优先从JWT token中获取租户ID
            var tenantId = context.User.FindFirst("TenantId")?.Value;
            var userId = context.User.FindFirst("UserId")?.Value;
            // 如果JWT中没有租户信息，尝试从请求头获取
            if (string.IsNullOrEmpty(tenantId))
            {
                tenantId = context.Request.Headers["X-Tenant-ID"].FirstOrDefault();
            }
            // 如果都没有，设置为SYSTEM
            if (string.IsNullOrEmpty(tenantId) || tenantId.Contains("system"))
            {
                tenantId = "SYSTEM";
            }
            else
            {
                var application = await _db.Queryable<AuthApplication>()
                .Where(a => a.Id == tenantId)
                .FirstAsync();

                if (application != null && !string.IsNullOrEmpty(application.TenantName))
                {
                    userId = application.TenantName;
                    tenantId = application.TenantId;
                }

                //userId = context.User.FindFirst("UserId")?.Value;
            }
                
            var actionType = $"{context.Request.Method} {context.Request.Path}";
            var description = $"响应时间: {elapsedMilliseconds}ms, 状态码: {context.Response.StatusCode}";


            // 添加用户信息
            if (context.User.Identity?.IsAuthenticated == true)
            {
                var userName = context.User.FindFirst(ClaimTypes.Name)?.Value;
                if (!string.IsNullOrEmpty(userName))
                {
                    description += $", 用户: {userName}";
                }
            }

            // 添加租户信息到描述
            if (!string.IsNullOrEmpty(tenantId) && tenantId != "SYSTEM")
            {
                var tenantName = context.Request.Headers["X-Tenant-Name"].FirstOrDefault();
                description += $", 租户: {tenantName ?? tenantId}";
            }

            // 添加客户端信息
            var userAgent = context.Request.Headers["User-Agent"].ToString();
            var clientIp = GetClientIpAddress(context);
            if (!string.IsNullOrEmpty(clientIp))
            {
                description += $", IP: {clientIp}";
            }

            await operationService.LogOperationAsync(tenantId, userId, actionType, description);
        }
        catch (Exception ex)
        {
            // 记录日志但不影响主流程
            _logger.LogError(ex, "记录操作日志失败");
        }
    }

    private string? GetClientIpAddress(HttpContext context)
    {
        // 优先获取代理转发的真实IP
        var xForwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(xForwardedFor))
        {
            return xForwardedFor.Split(',').FirstOrDefault()?.Trim();
        }

        var xRealIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(xRealIp))
        {
            return xRealIp;
        }

        // 返回连接的远程IP
        return context.Connection.RemoteIpAddress?.ToString();
    }
}