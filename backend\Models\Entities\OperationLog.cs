using SqlSugar;

namespace MapStudio.Api.Models.Entities
{
    /// <summary>
    /// 操作日志实体，对应数据库表 ms_operation_logs
    /// </summary>
    [SugarTable("ms_operation_logs")]
    public class OperationLog
    {
        /// <summary>
        /// 日志ID (主键)
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, Length = 36)]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 租户ID (外键)
        /// </summary>
        [SugarColumn(Length = 36, IsNullable = false)]
        public string TenantId { get; set; } = string.Empty;

        /// <summary>
        /// 用户ID (外键)
        /// </summary>
        [SugarColumn(Length = 36, IsNullable = true)]
        public string? UserId { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        public string ActionType { get; set; } = string.Empty;

        /// <summary>
        /// 操作描述
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string? Description { get; set; }

        /// <summary>
        /// 操作对象
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = true)]
        public string? ActionObject { get; set; }

        /// <summary>
        /// IP地址
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = true)]
        public string? IpAddress { get; set; }

        /// <summary>
        /// 用户代理
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string? UserAgent { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime OperatedAt { get; set; }

        // 导航属性
        [SugarColumn(IsIgnore = true)]
        public Tenant? Tenant { get; set; }

        [SugarColumn(IsIgnore = true)]
        public User? User { get; set; }
    }
}