#!/bin/bash

# Map Studio 自动化部署脚本
# 支持开发、测试和生产环境的一键部署

set -e

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 函数：打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_step() {
    echo -e "${PURPLE}🔄 $1${NC}"
}

print_header() {
    echo -e "${CYAN}"
    echo "========================================"
    echo "  Map Studio 自动化部署脚本"
    echo "========================================"
    echo -e "${NC}"
}

# 显示使用说明
show_usage() {
    echo "使用方法: $0 [环境] [选项]"
    echo ""
    echo "环境:"
    echo "  dev        开发环境 (默认)"
    echo "  test       测试环境"
    echo "  prod       生产环境"
    echo ""
    echo "选项:"
    echo "  --build    强制重新构建镜像"
    echo "  --clean    清理旧数据和镜像"
    echo "  --logs     显示服务日志"
    echo "  --health   检查服务健康状态"
    echo "  --stop     停止所有服务"
    echo "  --restart  重启所有服务"
    echo "  --help     显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 dev --build     # 构建并启动开发环境"
    echo "  $0 test --clean    # 清理并启动测试环境"
    echo "  $0 prod            # 启动生产环境"
    echo "  $0 --stop          # 停止所有服务"
}

# 检查依赖
check_dependencies() {
    print_step "检查系统依赖..."
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装或未在PATH中"
        print_info "请访问 https://docs.docker.com/get-docker/ 安装 Docker"
        exit 1
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose 未安装或未在PATH中"
        print_info "请访问 https://docs.docker.com/compose/install/ 安装 Docker Compose"
        exit 1
    fi
    
    # 检查 Docker 服务状态
    if ! docker info &> /dev/null; then
        print_error "Docker 服务未运行"
        print_info "请启动 Docker 服务"
        exit 1
    fi
    
    print_success "系统依赖检查完成"
}

# 创建必要的目录和文件
setup_directories() {
    print_step "创建必要的目录结构..."
    
    # 创建数据目录
    mkdir -p data/{mysql,uploads,logs}
    mkdir -p monitoring/{prometheus,grafana/{dashboards,datasources}}
    mkdir -p nginx/{conf.d,ssl}
    mkdir -p scripts
    
    # 设置目录权限
    chmod 755 data/{mysql,uploads,logs}
    
    print_success "目录结构创建完成"
}

# 生成配置文件
generate_configs() {
    print_step "生成配置文件..."
    
    # 生成 .env 文件
    if [ ! -f .env ]; then
        cat > .env << EOF
# Map Studio 环境配置
ENVIRONMENT=${ENVIRONMENT}
MYSQL_ROOT_PASSWORD=mapstudio_password
MYSQL_PASSWORD=mapstudio_user_password
JWT_SECRET_KEY=$(openssl rand -hex 32)
API_BASE_URL=http://localhost:5000/api
FRONTEND_URL=http://localhost:3001
EOF
        print_success "已生成 .env 配置文件"
    fi
    
    # 生成数据库初始化脚本
    if [ ! -f scripts/init-db.sql ]; then
        cat > scripts/init-db.sql << EOF
-- Map Studio 数据库初始化脚本
CREATE DATABASE IF NOT EXISTS mapstudio_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS mapstudio_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS mapstudio_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户并授权
CREATE USER IF NOT EXISTS 'mapstudio'@'%' IDENTIFIED BY 'mapstudio_user_password';
GRANT ALL PRIVILEGES ON mapstudio_dev.* TO 'mapstudio'@'%';
GRANT ALL PRIVILEGES ON mapstudio_test.* TO 'mapstudio'@'%';
GRANT ALL PRIVILEGES ON mapstudio_prod.* TO 'mapstudio'@'%';
FLUSH PRIVILEGES;
EOF
        print_success "已生成数据库初始化脚本"
    fi
    
    # 生成等待数据库脚本
    if [ ! -f scripts/wait-for-db.sh ]; then
        cat > scripts/wait-for-db.sh << 'EOF'
#!/bin/bash
# 等待数据库启动脚本

host="$1"
shift
cmd="$@"

until mysql -h"$host" -uroot -p"$MYSQL_ROOT_PASSWORD" -e 'SELECT 1' >/dev/null 2>&1; do
  echo "等待数据库启动..."
  sleep 2
done

echo "数据库已启动，执行命令: $cmd"
exec $cmd
EOF
        chmod +x scripts/wait-for-db.sh
        print_success "已生成数据库等待脚本"
    fi
}

# 构建镜像
build_images() {
    print_step "构建 Docker 镜像..."
    
    # 选择构建目标
    case $ENVIRONMENT in
        "dev")
            TARGET="development"
            ;;
        "test")
            TARGET="testing"
            ;;
        "prod")
            TARGET="production"
            ;;
    esac
    
    # 构建后端镜像
    print_info "构建后端镜像..."
    docker build -t mapstudio-backend:$ENVIRONMENT \
        --target $TARGET \
        ./backend/
    
    # 构建前端镜像
    print_info "构建前端镜像..."
    docker build -t mapstudio-frontend:$ENVIRONMENT \
        --target $TARGET \
        ./frontend/
    
    print_success "镜像构建完成"
}

# 启动服务
start_services() {
    print_step "启动服务..."
    
    # 根据环境选择 compose 文件和 profile
    case $ENVIRONMENT in
        "dev")
            docker-compose up -d mysql redis backend frontend
            ;;
        "test")
            docker-compose --profile testing up -d mysql-test backend-test
            ;;
        "prod")
            docker-compose --profile production up -d mysql redis backend frontend nginx
            ;;
    esac
    
    print_success "服务启动完成"
}

# 检查健康状态
check_health() {
    print_step "检查服务健康状态..."
    
    # 等待服务启动
    sleep 10
    
    # 检查数据库
    if docker-compose ps mysql | grep -q "Up"; then
        print_success "MySQL 数据库: 运行正常"
    else
        print_error "MySQL 数据库: 未运行"
    fi
    
    # 检查后端
    if curl -f http://localhost:5000/api/system/health &> /dev/null; then
        print_success "后端 API: 运行正常"
    else
        print_warning "后端 API: 启动中或异常"
    fi
    
    # 检查前端
    if curl -f http://localhost:3001 &> /dev/null; then
        print_success "前端应用: 运行正常"
    else
        print_warning "前端应用: 启动中或异常"
    fi
}

# 显示服务信息
show_services_info() {
    print_step "服务访问信息:"
    echo ""
    
    case $ENVIRONMENT in
        "dev"|"test")
            echo "  🌐 前端应用:    http://localhost:3001"
            echo "  🔧 后端 API:    http://localhost:5000"
            echo "  📚 API 文档:    http://localhost:5000/swagger"
            echo "  🗄️  数据库:      localhost:3306"
            echo "  🔴 Redis:       localhost:6379"
            ;;
        "prod")
            echo "  🌐 主站地址:    http://localhost"
            echo "  🔧 API 地址:    http://localhost/api"
            echo "  📊 监控面板:    http://localhost:3000 (如启用)"
            ;;
    esac
    
    echo ""
    print_info "使用 '$0 --logs' 查看服务日志"
    print_info "使用 '$0 --health' 检查服务状态"
    print_info "使用 '$0 --stop' 停止所有服务"
}

# 显示日志
show_logs() {
    print_step "显示服务日志..."
    docker-compose logs -f --tail=100
}

# 停止服务
stop_services() {
    print_step "停止所有服务..."
    docker-compose down
    print_success "服务已停止"
}

# 重启服务
restart_services() {
    print_step "重启服务..."
    stop_services
    start_services
    check_health
    print_success "服务重启完成"
}

# 清理资源
clean_resources() {
    print_step "清理旧资源..."
    
    # 停止并删除容器
    docker-compose down -v --remove-orphans
    
    # 删除旧镜像
    docker images | grep mapstudio | awk '{print $3}' | xargs -r docker rmi -f
    
    # 清理未使用的资源
    docker system prune -f
    
    print_success "资源清理完成"
}

# 运行测试
run_tests() {
    print_step "运行测试..."
    
    # 启动测试环境
    docker-compose --profile testing up -d mysql-test backend-test
    
    # 等待服务启动
    sleep 15
    
    # 运行后端测试
    print_info "运行后端测试..."
    docker-compose exec backend-test dotnet test
    
    # 运行前端测试
    print_info "运行前端测试..."
    cd frontend
    npm run test
    npm run test:e2e
    cd ..
    
    print_success "测试完成"
}

# 备份数据
backup_data() {
    print_step "备份数据..."
    
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p $BACKUP_DIR
    
    # 备份数据库
    docker-compose exec mysql mysqldump -uroot -p$MYSQL_ROOT_PASSWORD --all-databases > $BACKUP_DIR/database.sql
    
    # 备份文件
    cp -r data/uploads $BACKUP_DIR/
    
    print_success "数据备份到: $BACKUP_DIR"
}

# 主函数
main() {
    print_header
    
    # 解析参数
    ENVIRONMENT="dev"
    BUILD_FLAG=false
    CLEAN_FLAG=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            dev|test|prod)
                ENVIRONMENT="$1"
                shift
                ;;
            --build)
                BUILD_FLAG=true
                shift
                ;;
            --clean)
                CLEAN_FLAG=true
                shift
                ;;
            --logs)
                show_logs
                exit 0
                ;;
            --health)
                check_health
                exit 0
                ;;
            --stop)
                stop_services
                exit 0
                ;;
            --restart)
                restart_services
                exit 0
                ;;
            --test)
                run_tests
                exit 0
                ;;
            --backup)
                backup_data
                exit 0
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    print_info "部署环境: $ENVIRONMENT"
    
    # 执行部署流程
    check_dependencies
    setup_directories
    generate_configs
    
    if [ "$CLEAN_FLAG" = true ]; then
        clean_resources
    fi
    
    if [ "$BUILD_FLAG" = true ] || ! docker images | grep -q mapstudio-backend; then
        build_images
    fi
    
    start_services
    check_health
    show_services_info
    
    print_success "🎉 Map Studio 部署完成!"
}

# 捕获中断信号
trap 'print_info "部署被中断"; exit 1' INT TERM

# 执行主函数
main "$@"