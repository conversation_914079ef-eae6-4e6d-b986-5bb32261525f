{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "ConnectionStrings": {"DefaultConnection": "Server=*************;Port=3307;Database=MTNOH_AAA_Platform;Uid=dtauser;Pwd=dtauser;charset=utf8mb4;", "TestConnection": "Server=*************;Port=3307;Database=MTNOH_AAA_Platform_test;Uid=dtauser;Pwd=dtauser;charset=utf8mb4;"}, "JwtSettings": {"Enabled": true, "SecretKey": "MapStudio_Test_SecretKey_For_Integration_Testing_12345678901234567890", "Issuer": "MapStudio.Api.Test", "Audience": "MapStudio.Test", "ExpirationInMinutes": 30}, "CorsSettings": {"AllowedOrigins": ["http://localhost:3001", "http://localhost:3000", "http://127.0.0.1:3001"]}, "FileStorage": {"BasePath": "./test_uploads", "MaxFileSize": 1048576, "AllowedExtensions": [".pdf", ".jpg", ".jpeg", ".png", ".doc", ".docx"]}, "TestSettings": {"AutoSeedData": true, "CleanupAfterTest": true, "MockExternalServices": true, "EnableTestEndpoints": true}, "EmailSettings": {"SmtpServer": "localhost", "SmtpPort": 587, "EnableSsl": false, "Username": "<EMAIL>", "Password": "test_password", "FromEmail": "<EMAIL>", "FromName": "Map Studio Test"}, "ExternalServices": {"MockMode": true, "MapService": {"BaseUrl": "http://localhost:8080/mock-map-service", "ApiKey": "test_api_key"}, "NotificationService": {"BaseUrl": "http://localhost:8081/mock-notification", "ApiKey": "test_notification_key"}}}