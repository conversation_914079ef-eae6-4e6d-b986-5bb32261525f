# MySQL MCP Server Test Script
Write-Host "=========================================" -ForegroundColor Green
Write-Host "MySQL MCP Server Connection Test" -ForegroundColor Green  
Write-Host "=========================================" -ForegroundColor Green
Write-Host ""

Write-Host "[1/4] Testing network connection..." -ForegroundColor Yellow
$pingResult = Test-Connection -ComputerName "*************" -Count 2 -Quiet
if ($pingResult) {
    Write-Host "✓ Network connection successful" -ForegroundColor Green
} else {
    Write-Host "✗ Network connection failed" -ForegroundColor Red
    exit 1
}

Write-Host "[2/4] Testing port 3307..." -ForegroundColor Yellow
$portTest = Test-NetConnection -ComputerName "*************" -Port 3307 -InformationLevel Quiet
if ($portTest) {
    Write-Host "✓ Port 3307 connection successful" -ForegroundColor Green
} else {
    Write-Host "✗ Port 3307 connection failed" -ForegroundColor Red
    exit 1
}

Write-Host "[3/4] Setting environment variables..." -ForegroundColor Yellow
$env:MYSQL_HOST = "*************"
$env:MYSQL_PORT = "3307"
$env:MYSQL_USER = "dtauser"
$env:MYSQL_PASSWORD = "dtauser"
$env:MYSQL_DATABASE = "MTNOH_AAA_Platform"

Write-Host "Environment variables set:" -ForegroundColor Green
Write-Host "  MYSQL_HOST: $env:MYSQL_HOST"
Write-Host "  MYSQL_PORT: $env:MYSQL_PORT"
Write-Host "  MYSQL_USER: $env:MYSQL_USER"
Write-Host "  MYSQL_DATABASE: $env:MYSQL_DATABASE"
Write-Host ""

Write-Host "[4/4] Testing MCP Server startup..." -ForegroundColor Yellow
Write-Host "Starting MCP Server with Alibaba Cloud mirror..." -ForegroundColor Cyan

try {
    # Set timeout for the test
    $process = Start-Process -FilePath "uvx" -ArgumentList "--index-url", "https://mirrors.aliyun.com/pypi/simple/", "mcp-server-mysql" -NoNewWindow -PassThru -RedirectStandardOutput "mcp_output.log" -RedirectStandardError "mcp_error.log"
    
    # Wait a few seconds to see if it starts successfully
    Start-Sleep -Seconds 5
    
    if (!$process.HasExited) {
        Write-Host "✓ MCP Server started successfully!" -ForegroundColor Green
        Write-Host "Server is running in background with PID: $($process.Id)" -ForegroundColor Green
        
        # Stop the process for this test
        $process.Kill()
        Write-Host "Test completed - Server stopped." -ForegroundColor Yellow
    } else {
        Write-Host "✗ MCP Server failed to start" -ForegroundColor Red
        Write-Host "Check mcp_error.log for details" -ForegroundColor Yellow
    }
} catch {
    Write-Host "✗ Error starting MCP Server: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=========================================" -ForegroundColor Green
Write-Host "Test completed!" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green