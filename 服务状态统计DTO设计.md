# 服务状态统计DTO设计

## 概述
本文档描述了服务状态统计功能所需的DTO（数据传输对象）设计，用于在前端和后端之间传输服务状态统计数据。

## DTO定义

### ServiceStatusResponse
这是服务状态统计的主响应DTO，包含所有四个统计指标。

```csharp
public class ServiceStatusResponse
{
    /// <summary>
    /// 总用户数 - 统计ms_auth_applications表的数据量
    /// </summary>
    public int TotalUsers { get; set; }
    
    /// <summary>
    /// 活跃用户数 - 统计ms_operation_logs表的数据，按TenantId字段去重后的数量
    /// </summary>
    public int ActiveUsers { get; set; }
    
    /// <summary>
    /// 今日访问量 - 统计ms_operation_logs表中当天的数据量
    /// </summary>
    public int TodayVisits { get; set; }
    
    /// <summary>
    /// 待审批申请数 - 统计ms_auth_applications表中status=Pending的数据量
    /// </summary>
    public int PendingApplications { get; set; }
}
```

## 实体关系说明

### 总用户数统计
- 数据来源：`ms_auth_applications` 表
- 统计方式：COUNT(*) - 统计所有授权申请记录的数量

### 活跃用户统计
- 数据来源：`ms_operation_logs` 表
- 统计方式：COUNT(DISTINCT TenantId) - 按租户ID去重后统计数量

### 今日访问量统计
- 数据来源：`ms_operation_logs` 表
- 统计方式：COUNT(*) WHERE DATE(OperatedAt) = TODAY - 统计当天操作日志记录数量

### 待审批申请统计
- 数据来源：`ms_auth_applications` 表
- 统计方式：COUNT(*) WHERE Status = 'Pending' - 统计状态为待审批的申请数量

## 使用场景
该DTO将在以下场景中使用：
1. TenantController中的GetServiceStatus端点
2. TenantService中的GetServiceStatusAsync方法实现
3. 前端ServiceStatus.vue页面的数据展示

## 注意事项
1. 所有统计应该在数据库层面完成，避免将大量数据加载到内存中处理
2. 需要考虑性能优化，特别是在数据量大的情况下
3. 统计逻辑应该准确反映业务需求