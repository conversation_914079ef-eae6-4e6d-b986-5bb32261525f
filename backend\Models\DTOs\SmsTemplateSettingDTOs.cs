namespace MapStudio.Api.Models.DTOs
{
    /// <summary>
    /// 短信模板设置DTO
    /// </summary>
    public class SmsTemplateSettingDto
    {
        /// <summary>
        /// 模板ID
        /// </summary>
        public int TempId { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        public string TemplateName { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// 保存短信模板请求
    /// </summary>
    public class SaveSmsTemplateRequestDto
    {
        /// <summary>
        /// 模板ID (更新时提供，新增时为空)
        /// </summary>
        public int? TempId { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        public string TemplateName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 短信模板设置响应
    /// </summary>
    public class SmsTemplateSettingResponse
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 数据
        /// </summary>
        public SmsTemplateSettingDto? Data { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public string? ErrorCode { get; set; }
    }

    /// <summary>
    /// 短信模板设置列表响应
    /// </summary>
    public class SmsTemplateSettingListResponse
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 数据
        /// </summary>
        public List<SmsTemplateSettingDto> Data { get; set; } = new();

        /// <summary>
        /// 错误代码
        /// </summary>
        public string? ErrorCode { get; set; }
    }
}