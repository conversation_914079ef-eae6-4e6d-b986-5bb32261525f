using SqlSugar;

namespace MapStudio.Api.Models.Entities
{
    /// <summary>
    /// 权限范围实体，对应数据库表 ms_permission_scopes
    /// </summary>
    [SugarTable("ms_permission_scopes")]
    public class PermissionScope
    {
        /// <summary>
        /// 权限范围ID (主键)
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, Length = 36)]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 权限范围名称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 权限范围代码
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 权限描述
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string? Description { get; set; }

        /// <summary>
        /// 权限详情 (JSON格式)
        /// </summary>
        [SugarColumn(Length = 2000, IsNullable = true)]
        public string? Permissions { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime UpdatedAt { get; set; }
    }
}