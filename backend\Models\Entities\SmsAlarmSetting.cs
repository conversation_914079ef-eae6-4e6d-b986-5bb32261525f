using SqlSugar;

namespace MapStudio.Api.Models.Entities
{
    /// <summary>
    /// 短信告警设置实体，对应数据库表 ms_sms_alarm_settings
    /// </summary>
    [SugarTable("ms_sms_alarm_settings")]
    public class SmsAlarmSetting
    {
        /// <summary>
        /// 设置ID (主键)
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, Length = 36)]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 短信模板
        /// </summary>
        [SugarColumn(Length = 2000, IsNullable = true)]
        public string? SmsTemplate { get; set; }

        /// <summary>
        /// 手机号列表 (JSON格式)
        /// </summary>
        [SugarColumn(ColumnDataType = "json", IsNullable = true)]
        public string? PhoneList { get; set; }

        /// <summary>
        /// 告警类型
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        public string AlarmType { get; set; } = "sms";

        /// <summary>
        /// 告警条件 (JSON格式)
        /// </summary>
        [SugarColumn(ColumnDataType = "json", IsNullable = true)]
        public string? AlarmConditions { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime UpdatedAt { get; set; }
    }
}