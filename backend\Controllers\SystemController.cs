using Microsoft.AspNetCore.Mvc;
using System.Reflection;
using System.Diagnostics;

namespace MapStudio.Api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SystemController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<SystemController> _logger;

        public SystemController(
            IConfiguration configuration,
            IWebHostEnvironment environment,
            ILogger<SystemController> logger)
        {
            _configuration = configuration;
            _environment = environment;
            _logger = logger;
        }

        /// <summary>
        /// 健康检查接口
        /// </summary>
        [HttpGet("health")]
        public IActionResult Health()
        {
            try
            {
                var healthInfo = new
                {
                    status = "healthy",
                    timestamp = DateTime.UtcNow,
                    environment = _environment.EnvironmentName,
                    version = Assembly.GetExecutingAssembly().GetName().Version?.ToString(),
                    uptime = GetUptime(),
                    checks = new
                    {
                        database = CheckDatabaseConnection(),
                        memory = CheckMemoryUsage(),
                        disk = CheckDiskSpace()
                    }
                };

                return Ok(healthInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "健康检查失败");
                return StatusCode(503, new
                {
                    status = "unhealthy",
                    timestamp = DateTime.UtcNow,
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 简单的健康检查接口（Docker使用）
        /// </summary>
        [HttpGet("ping")]
        public IActionResult Ping()
        {
            return Ok(new
            {
                status = "ok",
                timestamp = DateTime.UtcNow
            });
        }

        /// <summary>
        /// 获取系统信息
        /// </summary>
        [HttpGet("info")]
        public IActionResult GetSystemInfo()
        {
            try
            {
                var systemInfo = new
                {
                    application = new
                    {
                        name = "Map Studio API",
                        version = Assembly.GetExecutingAssembly().GetName().Version?.ToString(),
                        environment = _environment.EnvironmentName,
                        contentRoot = _environment.ContentRootPath,
                        dotnetVersion = Environment.Version.ToString()
                    },
                    server = new
                    {
                        machineName = Environment.MachineName,
                        osVersion = Environment.OSVersion.ToString(),
                        processorCount = Environment.ProcessorCount,
                        workingSet = Environment.WorkingSet,
                        uptime = GetUptime()
                    },
                    timestamp = DateTime.UtcNow
                };

                return Ok(systemInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取系统信息失败");
                return StatusCode(500, new
                {
                    error = "获取系统信息失败",
                    message = ex.Message
                });
            }
        }

        /// <summary>
        /// 检查数据库连接
        /// </summary>
        private object CheckDatabaseConnection()
        {
            try
            {
                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                var isConfigured = !string.IsNullOrEmpty(connectionString);

                return new
                {
                    status = isConfigured ? "configured" : "not_configured",
                    provider = "MySQL",
                    configured = isConfigured
                };
            }
            catch (Exception ex)
            {
                return new
                {
                    status = "error",
                    error = ex.Message
                };
            }
        }

        /// <summary>
        /// 检查内存使用情况
        /// </summary>
        private object CheckMemoryUsage()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                var workingSet = process.WorkingSet64;
                var privateMemory = process.PrivateMemorySize64;

                return new
                {
                    status = "ok",
                    workingSetMB = Math.Round(workingSet / 1024.0 / 1024.0, 2),
                    privateMemoryMB = Math.Round(privateMemory / 1024.0 / 1024.0, 2)
                };
            }
            catch (Exception ex)
            {
                return new
                {
                    status = "error",
                    error = ex.Message
                };
            }
        }

        /// <summary>
        /// 检查磁盘空间
        /// </summary>
        private object CheckDiskSpace()
        {
            try
            {
                var drive = new DriveInfo(Path.GetPathRoot(_environment.ContentRootPath) ?? "C:\\");
                var freeSpaceGB = Math.Round(drive.AvailableFreeSpace / 1024.0 / 1024.0 / 1024.0, 2);
                var totalSpaceGB = Math.Round(drive.TotalSize / 1024.0 / 1024.0 / 1024.0, 2);
                var usedPercentage = Math.Round((1 - (double)drive.AvailableFreeSpace / drive.TotalSize) * 100, 1);

                return new
                {
                    status = usedPercentage > 90 ? "warning" : "ok",
                    freeSpaceGB = freeSpaceGB,
                    totalSpaceGB = totalSpaceGB,
                    usedPercentage = usedPercentage
                };
            }
            catch (Exception ex)
            {
                return new
                {
                    status = "error",
                    error = ex.Message
                };
            }
        }

        /// <summary>
        /// 获取应用程序运行时间
        /// </summary>
        private string GetUptime()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                var uptime = DateTime.Now - process.StartTime;
                return $"{uptime.Days}d {uptime.Hours}h {uptime.Minutes}m {uptime.Seconds}s";
            }
            catch
            {
                return "unknown";
            }
        }
    }
}