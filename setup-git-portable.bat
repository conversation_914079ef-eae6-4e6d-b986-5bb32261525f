@echo off
chcp 65001 >nul
echo === Git移动硬盘配置工具 ===
echo 正在为map-studio项目配置Git环境...
echo.

REM 检查当前全局用户配置
echo [1/4] 检查现有Git配置...
for /f "tokens=*" %%i in ('git config --global user.name 2^>nul') do set GLOBAL_NAME=%%i
for /f "tokens=*" %%i in ('git config --global user.email 2^>nul') do set GLOBAL_EMAIL=%%i

if "%GLOBAL_NAME%"=="" (
    echo    未检测到全局用户配置，设置临时配置...
    git config --global user.name "glau"
    git config --global user.email "<EMAIL>"
    echo    ✓ 已设置临时全局配置
) else (
    echo    检测到现有全局配置: %GLOBAL_NAME% ^<%GLOBAL_EMAIL%^>
    echo    将保持不变，使用项目级配置覆盖
)

REM 设置项目级用户配置（优先级最高）
echo.
echo [2/4] 设置项目专用Git配置...
git config --local user.name "glau"
git config --local user.email "<EMAIL>"
echo    ✓ 项目用户配置: glau ^<<EMAIL>^>

REM 添加当前目录为安全目录
echo.
echo [3/4] 添加安全目录配置...
for /f %%i in ('cd') do set CURRENT_DIR=%%i
git config --global --add safe.directory "%CURRENT_DIR%"
echo    ✓ 安全目录: %CURRENT_DIR%

REM 设置仓库特定配置
echo.
echo [4/4] 优化仓库配置...
git config --local core.autocrlf input
git config --local core.ignorecase false
git config --local core.filemode false
git config --local core.symlinks false
echo    ✓ 跨平台兼容性配置完成

REM 验证最终配置
echo.
echo === 配置验证 ===
echo 生效的用户名: 
for /f "tokens=*" %%i in ('git config user.name 2^>nul') do echo    %%i
echo 生效的邮箱: 
for /f "tokens=*" %%i in ('git config user.email 2^>nul') do echo    %%i
echo.
echo ✅ Git配置完成！现在可以正常提交代码了
echo 💡 提示：移除硬盘时不会影响电脑的全局Git设置
echo.
pause