namespace MapStudio.Api.Models.DTOs;

public class SubmitAuthApplicationRequest
{
    public string TenantName { get; set; } = string.Empty;
    public string TenantId { get; set; } = string.Empty;
    public string ContactPerson { get; set; } = string.Empty;
    public string ContactEmail { get; set; } = string.Empty;
    public string ContactPhone { get; set; } = string.Empty;
    public string ServiceType { get; set; } = string.Empty;
    public string AuthPeriod { get; set; } = string.Empty;
    public string PermissionScope { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? BusinessLicense { get; set; }
    public string? OrganizationCode { get; set; }
}

public class AuthApplicationResponse
{
    public string Id { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime AppliedAt { get; set; }
    public string Message { get; set; } = string.Empty;
}

public class AuthApplicationDto
{
    public string Id { get; set; } = string.Empty;
    public string TenantId { get; set; } = string.Empty;
    public string TenantName { get; set; } = string.Empty;
    public string ApplyTime { get; set; } = string.Empty;
    public string ServiceType { get; set; } = string.Empty;
    public string ServiceTypeName { get; set; } = string.Empty;
    public string AuthPeriod { get; set; } = string.Empty;
    public string AuthPeriodName { get; set; } = string.Empty;
    public string ContactPerson { get; set; } = string.Empty;
    public string ContactPhone { get; set; } = string.Empty;
    public string ContactEmail { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
}

public class AuthApplicationListResponse
{
    public List<AuthApplicationDto> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int CurrentPage { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
}

public class AuthApplicationDetailResponse
{
    public string ApplicationId { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string ApplyTime { get; set; } = string.Empty;
    public ServiceInfoDto ServiceInfo { get; set; } = new();
    public TenantInfoDto TenantInfo { get; set; } = new();
    public List<DocumentDto> Documents { get; set; } = new();
    public List<ApprovalHistoryDto> ApprovalHistory { get; set; } = new();
}

public class ServiceInfoDto
{
    public string TypeName { get; set; } = string.Empty;
    public string AuthPeriodName { get; set; } = string.Empty;
    public string PermissionScopeName { get; set; } = string.Empty;
    public string? Description { get; set; }
}

public class TenantInfoDto
{
    public string Name { get; set; } = string.Empty;
    public string Id { get; set; } = string.Empty;
    public string ContactPerson { get; set; } = string.Empty;
    public string ContactPhone { get; set; } = string.Empty;
    public string ContactEmail { get; set; } = string.Empty;
}

public class DocumentDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string TypeName { get; set; } = string.Empty;
    public string Size { get; set; } = string.Empty;
    public string UploadTime { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
}

public class ApprovalHistoryDto
{
    public string Id { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public string Operator { get; set; } = string.Empty;
    public string OperatorRole { get; set; } = string.Empty;
    public string Time { get; set; } = string.Empty;
    public string? Comment { get; set; }
    public string Status { get; set; } = string.Empty;
}

public class ApprovalRequest
{
    public string Action { get; set; } = string.Empty; // approve 或 reject
    public string? Comments { get; set; }
}

public class ApprovalResponse
{
    public string Id { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime? ApprovedAt { get; set; }
    public ApprovalUserDto? ApprovalUser { get; set; }
    public string Message { get; set; } = string.Empty;
}

public class ApprovalUserDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
}

// 服务状态相关
public class TenantServiceProgressResponse
{
    public List<TenantServiceProgressDto> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int CurrentPage { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
}

public class TenantServiceProgressDto
{
    public string Id { get; set; } = string.Empty;
    public string TenantId { get; set; } = string.Empty;
    public string TenantName { get; set; } = string.Empty;
    public string ApplyTime { get; set; } = string.Empty;
    public string ServiceType { get; set; } = string.Empty;
    public string ServiceTypeName { get; set; } = string.Empty;
    public string AuthPeriod { get; set; } = string.Empty;
    public string AuthPeriodName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string StatusName { get; set; } = string.Empty;
    public int Progress { get; set; } = 0;
    public string currentStep { get; set; } = string.Empty;
    public string ContactPerson { get; set; } = string.Empty;
    public string ContactPhone { get; set; } = string.Empty;
    public string ContactEmail { get; set; } = string.Empty;
    public List<ProgressStep> steps { get; set; } = [];

}

public class ProgressStep
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Time { get; set; }
    public string Status { get; set; } = string.Empty;

    public ProgressStep(string id ,string name, string time, string status)
    {
        this.Id = id;
        this.Name = name;
        this.Time = time;
        this.Status = status;
    }
}


public class AuthDestoryListResponse
{
    public List<TenantServiceDestoryDto> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int CurrentPage { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
}

public class TenantServiceDestoryDto
{
    public string Id { get; set; } = string.Empty;
    public string TenantId { get; set; } = string.Empty;
    public string TenantName { get; set; } = string.Empty;
    public string ApplyTime { get; set; } = string.Empty;
    public string ServiceType { get; set; } = string.Empty;
    public string ServiceTypeName { get; set; } = string.Empty;
    public string AuthStartTime { get; set; } = string.Empty;
    public string AuthEndTime { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string StatusName { get; set; } = string.Empty;
    public string ContactPerson { get; set; } = string.Empty;
    public string ContactPhone { get; set; } = string.Empty;
    public string ContactEmail { get; set; } = string.Empty;

}

public class TenantValidateDto
{
    public string Id { get; set; } = string.Empty;
    public string TenantId { get; set; } = string.Empty;
    public string TenantName { get; set; } = string.Empty;
    public string domain { get; set; } = string.Empty;
}