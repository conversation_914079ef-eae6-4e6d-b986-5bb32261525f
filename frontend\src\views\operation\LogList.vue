<template>
  <div class="p-6">
    <h1 class="text-2xl font-bold mb-6">操作日志列表</h1>
    <div class="bg-white rounded-lg shadow">
      <!-- 筛选条件 -->
      <div class="p-4 border-b border-gray-200">
        <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">租户名称</label>
            <input 
              v-model="filters.tenantName"
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
              placeholder="请输入租户名称"
            >
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">操作类型</label>
            <select 
              v-model="filters.actionType"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">全部类型</option>
              <option value="create">创建</option>
              <option value="update">更新</option>
              <option value="delete">删除</option>
              <option value="query">查询</option>
              <option value="export">导出</option>
              <option value="login">登录</option>
              <option value="logout">登出</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">操作用户</label>
            <input 
              v-model="filters.userName"
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
              placeholder="请输入用户名"
            >
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">操作时间</label>
            <input 
              v-model="filters.operationDate"
              type="date" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
          </div>
          <div class="flex items-end space-x-2">
            <button 
              @click="handleSearch"
              :disabled="loading"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            >
              <i v-if="loading" class="fa-solid fa-spinner fa-spin mr-1"></i>
              查询
            </button>
            <button 
              @click="clearFilters"
              class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              清除
            </button>
          </div>
        </div>
        
        <!-- 错误提示 -->
        <div v-if="error" class="mt-4 bg-red-50 border border-red-200 rounded-md p-3">
          <div class="flex">
            <div class="flex-shrink-0">
              <i class="fa-solid fa-exclamation-circle text-red-400"></i>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">加载失败</h3>
              <div class="mt-1 text-sm text-red-700">
                <p>{{ error }}</p>
              </div>
              <div class="mt-2">
                <button
                  @click="loadOperationLogs"
                  class="bg-red-100 px-2 py-1 rounded text-sm font-medium text-red-800 hover:bg-red-200"
                >
                  重新加载
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 表格 -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">租户</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作类型</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作对象</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">详情</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="log in logList" :key="log.id">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ log.time }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ log.tenant }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ log.user }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span 
                  class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                  :class="{
                    'bg-green-100 text-green-800': log.type === 'create',
                    'bg-blue-100 text-blue-800': log.type === 'update',
                    'bg-red-100 text-red-800': log.type === 'delete',
                    'bg-yellow-100 text-yellow-800': log.type === 'query',
                    'bg-purple-100 text-purple-800': log.type === 'export',
                    'bg-indigo-100 text-indigo-800': log.type === 'login',
                    'bg-gray-100 text-gray-800': log.type === 'logout'
                  }"
                >
                  {{ formatActionType(log.type).text }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ log.object }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <button @click="showDetail(log)" class="text-blue-600 hover:text-blue-900">
                  查看详情
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- 分页 -->
      <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
          <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            上一页
          </a>
          <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            下一页
          </a>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              显示第 <span class="font-medium">{{ (pagination.page - 1) * pagination.pageSize + 1 }}</span> 到 
              <span class="font-medium">{{ Math.min(pagination.page * pagination.pageSize, pagination.totalCount) }}</span> 条记录，
              共 <span class="font-medium">{{ pagination.totalCount }}</span> 条记录
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button 
                @click="handlePageChange(pagination.page - 1)"
                :disabled="pagination.page <= 1"
                class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
              >
                <span class="sr-only">上一页</span>
                <i class="fas fa-chevron-left"></i>
              </button>
              
              <button 
                v-for="page in Math.min(pagination.totalPages, 5)" 
                :key="page"
                @click="handlePageChange(page)"
                :class="{
                  'bg-blue-50 text-blue-600': page === pagination.page,
                  'bg-white text-gray-700 hover:bg-gray-50': page !== pagination.page
                }"
                class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium"
              >
                {{ page }}
              </button>
              
              <button 
                @click="handlePageChange(pagination.page + 1)"
                :disabled="pagination.page >= pagination.totalPages"
                class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
              >
                <span class="sr-only">下一页</span>
                <i class="fas fa-chevron-right"></i>
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { OperationService } from '../../services/operation.service'
import type { OperationLogParams, OperationLogItem, PaginationResponse } from '../../services/types'

interface LogItem {
  id: string
  time: string
  tenant: string
  user: string
  type: 'create' | 'update' | 'delete' | 'query' | 'export' | 'login' | 'logout'
  object: string
  detail: string
}

// 筛选条件
const filters = ref({
  tenantName: '',
  actionType: '',
  userName: '',
  operationDate: ''
})

// 分页参数
const pagination = ref({
  page: 1,
  pageSize: 20,
  totalCount: 0,
  totalPages: 0
})

const logList = ref<LogItem[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

/**
 * 加载操作日志列表
 */
const loadOperationLogs = async () => {
  try {
    loading.value = true
    error.value = null

    const params: OperationLogParams = {
      page: pagination.value.page,
      pageSize: pagination.value.pageSize
    }

    if (filters.value.tenantName) params.tenantName = filters.value.tenantName
    if (filters.value.actionType) params.actionType = filters.value.actionType
    if (filters.value.userName) params.userName = filters.value.userName
    if (filters.value.operationDate) params.operationDate = filters.value.operationDate

    console.log('📋 开始加载操作日志:', params)
    const response: PaginationResponse<OperationLogItem> = await OperationService.getOperationLogs(params)
    
    // 转换数据格式以适配组件
    logList.value = response.items.map(item => ({
      id: item.id,
      time: item.time,
      tenant: item.tenant,
      user: item.user,
      type: item.type as LogItem['type'],
      object: item.object,
      detail: item.detail
    }))
    
    pagination.value.totalCount = response.totalCount
    pagination.value.totalPages = response.totalPages || Math.ceil(response.totalCount / pagination.value.pageSize)
    
    console.log('✅ 操作日志加载成功:', logList.value.length, '条')
  } catch (err: any) {
    console.error('❌ 加载操作日志失败:', err)
    error.value = err.message || '加载数据失败'
    logList.value = []
  } finally {
    loading.value = false
  }
}

/**
 * 查询操作
 */
const handleSearch = () => {
  pagination.value.page = 1 // 重置到第一页
  loadOperationLogs()
}

/**
 * 清除筛选条件
 */
const clearFilters = () => {
  filters.value = {
    tenantName: '',
    actionType: '',
    userName: '',
    operationDate: ''
  }
  handleSearch()
}

/**
 * 翻页
 */
const handlePageChange = (page: number) => {
  pagination.value.page = page
  loadOperationLogs()
}

/**
 * 查看日志详情
 */
const showDetail = async (log: LogItem) => {
  try {
    console.log('🔍 查看日志详情:', log.id)
    const detail = await OperationService.getOperationLogDetail(log.id)
    alert(`日志详情:\n${JSON.stringify(detail, null, 2)}`)
  } catch (err: any) {
    console.error('❌ 获取日志详情失败:', err)
    alert(`获取详情失败: ${err.message}`)
  }
}

/**
 * 格式化操作类型
 */
const formatActionType = (type: string) => {
  return OperationService.formatActionType(type)
}

// 组件挂载时加载数据
onMounted(() => {
  loadOperationLogs()
})
</script>