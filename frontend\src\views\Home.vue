<template>
  <div class="p-6 space-y-8">
    <!-- 页面标题 -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">地图工作室SaaS平台</h1>
        <p class="mt-1 text-gray-500">租户管理与运营中心</p>
      </div>
      <div class="mt-4 md:mt-0 flex items-center space-x-4">
        <!-- API连接状态 -->
        <div class="flex items-center">
          <div
            :class="[
              'w-2 h-2 rounded-full mr-2',
              apiStatus.connected ? 'bg-green-500' : 'bg-red-500'
            ]"
          ></div>
          <span class="text-sm text-gray-600">
            API: {{ apiStatus.connected ? '已连接' : '连接失败' }}
          </span>
          <button
            @click="testApiConnection"
            :disabled="apiStatus.testing"
            class="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 disabled:opacity-50"
          >
            {{ apiStatus.testing ? '测试中...' : '测试' }}
          </button>
        </div>

        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
          <i class="fa-solid fa-calendar mr-2"></i>
          {{ currentDate }}
        </span>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">活跃租户</p>
            <h3 class="mt-1 text-2xl font-bold text-gray-900">128</h3>
            <p class="mt-1 text-xs text-green-500 flex items-center">
              <i class="fa-solid fa-arrow-up mr-1"></i> 12% 较上月
            </p>
          </div>
          <div class="p-3 bg-blue-100 rounded-lg">
            <i class="fa-solid fa-building text-blue-600 text-xl"></i>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">今日访问量</p>
            <h3 class="mt-1 text-2xl font-bold text-gray-900">5,732</h3>
            <p class="mt-1 text-xs text-green-500 flex items-center">
              <i class="fa-solid fa-arrow-up mr-1"></i> 8.5% 较昨日
            </p>
          </div>
          <div class="p-3 bg-purple-100 rounded-lg">
            <i class="fa-solid fa-eye text-purple-600 text-xl"></i>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">待审批申请</p>
            <h3 class="mt-1 text-2xl font-bold text-gray-900">16</h3>
            <p class="mt-1 text-xs text-red-500 flex items-center">
              <i class="fa-solid fa-arrow-up mr-1"></i> 3 较昨日
            </p>
          </div>
          <div class="p-3 bg-yellow-100 rounded-lg">
            <i class="fa-solid fa-clock text-yellow-600 text-xl"></i>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">服务可用性</p>
            <h3 class="mt-1 text-2xl font-bold text-gray-900">99.98%</h3>
            <p class="mt-1 text-xs text-green-500 flex items-center">
              <i class="fa-solid fa-check mr-1"></i> 符合SLA标准
            </p>
          </div>
          <div class="p-3 bg-green-100 rounded-lg">
            <i class="fa-solid fa-shield text-green-600 text-xl"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div class="lg:col-span-2 bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-lg font-semibold text-gray-900">访问趋势 (近6个月)</h2>
          <div class="flex space-x-2">
            <button class="px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded-md">月度</button>
            <button class="px-3 py-1 text-xs font-medium text-gray-500 hover:bg-gray-100 rounded-md">季度</button>
            <button class="px-3 py-1 text-xs font-medium text-gray-500 hover:bg-gray-100 rounded-md">年度</button>
          </div>
        </div>
        <div class="h-80">
          <ChartBase :option="accessTrendChartOptions" :loading="chartLoading" />
        </div>
      </div>

      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">租户服务状态分布</h2>
        <div class="h-64">
          <ChartBase :option="tenantStatusChartOptions" :loading="chartLoading" />
        </div>
        <div class="grid grid-cols-1 gap-3 mt-4">
          <div v-for="(status, index) in tenantStatusData" :key="index" class="flex items-center">
            <div
              class="w-3 h-3 rounded-full mr-2"
              :style="{ backgroundColor: status.color }"
            ></div>
            <span class="text-sm text-gray-700">{{ status.name }}</span>
            <span class="ml-auto text-sm font-medium text-gray-900">{{ status.value }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速访问卡片 -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
      <h2 class="text-lg font-semibold text-gray-900 mb-6">快速访问</h2>
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- 租户管理 -->
        <router-link
          to="/tenant/auth-apply"
          class="p-5 border border-gray-100 rounded-lg hover:border-blue-200 hover:bg-blue-50 transition-colors duration-200"
        >
          <div class="flex items-start">
            <div class="p-3 bg-blue-100 rounded-lg mr-4">
              <i class="fa-solid fa-file-signature text-blue-600"></i>
            </div>
            <div>
              <h3 class="font-medium text-gray-900">租户授权申请</h3>
              <p class="mt-1 text-sm text-gray-500">提交新的租户授权申请</p>
            </div>
          </div>
        </router-link>

        <router-link
          to="/tenant/auth-approval-list"
          class="p-5 border border-gray-100 rounded-lg hover:border-blue-200 hover:bg-blue-50 transition-colors duration-200"
        >
          <div class="flex items-start">
            <div class="p-3 bg-purple-100 rounded-lg mr-4">
              <i class="fa-solid fa-list-check text-purple-600"></i>
            </div>
            <div>
              <h3 class="font-medium text-gray-900">待审批清单</h3>
              <p class="mt-1 text-sm text-gray-500">查看和处理待审批申请</p>
            </div>
          </div>
        </router-link>

        <router-link
          to="/tenant/service-status"
          class="p-5 border border-gray-100 rounded-lg hover:border-blue-200 hover:bg-blue-50 transition-colors duration-200"
        >
          <div class="flex items-start">
            <div class="p-3 bg-green-100 rounded-lg mr-4">
              <i class="fa-solid fa-heartbeat text-green-600"></i>
            </div>
            <div>
              <h3 class="font-medium text-gray-900">服务状态监控</h3>
              <p class="mt-1 text-sm text-gray-500">查看租户服务运行状态</p>
            </div>
          </div>
        </router-link>

        <router-link
          to="/operation/access-stats"
          class="p-5 border border-gray-100 rounded-lg hover:border-blue-200 hover:bg-blue-50 transition-colors duration-200"
        >
          <div class="flex items-start">
            <div class="p-3 bg-orange-100 rounded-lg mr-4">
              <i class="fa-solid fa-chart-line text-orange-600"></i>
            </div>
            <div>
              <h3 class="font-medium text-gray-900">访问统计报表</h3>
              <p class="mt-1 text-sm text-gray-500">查看平台访问统计数据</p>
            </div>
          </div>
        </router-link>
      </div>
    </div>

    <!-- 最近申请 -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-gray-900">最近授权申请</h2>
        <router-link to="/tenant/auth-approval-list" class="text-sm text-blue-600 hover:text-blue-800">
          查看全部 <i class="fa-solid fa-angle-right ml-1"></i>
        </router-link>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请单号</th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">租户名称</th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请日期</th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="application in recentApplicationsData" :key="application.id" class="hover:bg-gray-50">
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">{{ application.id }}</td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">{{ application.name }}</td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">{{ application.date }}</td>
              <td class="px-3 py-4 whitespace-nowrap">
                <span :class="getStatusClass(application.status)">
                  {{ application.status }}
                </span>
              </td>
              <td class="px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
                <router-link
                  :to="`/tenant/auth-approval/${application.id}`"
                  class="text-blue-600 hover:text-blue-900 mr-3"
                >
                  查看详情
                </router-link>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import ChartBase from '@/components/ChartBase.vue'
import { httpClient, apiConfig } from '@/services'

// 图表加载状态
const chartLoading = ref(true)

// API连接状态
const apiStatus = ref({
  connected: false,
  testing: false,
  lastChecked: null as Date | null
})

// 测试API连接
const testApiConnection = async () => {
  apiStatus.value.testing = true

  try {
    console.log('🔍 正在测试API连接...', apiConfig.baseURL)

    // 测试获取服务类型接口
    const response = await httpClient.get('/Metadata/service-types')

    if (response && response.success) {
      apiStatus.value.connected = true
      console.log('✅ API连接成功', response)
    } else {
      apiStatus.value.connected = false
      console.warn('⚠️ API响应异常', response)
    }
  } catch (error) {
    apiStatus.value.connected = false
    console.error('❌ API连接失败:', error)
  } finally {
    apiStatus.value.testing = false
    apiStatus.value.lastChecked = new Date()
  }
}

// 初始化API连接测试
const initApiTest = async () => {
  await testApiConnection()
}

// 模拟数据
const accessTrendData = [
  { name: '1月', value: 1200 },
  { name: '2月', value: 1900 },
  { name: '3月', value: 1500 },
  { name: '4月', value: 2800 },
  { name: '5月', value: 2200 },
  { name: '6月', value: 3500 },
]

const tenantStatusData = [
  { name: '正常运行', value: 85, color: '#00B42A' },
  { name: '服务降级', value: 10, color: '#FF7D00' },
  { name: '已停用', value: 5, color: '#F53F3F' },
]

const recentApplicationsData = [
  { id: 'TA-20250801', name: '智慧城市科技有限公司', date: '2025-08-01', status: '已通过' },
  { id: 'TA-20250802', name: '未来交通研究院', date: '2025-08-02', status: '待审批' },
  { id: 'TA-20250803', name: '绿色能源集团', date: '2025-08-03', status: '审批中' },
]

// 图表配置
const accessTrendChartOptions = {
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: accessTrendData.map(item => item.name),
    axisLine: {
      lineStyle: {
        color: '#f0f0f0'
      }
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      color: '#9ca3af',
      fontSize: 12
    }
  },
  yAxis: {
    type: 'value',
    axisLine: {
      show: false
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      color: '#9ca3af',
      fontSize: 12
    },
    splitLine: {
      lineStyle: {
        color: '#f0f0f0',
        type: 'dashed'
      }
    }
  },
  series: [{
    data: accessTrendData.map(item => item.value),
    type: 'line',
    smooth: true,
    lineStyle: {
      color: '#165DFF',
      width: 2
    },
    itemStyle: {
      color: '#165DFF'
    },
    areaStyle: {
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [{
          offset: 0, color: 'rgba(22, 93, 255, 0.1)'
        }, {
          offset: 1, color: 'rgba(22, 93, 255, 0)'
        }]
      }
    }
  }],
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderColor: '#e5e7eb',
    borderWidth: 1,
    borderRadius: 8,
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
    textStyle: {
      color: '#374151'
    }
  }
}

const tenantStatusChartOptions = {
  series: [{
    type: 'pie',
    radius: ['60%', '80%'],
    center: ['50%', '50%'],
    data: tenantStatusData.map(item => ({
      value: item.value,
      name: item.name,
      itemStyle: {
        color: item.color
      }
    })),
    label: {
      show: false
    },
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    }
  }],
  tooltip: {
    trigger: 'item',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderColor: '#e5e7eb',
    borderWidth: 1,
    borderRadius: 8,
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
    textStyle: {
      color: '#374151'
    },
    formatter: '{b}: {c}% ({d}%)'
  }
}

// 组件挂载
onMounted(async () => {
  // 初始化API连接测试
  await initApiTest()

  // 模拟数据加载
  setTimeout(() => {
    chartLoading.value = false
  }, 500)
})

// 计算属性
const currentDate = computed(() => {
  return new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
})

// 获取状态样式类
const getStatusClass = (status: string) => {
  const baseClass = 'px-2 py-1 text-xs rounded-full '
  switch (status) {
    case '已通过':
      return baseClass + 'bg-green-100 text-green-800'
    case '待审批':
      return baseClass + 'bg-yellow-100 text-yellow-800'
    case '审批中':
      return baseClass + 'bg-blue-100 text-blue-800'
    default:
      return baseClass + 'bg-gray-100 text-gray-800'
  }
}
</script>
