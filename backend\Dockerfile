# Map Studio 后端 API Dockerfile
# 支持开发、测试和生产环境的多阶段构建

# 基础镜像
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 5000

# 安装必要的工具
RUN apt-get update && apt-get install -y \
    curl \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# SDK镜像用于构建
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# 复制项目文件并恢复依赖
COPY MapStudio.Api.csproj .
RUN dotnet restore

# 复制源代码
COPY . .

# 构建应用
RUN dotnet build "MapStudio.Api.csproj" -c Release -o /app/build

# 发布应用
FROM build AS publish
RUN dotnet publish "MapStudio.Api.csproj" -c Release -o /app/publish /p:UseAppHost=false

# 开发环境镜像
FROM base AS development
WORKDIR /app

# 安装开发工具
RUN apt-get update && apt-get install -y \
    git \
    vim \
    htop \
    && rm -rf /var/lib/apt/lists/*

# 创建必要的目录
RUN mkdir -p /app/uploads /app/logs /app/temp

# 复制应用文件
COPY --from=publish /app/publish .

# 设置权限
RUN chmod +x /app/MapStudio.Api.dll && \
    chown -R app:app /app

# 创建非root用户
RUN groupadd -r app && useradd -r -g app app

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5000/api/system/health || exit 1

USER app

ENTRYPOINT ["dotnet", "MapStudio.Api.dll"]

# 测试环境镜像
FROM development AS testing
USER root

# 安装测试工具
RUN apt-get update && apt-get install -y \
    mysql-client \
    jq \
    && rm -rf /var/lib/apt/lists/*

# 复制测试脚本
COPY scripts/wait-for-db.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/wait-for-db.sh

# 设置测试环境变量
ENV ASPNETCORE_ENVIRONMENT=Testing
ENV DOTNET_ENVIRONMENT=Testing

USER app

# 测试启动命令
CMD ["sh", "-c", "wait-for-db.sh mysql-test:3306 -- dotnet MapStudio.Api.dll"]

# 生产环境镜像
FROM base AS production
WORKDIR /app

# 创建必要的目录
RUN mkdir -p /app/uploads /app/logs

# 复制发布的应用
COPY --from=publish /app/publish .

# 创建非root用户
RUN groupadd -r app && useradd -r -g app app

# 设置权限
RUN chown -R app:app /app

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5000/api/system/health || exit 1

# 性能优化配置
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=1
ENV DOTNET_USE_POLLING_FILE_WATCHER=1
ENV ASPNETCORE_URLS=http://+:5000

USER app

ENTRYPOINT ["dotnet", "MapStudio.Api.dll"]