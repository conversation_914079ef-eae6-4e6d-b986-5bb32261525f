<template>
  <div class="p-6 space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">授权销毁</h1>
      <p class="mt-1 text-gray-500">销毁已授权租户的服务权限</p>
    </div>

    <!-- 搜索和筛选栏 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      
        <div class="relative">
          <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <i class="fa-solid fa-search text-gray-400"></i>
          </div>
          <input
            type="text"
            placeholder="搜索租户名称或租户ID..."
            v-model="searchTerm"
            @input="handleSearchChange"
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
        
        <!-- <div>
          <select
            name="status"
            v-model="filters.status"
            @change="handleFilterChange"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option v-for="option in statusOptions" :key="option.id" :value="option.id">
              {{ option.name }}
            </option>
          </select>
        </div> -->
        
        <div>
          <select
            name="serviceType"
            v-model="filters.serviceType"
            @change="handleFilterChange"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option v-for="option in serviceTypeOptions" :key="option.id" :value="option.id">
              {{ option.name }}
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                租户ID
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                租户名称
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                权限类型
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                授权时间
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                过期时间
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                联系人
              </th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-if="paginatedTenants.length > 0" v-for="tenant in paginatedTenants" :key="tenant.id" class="hover:bg-gray-50 transition-colors">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ tenant.id }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ tenant.tenantName }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                  {{ tenant.serviceTypeName }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500">{{ tenant.authStartTime }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500">{{ tenant.authEndTime }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span 
                  class="px-2 py-1 text-xs font-medium rounded-full"
                  :class="getStatusBadgeClass(tenant.status)"
                >
                  {{ tenant.statusName }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ tenant.contactPerson }}</div>
                <div class="text-sm text-gray-500">{{ tenant.contactPhone }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button
                  @click="handleDestroy(tenant.id, tenant.tenantName)"
                  class="text-red-600 hover:text-red-900"
                >
                  销毁授权
                </button>
              </td>
            </tr>
            <tr v-else>
              <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                暂无数据
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- 分页 -->
      <div v-if="paginatedTenants.length > 0" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
          <button
            @click="prevPage"
            :disabled="pagination.page === 1"
            class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            上一页
          </button>
          <button
            @click="nextPage"
            :disabled="pagination.page === Math.ceil(filteredTenants.length / pagination.pageSize)"
            class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            下一页
          </button>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              显示第 <span class="font-medium">{{ (pagination.page - 1) * pagination.pageSize + 1 }}</span> 到 <span class="font-medium">{{ Math.min(pagination.page * pagination.pageSize, filteredTenants.length) }}</span> 条结果，共 <span class="font-medium">{{ filteredTenants.length }}</span> 条
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                @click="prevPage"
                :disabled="pagination.page === 1"
                class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
              >
                <span class="sr-only">上一页</span>
                <i class="fa-solid fa-chevron-left"></i>
              </button>
              <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                第 {{ pagination.page }} 页，共 {{ Math.ceil(filteredTenants.length / pagination.pageSize) }} 页
              </span>
              <button
                @click="nextPage"
                :disabled="pagination.page === Math.ceil(filteredTenants.length / pagination.pageSize)"
                class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
              >
                <span class="sr-only">下一页</span>
                <i class="fa-solid fa-chevron-right"></i>
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 销毁确认对话框 -->
    <div v-if="destroyConfirm" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">确认销毁授权</h3>
        </div>
        <div class="px-6 py-4">
          <p class="text-gray-700">
            确定要销毁租户 <span class="font-medium text-red-600">{{ destroyConfirm.name }}</span> 的服务授权吗？
            此操作不可撤销，销毁后该租户将无法继续使用相关服务。
          </p>
        </div>
        <div class="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
          <button
            @click="cancelDestroy"
            :disabled="isProcessing"
            class="px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            取消
          </button>
          <button
            @click="confirmDestroy"
            :disabled="isProcessing"
            class="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
          >
            <template v-if="isProcessing">
              <i class="fa-solid fa-spinner fa-spin mr-2"></i>
              销毁中...
            </template>
            <template v-else>
              确认销毁
            </template>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue'
import { useToast } from '@/composables/useToast'
import { TenantService, type AuthApplicationDestroyItem } from '@/services';

// 在响应式数据中添加
const isLoading = ref(false)

// 模拟已授权的租户数据
const authorizedTenants = ref<AuthApplicationDestroyItem[]>([])
const loadauthorizedTenants = async () => {
  
    isLoading.value = true;

    try {
          const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      searchTerm: searchTerm.value || undefined,
      status: filters.status || undefined, // 修复：传递实际的筛选条件
      serviceType: filters.serviceType || undefined // 添加服务类型筛选
    };

    console.log('📋 加载已授权的租户...', params);

    const result = await TenantService.getAuthDestroyDetail(params);
    console.log('📋 加载已授权的租户成功:', result.items);
    authorizedTenants.value = result.items; // 正确：使用.value来修改ref的值
    pagination.total = result.items.length;
  } catch (error) {
    console.error('获取申请详情失败:', error);
  } finally {
    isLoading.value = false;
  }
}

onMounted(() => {
  loadauthorizedTenants();
});
// const authorizedTenants = [
//   {
//     id: 'TENANT-2025001',
//     tenantName: '智慧城市科技有限公司',
//     serviceType: 'enterprise',
//     serviceTypeName: '企业级地图服务',
//     authStartTime: '2025-01-15 09:00:00',
//     authEndTime: '2026-01-15 09:00:00',
//     status: 'active',
//     statusName: '服务中',
//     contactPerson: '张三',
//     contactPhone: '13800138000',
//     contactEmail: '<EMAIL>',
//   },
//   {
//     id: 'TENANT-2025002',
//     tenantName: '未来交通研究院',
//     serviceType: 'advanced',
//     serviceTypeName: '高级地图服务',
//     authStartTime: '2025-03-22 14:30:00',
//     authEndTime: '2027-03-22 14:30:00',
//     status: 'active',
//     statusName: '服务中',
//     contactPerson: '李四',
//     contactPhone: '13900139000',
//     contactEmail: '<EMAIL>',
//   },
//   {
//     id: 'TENANT-2025003',
//     tenantName: '绿色能源集团',
//     serviceType: 'basic',
//     serviceTypeName: '基础地图服务',
//     authStartTime: '2025-05-10 10:15:00',
//     authEndTime: '2025-08-10 10:15:00',
//     status: 'expiring',
//     statusName: '即将过期',
//     contactPerson: '王五',
//     contactPhone: '13700137000',
//     contactEmail: '<EMAIL>',
//   },
//   {
//     id: 'TENANT-2025004',
//     tenantName: '数字农业科技有限公司',
//     serviceType: 'custom',
//     serviceTypeName: '定制化地图服务',
//     authStartTime: '2024-12-01 16:45:00',
//     authEndTime: '2025-12-01 16:45:00',
//     status: 'active',
//     statusName: '服务中',
//     contactPerson: '赵六',
//     contactPhone: '13600136000',
//     contactEmail: '<EMAIL>',
//   },
//   {
//     id: 'TENANT-2025005',
//     tenantName: '智能物流股份有限公司',
//     serviceType: 'enterprise',
//     serviceTypeName: '企业级地图服务',
//     authStartTime: '2025-02-18 11:20:00',
//     authEndTime: '2026-02-18 11:20:00',
//     status: 'active',
//     statusName: '服务中',
//     contactPerson: '钱七',
//     contactPhone: '13500135000',
//     contactEmail: '<EMAIL>',
//   },
// ]

// 服务状态选项
const statusOptions = [
  { id: '', name: '全部状态' },
  { id: 'active', name: '服务中' },
  { id: 'expiring', name: '即将过期' },
   { id: 'expired', name: '已过期' },
]

// 服务类型选项
const serviceTypeOptions = [
  { id: '', name: '全部权限类型' },
  { id: 'basic', name: '基础地图服务' },
  { id: 'advanced', name: '高级地图服务' },
  { id: 'enterprise', name: '企业级地图服务' },
  { id: 'custom', name: '定制化地图服务' },
]

// 响应式数据
const searchTerm = ref('')
const filters = reactive({
  status: '',
  serviceType: '',
})

const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
})

const destroyConfirm = ref<{id: string, name: string} | null>(null)
const isProcessing = ref(false)

// 处理搜索变化
const handleSearchChange = () => {
  pagination.page = 1 // 重置页码
}

// 处理筛选变化
const handleFilterChange = () => {
  pagination.page = 1 // 重置页码
  loadauthorizedTenants() // 重新加载数据
}

// 过滤数据
const filteredTenants = computed(() => {
  return authorizedTenants.value.filter(tenant => {
    // 搜索过滤
    const matchesSearch = searchTerm.value === '' || 
      tenant.tenantName.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
      tenant.id.toLowerCase().includes(searchTerm.value.toLowerCase())
    
    // 状态过滤
    const matchesStatus = filters.status === '' || tenant.status === filters.status
    
    // 服务类型过滤
    const matchesServiceType = filters.serviceType === '' || tenant.serviceType === filters.serviceType
    
    return matchesSearch && matchesStatus && matchesServiceType
  })
})

// 计算分页数据
const paginatedTenants = computed(() => {
  const start = (pagination.page - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return filteredTenants.value.slice(start, end)
})

// 获取状态样式
const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'active':
      return 'bg-green-100 text-green-800'
    case 'expiring':
      return 'bg-yellow-100 text-yellow-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 处理销毁操作
const handleDestroy = (id: string, name: string) => {
  destroyConfirm.value = { id, name }
}

// 获取toast实例
const toast = useToast()

// 确认销毁
const confirmDestroy = async () => {
  if (!destroyConfirm.value) return
  
  // 检查是否正在处理中
  if (isProcessing.value) return;
  
  isProcessing.value = true
  
  try {
    // 调用删除API
    const result = await TenantService.destroyAuthorization(destroyConfirm.value!.id);
    console.log('销毁授权API响应:', result);
    
    // 删除成功后刷新数据
    await loadauthorizedTenants();
    
    toast.success('授权销毁成功', {
      description: `租户 ${destroyConfirm.value!.name} 的授权已成功销毁。`
    })
    
    // 关闭确认对话框
    destroyConfirm.value = null
  } catch (ex) {
    console.error('销毁授权失败:', ex);
    
    // 根据错误类型提供更具体的错误信息
    let errorMessage = '无法销毁授权，请稍后重试。';
    
    toast.error('授权销毁失败', {
      description: errorMessage
    })
    
  } finally {
    isProcessing.value = false;
  }
}

// 取消销毁
const cancelDestroy = () => {
  destroyConfirm.value = null
}

// 分页控制
const prevPage = () => {
  if (pagination.page > 1) {
    pagination.page--
  }
}

const nextPage = () => {
  if (pagination.page < Math.ceil(filteredTenants.value.length / pagination.pageSize)) {
    pagination.page++
  }
}
</script>