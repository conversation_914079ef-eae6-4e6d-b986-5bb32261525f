# Service Status 使用率（近6个月）图表需求分析

## 1. 当前实现分析

### 1.1 前端实现现状

在 [`ServiceStatus.vue`](frontend/src/views/tenant/ServiceStatus.vue) 文件中，使用率图表的实现方式如下：

- **图表组件**：使用 [`ChartBase`](frontend/src/views/tenant/ServiceStatus.vue:108) 组件展示趋势图
- **数据结构**：当前使用模拟数据 [`accessTrendData`](frontend/src/views/tenant/ServiceStatus.vue:248-255)
- **维度切换**：提供了月度、季度、年度三个维度的切换按钮（第102-104行）
- **图表配置**：通过 [`trendChartOption`](frontend/src/views/tenant/ServiceStatus.vue:295-369) 计算属性配置图表样式和数据

### 1.2 后端数据获取现状

当前后端通过以下方式获取服务状态统计数据：

- **API端点**：[`/operation/service-status-stats`](frontend/src/config/endpoints.ts:43)
- **服务方法**：[`OperationService.GetServiceStatusStatsAsync()`](backend/Services/Implementations/OperationService.cs:269-314)
- **数据源**：主要从 [`ms_operation_logs`](backend/Models/Entities/OperationLog.cs) 表获取操作日志数据
- **统计指标**：总用户数、活跃用户、今日访问量、待审批申请

### 1.3 当前数据结构

#### 前端数据结构
```typescript
// 模拟数据结构
interface AccessTrendData {
  name: string;    // 月份名称，如 "1月", "2月" 等
  value: number;   // 访问量数值
}
```

#### 后端数据结构
```csharp
// 服务状态统计数据传输对象
public class ServiceStatusStatsDto
{
    public int TotalUsers { get; set; }          // 总用户数
    public int ActiveUsers { get; set; }          // 活跃用户
    public int TodayVisits { get; set; }          // 今日访问量
    public int PendingApplications { get; set; } // 待审批申请
}
```

## 2. 需求分析

### 2.1 统计逻辑要求

根据用户需求，需要实现以下统计逻辑：

1. **横坐标**：当前月份往前推5个月，包括本月，显示6个月的数据统计
2. **纵坐标**：按当前日期近6个月份汇聚统计ms_operation_logs表的数据总量
3. **维度简化**：去掉月度、季度、年度维度统计，只保留近6个月的数据展示

### 2.2 数据源分析

- **主要数据表**：[`ms_operation_logs`](backend/Models/Entities/OperationLog.cs)
- **关键字段**：
  - `OperatedAt`：操作时间，用于按月份分组统计
  - `TenantId`：租户ID，可用于区分不同租户的操作
  - `ActionType`：操作类型，可用于区分不同类型的操作

### 2.3 日期处理逻辑

需要实现以下日期处理逻辑：

1. **计算近6个月范围**：
   - 获取当前日期
   - 计算当前月份
   - 向前推算5个月，形成6个月的月份范围

2. **月份标签格式化**：
   - 将月份格式化为中文显示，如 "4月", "5月" 等
   - 或使用 "YYYY-MM" 格式，如 "2025-04", "2025-05" 等

## 3. 数据结构设计

### 3.1 后端API数据结构

#### 新增DTO：月度使用率统计
```csharp
/// <summary>
/// 月度使用率统计数据传输对象
/// </summary>
public class MonthlyUsageStatsDto
{
    /// <summary>
    /// 月份标签（格式：YYYY-MM 或 中文月份名称）
    /// </summary>
    public string Month { get; set; } = string.Empty;

    /// <summary>
    /// 该月的操作总量
    /// </summary>
    public int OperationCount { get; set; }

    /// <summary>
    /// 该月的活跃租户数（可选）
    /// </summary>
    public int ActiveTenants { get; set; }

    /// <summary>
    /// 环比增长率（可选）
    /// </summary>
    public double GrowthRate { get; set; }
}

/// <summary>
/// 近6个月使用率统计响应数据传输对象
/// </summary>
public class SixMonthUsageStatsResponse
{
    /// <summary>
    /// 近6个月的使用率统计数据
    /// </summary>
    public List<MonthlyUsageStatsDto> MonthlyData { get; set; } = new();

    /// <summary>
    /// 统计概述信息
    /// </summary>
    public SixMonthUsageOverviewDto Overview { get; set; } = new();
}

/// <summary>
/// 近6个月使用率统计概述数据传输对象
/// </summary>
public class SixMonthUsageOverviewDto
{
    /// <summary>
    /// 6个月总操作量
    /// </summary>
    public int TotalOperations { get; set; }

    /// <summary>
    /// 月均操作量
    /// </summary>
    public double AverageMonthlyOperations { get; set; }

    /// <summary>
    /// 最高月操作量
    /// </summary>
    public int MaxMonthlyOperations { get; set; }

    /// <summary>
    /// 最高操作量月份
    /// </summary>
    public string MaxMonth { get; set; } = string.Empty;

    /// <summary>
    /// 整体增长率
    /// </summary>
    public double OverallGrowthRate { get; set; }
}
```

### 3.2 前端数据结构

```typescript
/// 月度使用率数据接口
interface MonthlyUsageData {
  month: string;        // 月份标签
  value: number;         // 操作量数值
  activeTenants?: number; // 活跃租户数（可选）
  growthRate?: number;   // 环比增长率（可选）
}

/// 近6个月使用率统计响应接口
interface SixMonthUsageStatsResponse {
  monthlyData: MonthlyUsageData[];
  overview: {
    totalOperations: number;
    averageMonthlyOperations: number;
    maxMonthlyOperations: number;
    maxMonth: string;
    overallGrowthRate: number;
  };
}
```

## 4. API端点设计

### 4.1 新增API端点

在 [`API_ENDPOINTS.OPERATION`](frontend/src/config/endpoints.ts:35-57) 中新增：

```typescript
// 在 OPERATION 对象中添加
SIX_MONTH_USAGE_STATS: '/operation/six-month-usage-stats',
```

### 4.2 后端控制器方法

在 [`OperationController`](backend/Controllers/OperationController.cs) 中新增：

```csharp
/// <summary>
/// 获取近6个月使用率统计数据
/// </summary>
[HttpGet("six-month-usage-stats")]
public async Task<ActionResult<ApiResponse<SixMonthUsageStatsResponse>>> GetSixMonthUsageStats()
{
    try
    {
        var result = await _operationService.GetSixMonthUsageStatsAsync();
        return Ok(ApiResponse<SixMonthUsageStatsResponse>.SuccessResult(result, "获取近6个月使用率统计成功"));
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "获取近6个月使用率统计失败");
        return StatusCode(500, ApiResponse<SixMonthUsageStatsResponse>.ErrorResult("服务器内部错误", "INTERNAL_ERROR"));
    }
}
```

## 5. 实现逻辑设计

### 5.1 后端统计逻辑

```csharp
public async Task<SixMonthUsageStatsResponse> GetSixMonthUsageStatsAsync()
{
    try
    {
        // 1. 计算近6个月的月份范围
        var currentDate = DateTime.Now;
        var months = new List<string>();
        var monthlyData = new List<MonthlyUsageStatsDto>();
        
        for (int i = 5; i >= 0; i--)
        {
            var targetDate = currentDate.AddMonths(-i);
            var monthKey = targetDate.ToString("yyyy-MM");
            months.Add(monthKey);
            
            // 2. 统计该月的操作总量
            var startDate = new DateTime(targetDate.Year, targetDate.Month, 1);
            var endDate = startDate.AddMonths(1);
            
            var operationCount = await _db.Queryable<OperationLog>()
                .Where(log => log.OperatedAt >= startDate && log.OperatedAt < endDate)
                .CountAsync();
            
            // 3. 统计该月的活跃租户数
            var activeTenants = await _db.Queryable<OperationLog>()
                .Where(log => log.OperatedAt >= startDate && log.OperatedAt < endDate)
                .GroupBy(log => log.TenantId)
                .Select(log => log.TenantId)
                .CountAsync();
            
            monthlyData.Add(new MonthlyUsageStatsDto
            {
                Month = targetDate.ToString("MM月"),  // 格式化为 "04月", "05月" 等
                OperationCount = operationCount,
                ActiveTenants = activeTenants
            });
        }
        
        // 4. 计算环比增长率和概述数据
        // ... 实现概述数据计算逻辑
        
        return new SixMonthUsageStatsResponse
        {
            MonthlyData = monthlyData,
            Overview = new SixMonthUsageOverviewDto
            {
                // ... 填充概述数据
            }
        };
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "获取近6个月使用率统计失败");
        throw;
    }
}
```

### 5.2 前端数据获取逻辑

```typescript
// 在 OperationService 中新增方法
static async getSixMonthUsageStats(): Promise<SixMonthUsageStatsResponse> {
  try {
    console.log('📊 获取近6个月使用率统计数据...');
    
    const response = await httpClient.get<SixMonthUsageStatsResponse>(
      API_ENDPOINTS.OPERATION.SIX_MONTH_USAGE_STATS
    );
    
    console.log('✅ 获取近6个月使用率统计成功');
    return response;
  } catch (error: any) {
    console.error('❌ 获取近6个月使用率统计失败:', error.message);
    throw new Error(error.message || '获取近6个月使用率统计数据失败');
  }
}
```

### 5.3 前端图表数据适配

```typescript
// 在 ServiceStatus.vue 中修改数据获取和图表配置
const sixMonthUsageData = ref<MonthlyUsageData[]>([]);

const loadSixMonthUsageStats = async () => {
  try {
    const response = await OperationService.getSixMonthUsageStats();
    sixMonthUsageData.value = response.monthlyData;
    
    // 更新图表配置
    updateTrendChart();
  } catch (error: any) {
    console.error('获取近6个月使用率统计失败:', error);
  }
};

const updateTrendChart = () => {
  // 使用 sixMonthUsageData 更新图表配置
  trendChartOption.value = {
    // ... 图表配置，使用 sixMonthUsageData 作为数据源
  };
};
```

## 6. 前端UI调整建议

### 6.1 移除维度切换按钮

由于需求中去掉了月度、季度、年度维度统计，建议移除以下代码：

```vue
<!-- 移除这部分代码 -->
<div class="flex space-x-2">
  <button class="px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded-md">月度</button>
  <button class="px-3 py-1 text-xs font-medium text-gray-500 hover:bg-gray-100 rounded-md">季度</button>
  <button class="px-3 py-1 text-xs font-medium text-gray-500 hover:bg-gray-100 rounded-md">年度</button>
</div>
```

### 6.2 更新图表标题

将图表标题从"使用率 (近6个月)"调整为更具体的描述：

```vue
<h2 class="text-lg font-semibold text-gray-900">近6个月操作量趋势</h2>
```

## 7. 总结

通过以上分析，我们明确了service-status页面使用率（近6个月）图表的需求和数据结构设计：

1. **数据源**：从 [`ms_operation_logs`](backend/Models/Entities/OperationLog.cs) 表按月份统计操作量
2. **时间范围**：当前月份往前推5个月，共6个月的数据
3. **数据结构**：设计了完整的后端DTO和前端接口，支持月度操作量、活跃租户数等指标
4. **API设计**：新增专门的API端点获取近6个月使用率统计数据
5. **实现逻辑**：提供了详细的后端统计逻辑和前端数据获取逻辑

这个设计方案满足了用户需求，去掉了不必要的维度切换，专注于近6个月的数据展示，为后续开发工作提供了清晰的指导。