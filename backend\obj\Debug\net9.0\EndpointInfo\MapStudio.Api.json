{"openapi": "3.0.1", "info": {"title": "MapStudio.Api", "version": "1.0"}, "paths": {"/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginResponseApiResponse"}}}}}}}, "/api/Auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Auth/profile": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserProfileResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserProfileResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserProfileResponseApiResponse"}}}}}}}, "/api/Files/upload": {"post": {"tags": ["Files"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "fileType": {"type": "string"}}}, "encoding": {"file": {"style": "form"}, "fileType": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FileUploadResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FileUploadResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FileUploadResponseApiResponse"}}}}}}}, "/api/Files/download/{fileId}": {"get": {"tags": ["Files"], "parameters": [{"name": "fileId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Files/preview/{fileId}": {"get": {"tags": ["Files"], "parameters": [{"name": "fileId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FilePreviewResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FilePreviewResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FilePreviewResponseApiResponse"}}}}}}}, "/api/Files/{fileId}": {"delete": {"tags": ["Files"], "parameters": [{"name": "fileId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Metadata/service-types": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ServiceTypeOptionListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceTypeOptionListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceTypeOptionListApiResponse"}}}}}}}, "/api/Metadata/permission-scopes": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PermissionScopeOptionListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PermissionScopeOptionListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PermissionScopeOptionListApiResponse"}}}}}}}, "/api/Metadata/auth-periods": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AuthPeriodOptionListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AuthPeriodOptionListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AuthPeriodOptionListApiResponse"}}}}}}}, "/api/Metadata/tenant-sizes": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TenantSizeOptionListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TenantSizeOptionListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantSizeOptionListApiResponse"}}}}}}}, "/api/Metadata/industries": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IndustryOptionListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IndustryOptionListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IndustryOptionListApiResponse"}}}}}}}, "/api/Operation/daily-stats": {"get": {"tags": ["Operation"], "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "selectedServiceType", "in": "query", "schema": {"type": "string"}}, {"name": "selectedSize", "in": "query", "schema": {"type": "string"}}, {"name": "viewMode", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DailyStatsResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DailyStatsResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DailyStatsResponseApiResponse"}}}}}}}, "/api/Operation/monthly-stats": {"get": {"tags": ["Operation"], "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "comparisonType", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MonthlyStatsResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MonthlyStatsResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MonthlyStatsResponseApiResponse"}}}}}}}, "/api/Operation/access-stats": {"get": {"tags": ["Operation"], "parameters": [{"name": "timeRange", "in": "query", "schema": {"type": "string"}}, {"name": "chartType", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AccessStatsResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AccessStatsResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AccessStatsResponseApiResponse"}}}}}}}, "/api/Operation/action-type-rank": {"get": {"tags": ["Operation"], "parameters": [{"name": "selectedOperationType", "in": "query", "schema": {"type": "string"}}, {"name": "tenantSize", "in": "query", "schema": {"type": "string"}}, {"name": "industry", "in": "query", "schema": {"type": "string"}}, {"name": "timeRange", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ActionTypeRankResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ActionTypeRankResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ActionTypeRankResponseApiResponse"}}}}}}}, "/api/Operation/tenant-action-rank": {"get": {"tags": ["Operation"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TenantActionRankResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TenantActionRankResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantActionRankResponseApiResponse"}}}}}}}, "/api/Operation/user-usage": {"get": {"tags": ["Operation"], "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "timeRange", "in": "query", "schema": {"type": "string"}}, {"name": "timeGranularity", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserUsageResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserUsageResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserUsageResponseApiResponse"}}}}}}}, "/api/Operation/logs": {"get": {"tags": ["Operation"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "tenantName", "in": "query", "schema": {"type": "string"}}, {"name": "actionType", "in": "query", "schema": {"type": "string"}}, {"name": "userName", "in": "query", "schema": {"type": "string"}}, {"name": "operationDate", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OperationLogListResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OperationLogListResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OperationLogListResponseApiResponse"}}}}}}}, "/api/Operation/addOperationlogs": {"post": {"tags": ["Operation"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationLog"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OperationLog"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OperationLog"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Operation/logs/{id}": {"get": {"tags": ["Operation"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OperationLogListResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OperationLogListResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OperationLogListResponseApiResponse"}}}}}}}, "/api/Operation/alarm-settings": {"get": {"tags": ["Operation"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AlarmSettingsResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AlarmSettingsResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AlarmSettingsResponseApiResponse"}}}}}}}, "/api/Operation/alarm-settings/template": {"put": {"tags": ["Operation"], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AlarmTemplateUpdateResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AlarmTemplateUpdateResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AlarmTemplateUpdateResponseApiResponse"}}}}}}}, "/api/Operation/service-status-stats": {"get": {"tags": ["Operation"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ServiceStatusStatsDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceStatusStatsDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceStatusStatsDtoApiResponse"}}}}}}}, "/api/Operation/six-month-usage-stats": {"get": {"tags": ["Operation"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SixMonthUsageStatsResponseDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SixMonthUsageStatsResponseDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SixMonthUsageStatsResponseDtoApiResponse"}}}}}}}, "/api/Operation/service-status-code-stats": {"get": {"tags": ["Operation"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ServiceStatusStatsResponseDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceStatusStatsResponseDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceStatusStatsResponseDtoApiResponse"}}}}}}}, "/api/Operation/tenants": {"get": {"tags": ["Operation"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TenantDataDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TenantDataDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantDataDtoListApiResponse"}}}}}}}, "/api/Operation/daily-stats/summary": {"get": {"tags": ["Operation"], "parameters": [{"name": "tenantName", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DailyStatsDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DailyStatsDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DailyStatsDTO"}}}}}}}, "/api/SendMsg/phones": {"get": {"tags": ["SendMsg"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SmsPhoneSettingDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SmsPhoneSettingDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SmsPhoneSettingDtoListApiResponse"}}}}}}, "post": {"tags": ["SendMsg"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddSmsPhoneSettingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddSmsPhoneSettingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddSmsPhoneSettingRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SmsPhoneSettingDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SmsPhoneSettingDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SmsPhoneSettingDtoApiResponse"}}}}}}}, "/api/SendMsg/phones/{id}": {"put": {"tags": ["SendMsg"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSmsPhoneSettingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateSmsPhoneSettingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateSmsPhoneSettingRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SmsPhoneSettingDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SmsPhoneSettingDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SmsPhoneSettingDtoApiResponse"}}}}}}, "delete": {"tags": ["SendMsg"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}}}}}}, "/api/SendMsg/sms-template": {"get": {"tags": ["SendMsg"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SmsTemplateSettingDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SmsTemplateSettingDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SmsTemplateSettingDtoListApiResponse"}}}}}}, "post": {"tags": ["SendMsg"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveSmsTemplateRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SaveSmsTemplateRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SaveSmsTemplateRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SmsTemplateSettingDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SmsTemplateSettingDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SmsTemplateSettingDtoApiResponse"}}}}}}}, "/api/SendMsg/send-test-sms": {"post": {"tags": ["SendMsg"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendSmsRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendSmsRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SendSmsRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SendSmsResponseDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SendSmsResponseDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendSmsResponseDtoApiResponse"}}}}}}}, "/api/SendMsg/send-alert-sms": {"post": {"tags": ["SendMsg"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendAlertSmsRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendAlertSmsRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SendAlertSmsRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SendSmsResponseDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SendSmsResponseDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendSmsResponseDtoApiResponse"}}}}}}}, "/api/System/health": {"get": {"tags": ["System"], "responses": {"200": {"description": "OK"}}}}, "/api/System/ping": {"get": {"tags": ["System"], "responses": {"200": {"description": "OK"}}}}, "/api/System/info": {"get": {"tags": ["System"], "responses": {"200": {"description": "OK"}}}}, "/api/Tenant/auth-applications": {"post": {"tags": ["Tenant"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubmitAuthApplicationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SubmitAuthApplicationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SubmitAuthApplicationRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AuthApplicationResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AuthApplicationResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AuthApplicationResponseApiResponse"}}}}}}, "get": {"tags": ["Tenant"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "serviceType", "in": "query", "schema": {"type": "string"}}, {"name": "authPeriod", "in": "query", "schema": {"type": "string"}}, {"name": "status", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AuthApplicationListResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AuthApplicationListResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AuthApplicationListResponseApiResponse"}}}}}}}, "/api/Tenant/auth-applications/{id}": {"get": {"tags": ["Tenant"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AuthApplicationDetailResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AuthApplicationDetailResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AuthApplicationDetailResponseApiResponse"}}}}}}}, "/api/Tenant/service-status": {"get": {"tags": ["Tenant"], "parameters": [{"name": "tenantId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TenantServiceStatusResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TenantServiceStatusResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantServiceStatusResponseApiResponse"}}}}}}}, "/api/Tenant/auth-progress": {"get": {"tags": ["Tenant"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "serviceType", "in": "query", "schema": {"type": "string"}}, {"name": "authPeriod", "in": "query", "schema": {"type": "string"}}, {"name": "status", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TenantServiceProgressResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TenantServiceProgressResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantServiceProgressResponseApiResponse"}}}}}}}, "/api/Tenant/auth-progress/{id}": {"get": {"tags": ["Tenant"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TenantServiceProgressDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TenantServiceProgressDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantServiceProgressDtoApiResponse"}}}}}}}}, "components": {"schemas": {"AccessStatsDto": {"type": "object", "properties": {"date": {"type": "string", "nullable": true}, "accessCount": {"type": "integer", "format": "int32"}, "uniqueVisitors": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "AccessStatsOverviewDto": {"type": "object", "properties": {"totalAccess": {"type": "integer", "format": "int32"}, "totalUniqueVisitors": {"type": "integer", "format": "int32"}, "averageDaily": {"type": "number", "format": "double"}, "growthRate": {"type": "number", "format": "double"}}, "additionalProperties": false}, "AccessStatsResponse": {"type": "object", "properties": {"accessData": {"type": "array", "items": {"$ref": "#/components/schemas/AccessStatsDto"}, "nullable": true}, "overview": {"$ref": "#/components/schemas/AccessStatsOverviewDto"}}, "additionalProperties": false}, "AccessStatsResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/AccessStatsResponse"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ActionTypeOverviewDto": {"type": "object", "properties": {"totalOperations": {"type": "integer", "format": "int32"}, "mostPopularAction": {"type": "string", "nullable": true}, "mostPopularPercentage": {"type": "number", "format": "double"}}, "additionalProperties": false}, "ActionTypeRankDto": {"type": "object", "properties": {"actionType": {"type": "string", "nullable": true}, "actionName": {"type": "string", "nullable": true}, "count": {"type": "integer", "format": "int32"}, "percentage": {"type": "number", "format": "double"}}, "additionalProperties": false}, "ActionTypeRankResponse": {"type": "object", "properties": {"rankingData": {"type": "array", "items": {"$ref": "#/components/schemas/ActionTypeRankDto"}, "nullable": true}, "overview": {"$ref": "#/components/schemas/ActionTypeOverviewDto"}}, "additionalProperties": false}, "ActionTypeRankResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ActionTypeRankResponse"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "AddSmsPhoneSettingRequest": {"type": "object", "properties": {"phone": {"type": "string", "nullable": true}, "levels": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "AlarmEmailDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "enabled": {"type": "boolean"}, "levels": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "AlarmSettingsResponse": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "emailTemplate": {"type": "string", "nullable": true}, "emailList": {"type": "array", "items": {"$ref": "#/components/schemas/AlarmEmailDto"}, "nullable": true}, "isActive": {"type": "boolean"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "AlarmSettingsResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/AlarmSettingsResponse"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "AlarmTemplateUpdateResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "AlarmTemplateUpdateResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/AlarmTemplateUpdateResponse"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ApprovalHistoryDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "action": {"type": "string", "nullable": true}, "operator": {"type": "string", "nullable": true}, "operatorRole": {"type": "string", "nullable": true}, "time": {"type": "string", "nullable": true}, "comment": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AuthApplicationDetailResponse": {"type": "object", "properties": {"applicationId": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "applyTime": {"type": "string", "nullable": true}, "serviceInfo": {"$ref": "#/components/schemas/ServiceInfoDto"}, "tenantInfo": {"$ref": "#/components/schemas/TenantInfoDto"}, "documents": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentDto"}, "nullable": true}, "approvalHistory": {"type": "array", "items": {"$ref": "#/components/schemas/ApprovalHistoryDto"}, "nullable": true}}, "additionalProperties": false}, "AuthApplicationDetailResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/AuthApplicationDetailResponse"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "AuthApplicationDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "tenantId": {"type": "string", "nullable": true}, "tenantName": {"type": "string", "nullable": true}, "applyTime": {"type": "string", "nullable": true}, "serviceType": {"type": "string", "nullable": true}, "serviceTypeName": {"type": "string", "nullable": true}, "authPeriod": {"type": "string", "nullable": true}, "authPeriodName": {"type": "string", "nullable": true}, "contactPerson": {"type": "string", "nullable": true}, "contactPhone": {"type": "string", "nullable": true}, "contactEmail": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AuthApplicationListResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/AuthApplicationDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "currentPage": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "AuthApplicationListResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/AuthApplicationListResponse"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "AuthApplicationResponse": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "appliedAt": {"type": "string", "format": "date-time"}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AuthApplicationResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/AuthApplicationResponse"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "AuthPeriodOption": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AuthPeriodOptionListApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/AuthPeriodOption"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "BooleanApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "DailyStatsDTO": {"type": "object", "properties": {"totalTenants": {"type": "integer", "format": "int32"}, "averageDailyOperations": {"type": "number", "format": "double"}, "maxDailyOperations": {"type": "integer", "format": "int32"}, "totalOperations": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "DailyStatsDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "serviceType": {"type": "string", "nullable": true}, "serviceName": {"type": "string", "nullable": true}, "size": {"type": "string", "nullable": true}, "dailyOperations": {"type": "integer", "format": "int32"}, "color": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DailyStatsOverviewDto": {"type": "object", "properties": {"totalTenants": {"type": "integer", "format": "int32"}, "averageDailyOperations": {"type": "integer", "format": "int32"}, "maxDailyOperations": {"type": "integer", "format": "int32"}, "maxTenant": {"$ref": "#/components/schemas/TenantSummaryDto"}, "totalOperations": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "DailyStatsResponse": {"type": "object", "properties": {"dailyStatsData": {"type": "array", "items": {"$ref": "#/components/schemas/DailyStatsDto"}, "nullable": true}, "overview": {"$ref": "#/components/schemas/DailyStatsOverviewDto"}}, "additionalProperties": false}, "DailyStatsResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/DailyStatsResponse"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "DocumentDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "typeName": {"type": "string", "nullable": true}, "size": {"type": "string", "nullable": true}, "uploadTime": {"type": "string", "nullable": true}, "url": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FilePreviewResponse": {"type": "object", "properties": {"fileId": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "contentType": {"type": "string", "nullable": true}, "previewUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FilePreviewResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/FilePreviewResponse"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "FileUploadResponse": {"type": "object", "properties": {"fileId": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "fileSize": {"type": "integer", "format": "int64"}, "fileType": {"type": "string", "nullable": true}, "uploadTime": {"type": "string", "format": "date-time"}, "downloadUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FileUploadResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/FileUploadResponse"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "IndustryOption": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "IndustryOptionListApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/IndustryOption"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "LoginRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoginResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "token": {"type": "string", "nullable": true}, "user": {"$ref": "#/components/schemas/UserDto"}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoginResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/LoginResponse"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "MonthlyDataDto": {"type": "object", "properties": {"month": {"type": "string", "nullable": true}, "operations": {"type": "integer", "format": "int32"}, "prevYearOperations": {"type": "integer", "format": "int32"}, "yoyGrowth": {"type": "number", "format": "double"}}, "additionalProperties": false}, "MonthlyStatsOverviewDto": {"type": "object", "properties": {"totalOperations": {"type": "integer", "format": "int32"}, "monthlyAvg": {"type": "integer", "format": "int32"}, "maxMonthOperations": {"type": "integer", "format": "int32"}, "maxMonthName": {"type": "string", "nullable": true}, "averageGrowth": {"type": "number", "format": "double"}}, "additionalProperties": false}, "MonthlyStatsResponse": {"type": "object", "properties": {"overview": {"$ref": "#/components/schemas/MonthlyStatsOverviewDto"}, "monthlyData": {"type": "array", "items": {"$ref": "#/components/schemas/MonthlyDataDto"}, "nullable": true}}, "additionalProperties": false}, "MonthlyStatsResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/MonthlyStatsResponse"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "MonthlyUsageDataDto": {"type": "object", "properties": {"month": {"type": "string", "nullable": true}, "operations": {"type": "integer", "format": "int32"}, "activeTenants": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "OperationLog": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "tenantId": {"type": "string", "nullable": true}, "userId": {"type": "string", "nullable": true}, "actionType": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "actionObject": {"type": "string", "nullable": true}, "ipAddress": {"type": "string", "nullable": true}, "userAgent": {"type": "string", "nullable": true}, "operatedAt": {"type": "string", "format": "date-time"}, "tenant": {"$ref": "#/components/schemas/Tenant"}, "user": {"$ref": "#/components/schemas/User"}}, "additionalProperties": false}, "OperationLogDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "time": {"type": "string", "nullable": true}, "tenant": {"type": "string", "nullable": true}, "user": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "object": {"type": "string", "nullable": true}, "detail": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OperationLogListResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/OperationLogDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "currentPage": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "OperationLogListResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/OperationLogListResponse"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "PermissionScopeOption": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "permissions": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PermissionScopeOptionListApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionScopeOption"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ProgressStep": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "time": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SaveSmsTemplateRequestDto": {"type": "object", "properties": {"tempId": {"type": "integer", "format": "int32", "nullable": true}, "templateName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SendAlertSmsRequestDto": {"type": "object", "properties": {"phoneNumber": {"type": "string", "nullable": true}, "level": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SendSmsRequestDto": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "modular": {"type": "string", "nullable": true}, "function": {"type": "string", "nullable": true}, "receiveMans": {"type": "string", "nullable": true}, "receivePns": {"type": "string", "nullable": true}, "ccMans": {"type": "string", "nullable": true}, "ccPns": {"type": "string", "nullable": true}, "sendMan": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SendSmsResponseDto": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"nullable": true}, "errorCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SendSmsResponseDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/SendSmsResponseDto"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ServiceInfoDto": {"type": "object", "properties": {"typeName": {"type": "string", "nullable": true}, "authPeriodName": {"type": "string", "nullable": true}, "permissionScopeName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ServiceStatusStatsDto": {"type": "object", "properties": {"totalUsers": {"type": "integer", "format": "int32"}, "activeUsers": {"type": "integer", "format": "int32"}, "todayVisits": {"type": "integer", "format": "int32"}, "pendingApplications": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ServiceStatusStatsDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ServiceStatusStatsDto"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ServiceStatusStatsResponseDto": {"type": "object", "properties": {"totalLogs": {"type": "integer", "format": "int32"}, "statusCodeStats": {"type": "array", "items": {"$ref": "#/components/schemas/StatusCodeStatsDto"}, "nullable": true}, "lastUpdated": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ServiceStatusStatsResponseDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ServiceStatusStatsResponseDto"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ServiceTypeOption": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ServiceTypeOptionListApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ServiceTypeOption"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "SixMonthUsageStatsResponseDto": {"type": "object", "properties": {"monthlyData": {"type": "array", "items": {"$ref": "#/components/schemas/MonthlyUsageDataDto"}, "nullable": true}, "totalOperations": {"type": "integer", "format": "int32"}, "averageOperations": {"type": "number", "format": "double"}, "growthRate": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "SixMonthUsageStatsResponseDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/SixMonthUsageStatsResponseDto"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "SmsPhoneSettingDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "phone": {"type": "string", "nullable": true}, "levels": {"type": "array", "items": {"type": "string"}, "nullable": true}, "status": {"type": "boolean"}, "createDate": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "SmsPhoneSettingDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/SmsPhoneSettingDto"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "SmsPhoneSettingDtoListApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SmsPhoneSettingDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "SmsTemplateSettingDto": {"type": "object", "properties": {"tempId": {"type": "integer", "format": "int32"}, "templateName": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "SmsTemplateSettingDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/SmsTemplateSettingDto"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "SmsTemplateSettingDtoListApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SmsTemplateSettingDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "StatusCodeStatsDto": {"type": "object", "properties": {"statusCode": {"type": "integer", "format": "int32"}, "statusName": {"type": "string", "nullable": true}, "count": {"type": "integer", "format": "int32"}, "percentage": {"type": "number", "format": "double"}}, "additionalProperties": false}, "SubmitAuthApplicationRequest": {"type": "object", "properties": {"tenantName": {"type": "string", "nullable": true}, "tenantId": {"type": "string", "nullable": true}, "contactPerson": {"type": "string", "nullable": true}, "contactEmail": {"type": "string", "nullable": true}, "contactPhone": {"type": "string", "nullable": true}, "serviceType": {"type": "string", "nullable": true}, "authPeriod": {"type": "string", "nullable": true}, "permissionScope": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "businessLicense": {"type": "string", "nullable": true}, "organizationCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Tenant": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "tenantName": {"type": "string", "nullable": true}, "tenantId": {"type": "string", "nullable": true}, "contactPerson": {"type": "string", "nullable": true}, "contactEmail": {"type": "string", "nullable": true}, "contactPhone": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "industry": {"type": "string", "nullable": true}, "scale": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "TenantActionRankDto": {"type": "object", "properties": {"tenantId": {"type": "string", "nullable": true}, "tenantName": {"type": "string", "nullable": true}, "operationCount": {"type": "integer", "format": "int32"}, "serviceType": {"type": "string", "nullable": true}, "size": {"type": "string", "nullable": true}, "growthRate": {"type": "number", "format": "double"}}, "additionalProperties": false}, "TenantActionRankResponse": {"type": "object", "properties": {"rankingData": {"type": "array", "items": {"$ref": "#/components/schemas/TenantActionRankDto"}, "nullable": true}}, "additionalProperties": false}, "TenantActionRankResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/TenantActionRankResponse"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "TenantDataDto": {"type": "object", "properties": {"tenantId": {"type": "string", "nullable": true}, "tenantName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TenantDataDtoListApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TenantDataDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "TenantInfoDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}, "contactPerson": {"type": "string", "nullable": true}, "contactPhone": {"type": "string", "nullable": true}, "contactEmail": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TenantServiceProgressDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "tenantId": {"type": "string", "nullable": true}, "tenantName": {"type": "string", "nullable": true}, "applyTime": {"type": "string", "nullable": true}, "serviceType": {"type": "string", "nullable": true}, "serviceTypeName": {"type": "string", "nullable": true}, "authPeriod": {"type": "string", "nullable": true}, "authPeriodName": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "statusName": {"type": "string", "nullable": true}, "progress": {"type": "integer", "format": "int32"}, "currentStep": {"type": "string", "nullable": true}, "contactPerson": {"type": "string", "nullable": true}, "contactPhone": {"type": "string", "nullable": true}, "contactEmail": {"type": "string", "nullable": true}, "steps": {"type": "array", "items": {"$ref": "#/components/schemas/ProgressStep"}, "nullable": true}}, "additionalProperties": false}, "TenantServiceProgressDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/TenantServiceProgressDto"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "TenantServiceProgressResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/TenantServiceProgressDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "currentPage": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TenantServiceProgressResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/TenantServiceProgressResponse"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "TenantServiceStatusDto": {"type": "object", "properties": {"tenantId": {"type": "string", "nullable": true}, "tenantName": {"type": "string", "nullable": true}, "serviceType": {"type": "string", "nullable": true}, "serviceName": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "statusName": {"type": "string", "nullable": true}, "expiryDate": {"type": "string", "format": "date-time", "nullable": true}, "remainingDays": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TenantServiceStatusResponse": {"type": "object", "properties": {"services": {"type": "array", "items": {"$ref": "#/components/schemas/TenantServiceStatusDto"}, "nullable": true}}, "additionalProperties": false}, "TenantServiceStatusResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/TenantServiceStatusResponse"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "TenantSizeOption": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TenantSizeOptionListApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TenantSizeOption"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "TenantSummaryDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "serviceName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateSmsPhoneSettingRequest": {"type": "object", "properties": {"status": {"type": "boolean"}, "levels": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "User": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}, "tenantId": {"type": "string", "nullable": true}, "passwordHash": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserProfileResponse": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserProfileResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/UserProfileResponse"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "UserUsageDto": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "tenantName": {"type": "string", "nullable": true}, "operationCount": {"type": "integer", "format": "int32"}, "lastActiveTime": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserUsageOverviewDto": {"type": "object", "properties": {"totalUsers": {"type": "integer", "format": "int32"}, "activeUsers": {"type": "integer", "format": "int32"}, "averageOperations": {"type": "number", "format": "double"}}, "additionalProperties": false}, "UserUsageResponse": {"type": "object", "properties": {"usageData": {"type": "array", "items": {"$ref": "#/components/schemas/UserUsageDto"}, "nullable": true}, "overview": {"$ref": "#/components/schemas/UserUsageOverviewDto"}}, "additionalProperties": false}, "UserUsageResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/UserUsageResponse"}, "message": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}}}}