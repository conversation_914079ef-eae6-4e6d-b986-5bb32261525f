using SqlSugar;

namespace MapStudio.Api.Models.Entities
{
    /// <summary>
    /// 用户实体，对应数据库表 ms_users
    /// </summary>
    [SugarTable("ms_users")]
    public class User
    {
        /// <summary>
        /// 用户ID (主键)
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, Length = 36)]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 用户姓名
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 用户邮箱
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = false)]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 用户角色 (admin, operator, viewer)
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        public string Role { get; set; } = string.Empty;

        /// <summary>
        /// 租户ID
        /// </summary>
        [SugarColumn(Length = 36, IsNullable = true)]
        public string? TenantId { get; set; }

        /// <summary>
        /// 密码哈希
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = false)]
        public string PasswordHash { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsActive { get; set; } = true;
    }
}