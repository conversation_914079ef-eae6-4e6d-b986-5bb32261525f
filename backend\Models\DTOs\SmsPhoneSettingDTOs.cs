namespace MapStudio.Api.Models.DTOs
{
    [SqlSugar.SugarTable("ms_sms_phone_settings")]
    /// <summary>
    /// 短信接收手机号设置DTO
    /// </summary>
    public class SmsPhoneSettingDto
    {
        /// <summary>
        /// 设置ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string Phone { get; set; } = string.Empty;

        /// <summary>
        /// 告警级别
        /// </summary>
        public List<string> Levels { get; set; } = new();

        /// <summary>
        /// 状态
        /// </summary>
        public bool Status { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateDate { get; set; }
    }

    /// <summary>
    /// 添加短信接收手机号请求
    /// </summary>
    public class AddSmsPhoneSettingRequest
    {
        /// <summary>
        /// 手机号
        /// </summary>
        public string Phone { get; set; } = string.Empty;

        /// <summary>
        /// 告警级别
        /// </summary>
        public string[] Levels { get; set; } = Array.Empty<string>();
    }

    /// <summary>
    /// 更新短信接收手机号状态请求
    /// </summary>
    public class UpdateSmsPhoneSettingRequest
    {
        /// <summary>
        /// 状态
        /// </summary>
        public bool Status { get; set; }

        /// <summary>
        /// 告警级别
        /// </summary>
        public string[] Levels { get; set; } = Array.Empty<string>();
    }

    /// <summary>
    /// 短信接收手机号响应
    /// </summary>
    public class SmsPhoneSettingResponse
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 数据
        /// </summary>
        public SmsPhoneSettingDto? Data { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public string? ErrorCode { get; set; }
    }

    /// <summary>
    /// 短信接收手机号列表响应
    /// </summary>
    public class SmsPhoneSettingListResponse
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 数据
        /// </summary>
        public List<SmsPhoneSettingDto> Data { get; set; } = new();

        /// <summary>
        /// 错误代码
        /// </summary>
        public string? ErrorCode { get; set; }
    }
}