<template>
  <div class="p-6 space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">租户授权待审批清单</h1>
      <p class="mt-1 text-gray-500">查看和处理租户提交的授权申请</p>
    </div>

    <!-- 搜索和筛选栏 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <i class="fa-solid fa-search text-gray-400"></i>
          </div>
          <input
            type="text"
            placeholder="搜索租户名称、申请单号或联系人..."
            v-model="searchTerm"
            @input="handleSearchChange"
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>

        <div>
          <select
            name="serviceType"
            v-model="filters.serviceType"
            @change="handleFilterChange"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option v-for="option in serviceTypeOptions" :key="option.id" :value="option.id">{{ option.name }}</option>
          </select>
        </div>

        <div>
          <select
            name="authPeriod"
            v-model="filters.authPeriod"
            @change="handleFilterChange"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:border-blue-500 sm:text-sm"
          >
            <option v-for="option in authPeriodOptions" :key="option.id" :value="option.id">{{ option.name }}</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                申请单号
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                租户名称
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                申请时间
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                权限类型
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                过期时间
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                申请人
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                联系方式
              </th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <template v-if="paginatedApplications.length > 0">
              <tr v-for="application in paginatedApplications" :key="application.id" class="hover:bg-gray-50 transition-colors">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-blue-600 hover:text-blue-900 cursor-pointer">
                    {{ application.id }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ application.tenantName }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-500">{{ application.applyTime }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                    {{ application.serviceTypeName }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ application.authPeriodName }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ application.contactPerson }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-500">{{ application.contactPhone }}</div>
                  <div class="text-xs text-gray-400">{{ application.contactEmail }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex justify-end space-x-2">
                    <router-link
                      :to="`/tenant/auth-approval/${application.id}`"
                      class="text-blue-600 hover:text-blue-900"
                    >
                      查看详情
                    </router-link>
                    <button
                      @click="handleApprovalAction(application.id, 'approve')"
                      :disabled="loading"
                      class="text-green-600 hover:text-green-900 disabled:opacity-50"
                    >
                      审批通过
                    </button>
                    <button
                      @click="handleApprovalAction(application.id, 'reject')"
                      :disabled="loading"
                      class="text-red-600 hover:text-red-900 disabled:opacity-50"
                    >
                      驳回
                    </button>
                  </div>
                </td>
              </tr>
            </template>
            <template v-else>
              <tr>
                <td colspan="8" class="px-6 py-12 text-center">
                  <div class="flex flex-col items-center">
                    <i class="fa-solid fa-search text-gray-300 text-5xl mb-3"></i>
                    <h3 class="text-lg font-medium text-gray-900">未找到匹配的申请</h3>
                    <p class="mt-1 text-gray-500">请尝试调整搜索条件或清除筛选器</p>
                    <button
                      @click="clearFilters"
                      class="mt-4 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100"
                    >
                      清除所有筛选条件
                    </button>
                  </div>
                </td>
              </tr>
            </template>
          </tbody>
        </table>
      </div>

      <!-- 分页控件 -->
      <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
        <div class="flex-1 flex justify-between sm:hidden">
          <button
            @click="prevPage"
            :disabled="pagination.page === 1"
            class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            上一页
          </button>
          <button
            @click="nextPage"
            :disabled="pagination.page >= Math.ceil(pagination.total / pagination.pageSize)"
            class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            下一页
          </button>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              显示第 <span class="font-medium">{{ (pagination.page - 1) * pagination.pageSize + 1 }}</span> 到{' '}
              <span class="font-medium">
                {{ Math.min(pagination.page * pagination.pageSize, pagination.total) }}
              </span> 条，共 <span class="font-medium">{{ pagination.total }}</span> 条结果
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                @click="goToFirstPage"
                :disabled="pagination.page === 1"
                class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span class="sr-only">首页</span>
                <i class="fa-solid fa-angle-double-left"></i>
              </button>
              <button
                @click="prevPage"
                :disabled="pagination.page === 1"
                class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span class="sr-only">上一页</span>
                <i class="fa-solid fa-angle-left"></i>
              </button>

              <!-- 页码按钮 - 简化版只显示当前页 -->
              <button
                @click="goToPage(pagination.page)"
                aria-current="page"
                class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium"
              >
                {{ pagination.page }}
              </button>

              <button
                @click="nextPage"
                :disabled="pagination.page >= Math.ceil(pagination.total / pagination.pageSize)"
                class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span class="sr-only">下一页</span>
                <i class="fa-solid fa-angle-right"></i>
              </button>
              <button
                @click="goToLastPage"
                :disabled="pagination.page >= Math.ceil(pagination.total / pagination.pageSize)"
                class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span class="sr-only">末页</span>
                <i class="fa-solid fa-angle-double-right"></i>
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { TenantService } from '@/services';
import type { AuthApplicationItem } from '@/services/types';

// 申请数据和状态
const approvalApplications = ref<AuthApplicationItem[]>([]);
const isLoadingData = ref(false);

// 搜索状态
const searchTerm = ref('');

// 筛选状态
const filters = reactive({
  serviceType: '',
  authPeriod: '',
});

// 分页状态
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
});

// 加载状态
const loading = ref(false);

// 处理搜索变化
const handleSearchChange = () => {
  pagination.page = 1; // 重置页码
  loadAuthApplications(); // 重新加载数据
};

// 处理筛选变化
const handleFilterChange = (e: Event) => {
  const target = e.target as HTMLSelectElement;
  const { name, value } = target;
  filters[name as keyof typeof filters] = value;
  pagination.page = 1; // 重置页码
  loadAuthApplications(); // 重新加载数据
};

// 处理审批操作
const handleApprovalAction = async (id: string, action: 'approve' | 'reject') => {
  try {
    loading.value = true;

    const comments = prompt(`请输入审批意见（${action === 'approve' ? '通过' : '驳回'}）：`);
    if (!comments || !comments.trim()) {
      alert('审批意见不能为空');
      return;
    }

    await TenantService.approveAuthApplication(id, {
      action,
      comments: comments.trim()
    });

    alert(`申请单 ${id} 已成功${action === 'approve' ? '审批通过' : '驳回'}。`);

    // 重新加载数据
    await loadAuthApplications();
  } catch (error: any) {
    alert(`审批操作失败：${error.message}`);
  } finally {
    loading.value = false;
  }
};

// 清除所有筛选条件
const clearFilters = () => {
  searchTerm.value = '';
  filters.serviceType = '';
  filters.authPeriod = '';
  pagination.page = 1;
  loadAuthApplications(); // 重新加载数据
};

// 加载授权申请数据
const loadAuthApplications = async () => {
  try {
    isLoadingData.value = true;

    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      searchTerm: searchTerm.value || undefined,
      serviceType: filters.serviceType || undefined,
      authPeriod: filters.authPeriod || undefined,
      status: 'pending' // 只获取待审批的申请
    };

    console.log('📋 加载授权申请列表...', params);

    const response = await TenantService.getAuthApplications(params);

    approvalApplications.value = response.items;
    pagination.total = response.totalCount;

    console.log(`✅ 获取申请列表成功: ${response.totalCount} 条记录`);
  } catch (error: any) {
    console.error('❌ 获取申请列表失败:', error);
    alert(`获取申请列表失败：${error.message}`);
  } finally {
    isLoadingData.value = false;
  }
};

// 计算分页数据
const paginatedApplications = computed(() => {
  return approvalApplications.value;
});

// 分页方法
const prevPage = () => {
  if (pagination.page > 1) {
    pagination.page--;
    loadAuthApplications();
  }
};

const nextPage = () => {
  const maxPage = Math.ceil(pagination.total / pagination.pageSize);
  if (pagination.page < maxPage) {
    pagination.page++;
    loadAuthApplications();
  }
};

const goToPage = (page: number) => {
  pagination.page = page;
  loadAuthApplications();
};

const goToFirstPage = () => {
  pagination.page = 1;
  loadAuthApplications();
};

const goToLastPage = () => {
  pagination.page = Math.ceil(pagination.total / pagination.pageSize);
  loadAuthApplications();
};

// 页面加载时获取数据
onMounted(() => {
  loadAuthApplications();
});

// 服务类型选项
const serviceTypeOptions = [
  { id: '', name: '全部权限类型' },
  { id: 'basic', name: '基础地图服务' },
  { id: 'advanced', name: '高级地图服务' },
  { id: 'enterprise', name: '企业级地图服务' },
  { id: 'custom', name: '定制化地图服务' },
];

// 授权期限选项
const authPeriodOptions = [
  { id: '', name: '全部过期时间' },
  { id: '3months', name: '3个月' },
  { id: '6months', name: '6个月' },
  { id: '1year', name: '1年' },
  { id: '2years', name: '2年' },
  { id: '3years', name: '3年' },
];
</script>
