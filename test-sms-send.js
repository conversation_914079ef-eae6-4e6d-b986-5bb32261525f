// 短信发送测试程序
const axios = require('axios');

async function testSmsSend() {
  console.log('开始测试短信发送功能...');
  
  try {
    // 准备测试数据
    const testData = {
      phoneNumber: '13800138000',  // 测试手机号
      level: 'test'
    };
    
    console.log('准备发送测试短信数据:', testData);
    
    // 发送HTTP请求到后端接口
    console.log('发送HTTP请求到后端接口...');
    
    // 使用axios发送POST请求
    const response = await axios.post('http://localhost:5000/SendMsg/send-alert-sms', testData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000  // 10秒超时
    });
    
    console.log('收到响应:', response.data);
    console.log('短信发送测试完成');
  } catch (error) {
    console.error('短信发送测试失败:');
    console.error('错误信息:', error.message);
    
    if (error.response) {
      console.error('响应状态码:', error.response.status);
      console.error('响应头:', error.response.headers);
      console.error('响应数据:', error.response.data);
    } else if (error.request) {
      console.error('请求信息:', error.request);
    } else {
      console.error('错误详情:', error);
    }
  }
}

// 执行测试
testSmsSend();