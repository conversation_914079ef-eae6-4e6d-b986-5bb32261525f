# 前端实现计划：服务状态统计

## 概述
本文档描述了如何在前端实现服务状态统计功能，包括API调用服务的创建和Vue组件的修改。

## 实现步骤

### 1. 创建API服务方法
在前端服务层创建用于获取服务状态统计的API方法。

#### 1.1 在types.ts中添加类型定义
```typescript
// 服务状态统计响应类型
export interface ServiceStatusStatisticsResponse {
  totalUsers: number;
  activeUsers: number;
  todayVisits: number;
  pendingApplications: number;
}

// API响应类型
export interface ServiceStatusApiResponse {
  success: boolean;
  data: ServiceStatusStatisticsResponse;
  message: string;
}
```

#### 1.2 在endpoints.ts中添加端点定义
```typescript
// 租户管理
TENANT: {
  // ... existing endpoints
  SERVICE_STATUS_STATISTICS: '/Tenant/service-status-statistics'
}
```

#### 1.3 创建tenantService.ts服务文件
```typescript
import { API_ENDPOINTS } from '@/config/endpoints'
import { ServiceStatusApiResponse } from '@/services/types'
import { apiClient } from '@/utils/apiClient'

/**
 * 获取服务状态统计
 */
export const getServiceStatusStatistics = async (): Promise<ServiceStatusApiResponse> => {
  const response = await apiClient.get<ServiceStatusApiResponse>(
    API_ENDPOINTS.TENANT.SERVICE_STATUS_STATISTICS
  )
  return response.data
}
```

### 2. 修改ServiceStatus.vue组件
更新ServiceStatus.vue组件以使用动态数据替换静态数据。

#### 2.1 导入必要的模块
```typescript
import { ref, computed, onMounted } from 'vue'
import ChartBase from '@/components/ChartBase.vue'
import { getServiceStatusStatistics } from '@/services/tenantService'
import type { ServiceStatusStatisticsResponse } from '@/services/types'
```

#### 2.2 定义响应式数据
```typescript
// 服务状态统计数据
const serviceStatusData = ref<ServiceStatusStatisticsResponse>({
  totalUsers: 0,
  activeUsers: 0,
  todayVisits: 0,
  pendingApplications: 0
})

// 加载状态
const loading = ref(true)
const error = ref<string | null>(null)
```

#### 2.3 创建数据加载方法
```typescript
/**
 * 加载服务状态统计数据
 */
const loadServiceStatusData = async () => {
  try {
    loading.value = true
    error.value = null
    const response = await getServiceStatusStatistics()
    
    if (response.success) {
      serviceStatusData.value = response.data
    } else {
      error.value = response.message || '获取数据失败'
    }
  } catch (err) {
    error.value = '网络错误，请稍后重试'
    console.error('加载服务状态数据失败:', err)
  } finally {
    loading.value = false
  }
}
```

#### 2.4 在onMounted中调用数据加载
```typescript
onMounted(() => {
  loadServiceStatusData()
})
```

#### 2.5 更新模板中的数据绑定
将原来静态的数据替换为从serviceStatusData中获取的动态数据：
```vue
<!-- 总用户数 -->
<h3 class="mt-1 text-2xl font-bold text-gray-900">{{ serviceStatusData.totalUsers }}</h3>

<!-- 活跃用户 -->
<h3 class="mt-1 text-2xl font-bold text-gray-900">{{ serviceStatusData.activeUsers }}</h3>

<!-- 今日访问量 -->
<h3 class="mt-1 text-2xl font-bold text-gray-900">{{ serviceStatusData.todayVisits }}</h3>

<!-- 待审批申请 -->
<h3 class="mt-1 text-2xl font-bold text-gray-900">{{ serviceStatusData.pendingApplications }}</h3>
```

#### 2.6 添加加载状态和错误处理
```vue
<!-- 在核心指标卡片区域添加 -->
<div v-if="loading" class="flex justify-center items-center h-32">
  <div class="text-gray-500">加载中...</div>
</div>

<div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
  <div class="text-red-600">{{ error }}</div>
  <button @click="loadServiceStatusData" class="mt-2 px-4 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200">
    重新加载
  </button>
</div>
```

### 3. 添加自动刷新功能（可选）
为了确保数据的实时性，可以添加定时刷新功能：

```typescript
// 在组件中添加
let refreshInterval: number | null = null

onMounted(() => {
  loadServiceStatusData()
  
  // 每30秒刷新一次数据
  refreshInterval = window.setInterval(() => {
    loadServiceStatusData()
  }, 30000)
})

onUnmounted(() => {
  // 清理定时器
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})
```

## 样式和用户体验优化

### 4.1 加载动画
添加更友好的加载动画：
```vue
<div v-if="loading" class="flex justify-center items-center h-32">
  <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
  <span class="ml-2 text-gray-500">加载中...</span>
</div>
```

### 4.2 数字格式化
对于较大的数字，可以添加格式化显示：
```typescript
const formatNumber = (num: number): string => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}
```

### 4.3 响应式设计优化
确保在不同屏幕尺寸下都能良好显示。

## 测试计划

### 5.1 单元测试
- 测试getServiceStatusStatistics方法的正确性
- 测试ServiceStatus.vue组件在不同状态下的渲染

### 5.2 端到端测试
- 验证页面能正确显示动态数据
- 测试加载状态和错误处理
- 验证自动刷新功能（如果实现）

## 部署注意事项
- 确保API端点URL配置正确
- 验证认证token的传递
- 测试在不同网络条件下的表现