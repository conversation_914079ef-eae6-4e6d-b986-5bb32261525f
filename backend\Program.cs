using SqlSugar;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Serilog;
using MapStudio.Api.Extensions;
using MapStudio.Api.Middleware;
using MapStudio.Api.Data.Context;
using MapStudio.Api.Services.Interfaces;
using MapStudio.Api.Services.Implementations;

// 配置Serilog
Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File("logs/mapstudio-api-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

var builder = WebApplication.CreateBuilder(args);

// 使用Serilog
builder.Host.UseSerilog();

// 添加控制器
builder.Services.AddControllers();

// 配置API文档
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// 配置SqlSugar
builder.Services.AddSqlSugar(new ConnectionConfig()
{
    ConnectionString = builder.Configuration.GetConnectionString("DefaultConnection"),
    DbType = DbType.MySql,
    IsAutoCloseConnection = true,
    InitKeyType = InitKeyType.Attribute
});

// 配置JWT认证（可选启用）
var jwtSettings = builder.Configuration.GetSection("JwtSettings");
var jwtEnabled = jwtSettings.GetValue<bool>("Enabled", false);

if (jwtEnabled)
{
    var secretKey = Encoding.ASCII.GetBytes(jwtSettings["SecretKey"]);

    builder.Services.AddAuthentication(options =>
    {
        options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
        options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    })
    .AddJwtBearer(options =>
    {
        options.RequireHttpsMetadata = false;
        options.SaveToken = true;
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(secretKey),
            ValidateIssuer = true,
            ValidIssuer = jwtSettings["Issuer"],
            ValidateAudience = true,
            ValidAudience = jwtSettings["Audience"],
            ValidateLifetime = true,
            ClockSkew = TimeSpan.Zero
        };
    });
    
    Console.WriteLine("🔐 JWT认证已启用");
}
else
{
    // 添加空的认证方案以避免错误
    builder.Services.AddAuthentication();
    builder.Services.AddAuthorization();
    
    Console.WriteLine("⚠️ JWT认证已禁用 - 仅用于开发环境");
}

// 配置CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("MapStudioPolicy", policy =>
    {
        var allowedOrigins = builder.Configuration.GetSection("CorsSettings:AllowedOrigins").Get<string[]>() ?? new[] { "http://localhost:5173" };
        policy.WithOrigins(allowedOrigins)
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});

// 注册业务服务
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<ITenantService, TenantService>();
builder.Services.AddScoped<IOperationService, OperationService>();
builder.Services.AddScoped<IFileService, FileService>();
builder.Services.AddScoped<IMetadataService, MetadataService>();
builder.Services.AddScoped<ISmsPhoneService, SmsPhoneService>();
builder.Services.AddScoped<ISmsTemplateService, SmsTemplateService>();
builder.Services.AddHttpClient();
builder.Services.AddScoped<DatabaseInitializer>();
builder.Services.AddScoped<TestDatabaseInitializer>();

var app = builder.Build();

// 配置HTTP请求管道
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// 使用CORS
app.UseCors("MapStudioPolicy");

// 使用自定义中间件
app.UseMiddleware<GlobalExceptionMiddleware>();

app.UseHttpsRedirection();

// 条件性使用认证中间件
var jwtEnabledForMiddleware = app.Configuration.GetValue<bool>("JwtSettings:Enabled", false);
if (jwtEnabledForMiddleware)
{
    app.UseAuthentication();
    app.UseAuthorization();
    Console.WriteLine("🔐 认证中间件已启用");
}
else
{
    Console.WriteLine("⚠️ 认证中间件已禁用");
}

// 使用操作日志中间件
app.UseMiddleware<OperationLoggingMiddleware>();

app.MapControllers();

// 数据库初始化
using (var scope = app.Services.CreateScope())
{
    var dbInitializer = scope.ServiceProvider.GetRequiredService<DatabaseInitializer>();
    await dbInitializer.InitializeAsync();
}

app.Run();
