#!/bin/bash
echo "=== Git移动硬盘配置工具 ==="
echo "正在为map-studio项目配置Git环境..."
echo

# 检查当前全局用户配置
echo "[1/4] 检查现有Git配置..."
GLOBAL_NAME=$(git config --global user.name 2>/dev/null)
GLOBAL_EMAIL=$(git config --global user.email 2>/dev/null)

if [ -z "$GLOBAL_NAME" ]; then
    echo "    未检测到全局用户配置，设置临时配置..."
    git config --global user.name "glau"
    git config --global user.email "<EMAIL>"
    echo "    ✓ 已设置临时全局配置"
else
    echo "    检测到现有全局配置: $GLOBAL_NAME <$GLOBAL_EMAIL>"
    echo "    将保持不变，使用项目级配置覆盖"
fi

# 设置项目级用户配置（优先级最高）
echo
echo "[2/4] 设置项目专用Git配置..."
git config --local user.name "glau"
git config --local user.email "<EMAIL>"
echo "    ✓ 项目用户配置: glau <<EMAIL>>"

# 添加当前目录为安全目录
echo
echo "[3/4] 添加安全目录配置..."
CURRENT_DIR=$(pwd)
git config --global --add safe.directory "$CURRENT_DIR"
echo "    ✓ 安全目录: $CURRENT_DIR"

# 设置仓库特定配置
echo
echo "[4/4] 优化仓库配置..."
git config --local core.autocrlf input
git config --local core.ignorecase false
git config --local core.filemode false
git config --local core.symlinks false
echo "    ✓ 跨平台兼容性配置完成"

# 验证最终配置
echo
echo "=== 配置验证 ==="
echo "生效的用户名: $(git config user.name)"
echo "生效的邮箱: $(git config user.email)"
echo
echo "✅ Git配置完成！现在可以正常提交代码了"
echo "💡 提示：移除硬盘时不会影响电脑的全局Git设置"
echo