import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: () => import('@/components/Layout.vue'),
      children: [
        // 默认路由 - 首页
        {
          path: '',
          name: 'Home',
          component: () => import('@/views/Home.vue')
        },

        // 租户管理路由
        {
          path: 'tenant/auth-apply',
          name: 'AuthApply',
          component: () => import('@/views/tenant/AuthApply.vue')
        },
        {
          path: 'tenant/auth-approval-list',
          name: 'AuthApprovalList',
          component: () => import('@/views/tenant/AuthApprovalList.vue')
        },
        {
          path: 'tenant/auth-approval/:id',
          name: 'AuthApproval',
          component: () => import('@/views/tenant/AuthApproval.vue')
        },
        {
          path: 'tenant/auth-destroy',
          name: 'AuthDestroy',
          component: () => import('@/views/tenant/AuthDestroy.vue')
        },
        {
          path: 'tenant/auth-progress',
          name: 'AuthProgress',
          component: () => import('@/views/tenant/AuthProgress.vue')
        },
        {
          path: 'tenant/auth-progress-detail/:id',
          name: 'AuthProgressDetail',
          component: () => import('@/views/tenant/AuthProgressDetail.vue')
        },
        {
          path: 'tenant/service-status',
          name: 'ServiceStatus',
          component: () => import('@/views/tenant/ServiceStatus.vue')
        },

        // 运营管理路由
        {
          path: 'operation/access-stats',
          name: 'AccessStats',
          component: () => import('@/views/operation/AccessStats.vue')
        },
        {
          path: 'operation/action-type-rank',
          name: 'ActionTypeRank',
          component: () => import('@/views/operation/ActionTypeRank.vue')
        },
        {
          path: 'operation/alarm-setting',
          name: 'AlarmSetting',
          component: () => import('@/views/operation/AlarmSetting.vue')
        },
        {
          path: 'operation/daily-stats',
          name: 'DailyStats',
          component: () => import('@/views/operation/DailyStats.vue')
        },
        {
          path: 'operation/log-list',
          name: 'LogList',
          component: () => import('@/views/operation/LogList.vue')
        },
        {
          path: 'operation/monthly-stats',
          name: 'MonthlyStats',
          component: () => import('@/views/operation/MonthlyStats.vue')
        },
        {
          path: 'operation/tenant-action-rank',
          name: 'TenantActionRank',
          component: () => import('@/views/operation/TenantActionRank.vue')
        },
        {
          path: 'operation/user-usage',
          name: 'UserUsage',
          component: () => import('@/views/operation/UserUsage.vue')
        },

        // 其他路由
        {
          path: 'other',
          name: 'Other',
          component: () => import('@/components/Empty.vue')
        },

        // API测试页面
        {
          path: 'api-test',
          name: 'ApiTest',
          component: () => import('@/views/ApiTestPage.vue')
        }
      ]
    },
    // 404 重定向
    {
      path: '/:pathMatch(.*)*',
      redirect: '/'
    }
  ]
})

export default router
