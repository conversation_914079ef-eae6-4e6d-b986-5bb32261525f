2025-09-12 09:53:23.002 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 09:53:23.038 +08:00 [INF] CORS policy execution successful.
2025-09-12 09:53:23.042 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 40.9154ms
2025-09-12 09:53:23.046 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 09:53:23.055 +08:00 [INF] CORS policy execution successful.
2025-09-12 09:53:23.474 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 404 0 null 428.3936ms
2025-09-12 09:53:23.485 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7007/api/operation/service-status-stats, Response status code: 404
2025-09-12 09:54:55.486 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 09:54:55.492 +08:00 [INF] CORS policy execution successful.
2025-09-12 09:54:55.493 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 7.5235ms
2025-09-12 09:54:55.495 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 09:54:55.497 +08:00 [INF] CORS policy execution successful.
2025-09-12 09:54:55.618 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 404 0 null 123.4308ms
2025-09-12 09:54:55.621 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7007/api/operation/service-status-stats, Response status code: 404
2025-09-12 10:02:38.666 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:02:38.671 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:02:38.673 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 7.7244ms
2025-09-12 10:02:38.680 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:02:38.684 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:03:04.588 +08:00 [INF] 开始初始化数据库...
2025-09-12 10:03:11.501 +08:00 [INF] 数据库表创建完成
2025-09-12 10:03:11.928 +08:00 [INF] 数据库初始化完成
2025-09-12 10:03:11.950 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-12 10:03:12.221 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-12 10:03:12.223 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-12 10:03:12.303 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-12 10:03:12.304 +08:00 [INF] Hosting environment: Development
2025-09-12 10:03:12.305 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-12 10:03:21.821 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:03:21.882 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:03:21.887 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 67.2092ms
2025-09-12 10:03:21.890 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:03:21.907 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:03:21.913 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:03:21.937 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:03:27.349 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:03:34.570 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:03:34.574 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:03:34.576 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 6.1579ms
2025-09-12 10:03:34.578 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:03:34.580 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:03:34.581 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:03:34.582 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:03:37.947 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:03:48.559 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:03:48.920 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:03:49.375 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 815.7231ms
2025-09-12 10:03:49.377 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:03:49.379 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=144, 待审批申请=1
2025-09-12 10:03:49.380 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:03:53.015 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:03:54.620 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:03:54.628 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:03:57.982 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:03:59.579 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=144, 待审批申请=1
2025-09-12 10:03:59.581 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 36414.1106ms
2025-09-12 10:04:01.601 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:04:01.881 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:04:01.920 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:04:02.738 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:04:02.738 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 1137.8005ms
2025-09-12 10:04:02.740 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:04:04.026 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 29441.9018ms
2025-09-12 10:04:04.365 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:04:04.366 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:04:04.367 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:04:04.370 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:04:09.327 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:04:24.601 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=144, 待审批申请=1
2025-09-12 10:04:24.634 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:04:25.703 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 499 null application/json; charset=utf-8 51124.1029ms
2025-09-12 10:04:25.703 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 499 null application/json; charset=utf-8 63811.5022ms
2025-09-12 10:04:25.811 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 31188.9632ms
2025-09-12 10:04:25.817 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:04:25.927 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 499 null application/json; charset=utf-8 36550.2428ms
2025-09-12 10:04:25.937 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=146, 待审批申请=1
2025-09-12 10:04:25.938 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:04:26.178 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 21805.5849ms
2025-09-12 10:04:26.179 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:04:26.410 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 499 null application/json; charset=utf-8 23670.4052ms
2025-09-12 10:04:52.728 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:04:52.740 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:04:52.741 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 13.2865ms
2025-09-12 10:04:52.743 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:04:52.748 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:04:52.749 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:04:52.750 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:05:00.157 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:05:12.013 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:05:12.040 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=148, 待审批申请=1
2025-09-12 10:05:12.040 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:05:14.838 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:05:14.839 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 2827.0615ms
2025-09-12 10:05:15.074 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 22321.4006ms
2025-09-12 10:05:15.076 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:05:15.209 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 499 null application/json; charset=utf-8 22465.7666ms
2025-09-12 10:05:16.521 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:05:16.523 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:05:16.524 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:05:16.525 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:05:20.246 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:05:21.172 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=149, 待审批申请=1
2025-09-12 10:05:21.173 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:05:21.175 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 4648.5195ms
2025-09-12 10:05:21.176 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:05:21.307 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 4785.7762ms
2025-09-12 10:05:31.806 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:05:31.808 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:05:31.809 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 2.6888ms
2025-09-12 10:05:31.811 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:05:31.813 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:05:31.815 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:05:31.816 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:05:31.898 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:05:32.189 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=150, 待审批申请=1
2025-09-12 10:05:32.191 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:05:32.192 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 373.6057ms
2025-09-12 10:05:32.193 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:05:32.285 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 474.356ms
2025-09-12 10:05:34.785 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:05:34.787 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:05:34.788 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:05:34.789 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:05:34.857 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:05:35.139 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=151, 待审批申请=1
2025-09-12 10:05:35.142 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:05:35.143 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 352.0528ms
2025-09-12 10:05:35.144 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:05:35.284 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 498.423ms
2025-09-12 10:05:54.832 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:05:54.834 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:05:54.835 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 2.8562ms
2025-09-12 10:05:54.836 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:05:54.838 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:05:54.839 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:05:54.841 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:05:54.905 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:05:55.229 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=152, 待审批申请=1
2025-09-12 10:05:55.231 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:05:55.232 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 388.8691ms
2025-09-12 10:05:55.233 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:05:55.385 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 548.7394ms
2025-09-12 10:19:33.456 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:19:33.463 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:19:33.463 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 7.2081ms
2025-09-12 10:19:33.465 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:19:33.468 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:19:33.469 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:19:33.470 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:19:33.815 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:19:34.108 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=157, 待审批申请=1
2025-09-12 10:19:34.110 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:19:34.111 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 640.2596ms
2025-09-12 10:19:34.112 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:19:34.289 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 823.7286ms
2025-09-12 10:20:25.400 +08:00 [INF] 开始初始化数据库...
2025-09-12 10:20:30.874 +08:00 [INF] 数据库表创建完成
2025-09-12 10:20:31.411 +08:00 [INF] 数据库初始化完成
2025-09-12 10:20:31.425 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-12 10:20:31.548 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-12 10:20:31.549 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-12 10:20:31.599 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-12 10:20:31.601 +08:00 [INF] Hosting environment: Development
2025-09-12 10:20:31.602 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-12 10:20:34.577 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:20:34.630 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:20:34.636 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 60.2745ms
2025-09-12 10:20:34.643 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:20:34.674 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:20:34.680 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:20:34.701 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:20:34.861 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:20:35.209 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=158, 待审批申请=1
2025-09-12 10:20:35.220 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:20:35.241 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 530.9678ms
2025-09-12 10:20:35.243 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:20:35.432 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 788.7757ms
2025-09-12 10:20:48.471 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:20:48.500 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:20:48.503 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 31.7849ms
2025-09-12 10:20:48.506 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:20:48.516 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:20:48.517 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:20:48.519 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:20:48.610 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:20:48.913 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=159, 待审批申请=1
2025-09-12 10:20:48.915 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:20:48.917 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 391.3114ms
2025-09-12 10:20:48.918 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:20:49.055 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 549.2267ms
2025-09-12 10:22:08.789 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:22:08.791 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:22:08.792 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 2.7459ms
2025-09-12 10:22:08.793 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:22:08.798 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:22:08.800 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:22:08.801 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:22:08.876 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:22:09.147 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=162, 待审批申请=1
2025-09-12 10:22:09.149 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:22:09.152 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 347.2392ms
2025-09-12 10:22:09.156 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:22:09.349 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 555.7665ms
2025-09-12 10:23:04.411 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:23:04.421 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:23:04.423 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 11.8494ms
2025-09-12 10:23:04.425 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:23:04.430 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:23:04.431 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:23:04.432 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:23:04.509 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:23:04.820 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=163, 待审批申请=1
2025-09-12 10:23:04.824 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:23:04.826 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 391.5453ms
2025-09-12 10:23:04.828 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:23:04.978 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 552.3509ms
2025-09-12 10:23:14.098 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:23:14.100 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:23:14.103 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 4.8898ms
2025-09-12 10:23:14.106 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:23:14.112 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:23:14.113 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:23:14.114 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:23:14.221 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:23:14.532 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=164, 待审批申请=1
2025-09-12 10:23:14.534 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:23:14.536 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 420.0382ms
2025-09-12 10:23:14.539 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:23:14.769 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 662.9794ms
2025-09-12 10:25:15.084 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:25:15.086 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:25:15.088 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 3.7601ms
2025-09-12 10:25:15.090 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:25:15.108 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:25:15.120 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:25:15.145 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:25:15.285 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:25:15.578 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=165, 待审批申请=1
2025-09-12 10:25:15.580 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:25:15.585 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 410.3349ms
2025-09-12 10:25:15.588 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:25:15.776 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 686.5158ms
2025-09-12 10:25:26.773 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:25:26.776 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:25:26.777 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 4.573ms
2025-09-12 10:25:26.779 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:25:26.788 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:25:26.789 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:25:26.790 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:25:26.865 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:25:27.166 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=166, 待审批申请=1
2025-09-12 10:25:27.168 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:25:27.169 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 372.4983ms
2025-09-12 10:25:27.170 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:25:27.315 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 536.3132ms
2025-09-12 10:27:17.762 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 10:27:17.762 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:27:17.764 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:27:17.765 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:27:17.766 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 4.3098ms
2025-09-12 10:27:17.767 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 10:27:17.767 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 5.8099ms
2025-09-12 10:27:17.769 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:27:17.771 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:27:17.777 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:27:17.780 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:27:17.781 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:27:17.913 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 404 0 null 145.577ms
2025-09-12 10:27:17.918 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7007/api/operation/six-month-usage-stats, Response status code: 404
2025-09-12 10:27:18.082 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:27:18.354 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=168, 待审批申请=1
2025-09-12 10:27:18.356 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:27:18.358 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 572.1517ms
2025-09-12 10:27:18.361 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:27:18.544 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 775.1648ms
2025-09-12 10:28:51.616 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:28:51.616 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 10:28:51.627 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:28:51.632 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:28:51.634 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 17.8537ms
2025-09-12 10:28:51.642 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:28:51.643 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 26.3097ms
2025-09-12 10:28:51.646 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 10:28:51.650 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:28:51.663 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:28:51.667 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:28:51.672 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:28:51.777 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:28:51.809 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 404 0 null 163.4099ms
2025-09-12 10:28:51.813 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7007/api/operation/six-month-usage-stats, Response status code: 404
2025-09-12 10:28:52.130 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=170, 待审批申请=1
2025-09-12 10:28:52.131 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:28:52.133 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 454.4128ms
2025-09-12 10:28:52.134 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:28:52.294 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 652.3315ms
2025-09-12 10:33:27.776 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:33:27.776 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 10:33:27.778 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:33:27.781 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:33:27.783 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 7.3727ms
2025-09-12 10:33:27.784 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 8.0262ms
2025-09-12 10:33:27.785 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:33:27.787 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 10:33:27.792 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:33:27.793 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:33:27.794 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:33:27.795 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:33:28.046 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:33:28.110 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 404 0 null 323.0674ms
2025-09-12 10:33:28.136 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7007/api/operation/six-month-usage-stats, Response status code: 404
2025-09-12 10:33:28.395 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=172, 待审批申请=1
2025-09-12 10:33:30.309 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:33:30.738 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 2932.7279ms
2025-09-12 10:33:30.842 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:33:31.002 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 3216.9511ms
2025-09-12 10:33:34.589 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 10:33:34.589 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:33:34.592 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:33:34.594 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:33:34.595 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 5.4904ms
2025-09-12 10:33:34.596 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 6.4766ms
2025-09-12 10:33:34.596 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 10:33:34.598 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:33:34.606 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:33:34.608 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:33:34.611 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:33:34.612 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:33:34.705 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:33:34.749 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 404 0 null 151.9681ms
2025-09-12 10:33:34.752 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7007/api/operation/six-month-usage-stats, Response status code: 404
2025-09-12 10:33:34.959 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=174, 待审批申请=1
2025-09-12 10:33:34.961 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:33:34.962 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 346.8384ms
2025-09-12 10:33:34.965 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:33:35.080 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 481.5694ms
2025-09-12 10:33:56.791 +08:00 [INF] 开始初始化数据库...
2025-09-12 10:34:02.941 +08:00 [INF] 数据库表创建完成
2025-09-12 10:34:03.442 +08:00 [INF] 数据库初始化完成
2025-09-12 10:34:03.466 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-12 10:34:03.749 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-12 10:34:03.750 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-12 10:34:03.833 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-12 10:34:03.834 +08:00 [INF] Hosting environment: Development
2025-09-12 10:34:03.835 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-12 10:34:03.997 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 10:34:03.997 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:34:04.055 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:34:04.055 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:34:04.062 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 67.0338ms
2025-09-12 10:34:04.062 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 66.3013ms
2025-09-12 10:34:04.068 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:34:04.068 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 10:34:04.095 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:34:04.095 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:34:04.100 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 10:34:04.100 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:34:04.122 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:34:04.122 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:34:16.363 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:34:16.363 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:34:16.364 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 10:34:16.366 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:34:16.368 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:34:16.369 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 5.2822ms
2025-09-12 10:34:16.369 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 5.8608ms
2025-09-12 10:34:16.371 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:34:16.371 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 10:34:16.379 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:34:16.381 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:34:16.381 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:34:16.382 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 10:34:16.385 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:34:16.385 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:34:19.289 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 10:34:21.459 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 10:34:23.301 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 10:34:23.461 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=175, 待审批申请=1
2025-09-12 10:34:23.471 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:34:23.592 +08:00 [INF] 成功获取服务状态统计数据：总用户数=4, 活跃用户=1, 今日访问量=175, 待审批申请=1
2025-09-12 10:34:23.660 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:34:23.789 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 19661.4954ms
2025-09-12 10:34:23.789 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 7398.1327ms
2025-09-12 10:34:23.791 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:34:23.792 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:34:23.966 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 499 null application/json; charset=utf-8 19898.0033ms
2025-09-12 10:34:24.000 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 7629.7311ms
2025-09-12 10:34:24.902 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=942, 平均月操作量=157, 增长率=null%
2025-09-12 10:34:24.904 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:34:24.915 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=942, 平均月操作量=157, 增长率=null%
2025-09-12 10:34:24.916 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 10:34:24.922 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 8529.8403ms
2025-09-12 10:34:25.041 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 10:34:25.146 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 21016.4674ms
2025-09-12 10:34:25.147 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 10:34:25.310 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 499 null application/json; charset=utf-8 21241.5827ms
2025-09-12 10:34:25.326 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 8954.8747ms
2025-09-12 10:56:16.689 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 10:56:16.689 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:56:16.695 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:56:16.695 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:56:16.697 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 7.9026ms
2025-09-12 10:56:16.697 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 7.9026ms
2025-09-12 10:56:16.703 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 10:56:16.702 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 10:56:16.713 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:56:16.714 +08:00 [INF] CORS policy execution successful.
2025-09-12 10:56:16.715 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 10:56:16.715 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 10:56:16.716 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 10:56:16.717 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:01:21.645 +08:00 [INF] 开始初始化数据库...
2025-09-12 11:01:37.942 +08:00 [ERR] 数据库初始化失败
SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="".
English Message : Connection open error . Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.DbMaintenanceProvider.IsAnySystemTablePermissions()
   at SqlSugar.CodeFirstProvider.InitTables(Type entityType)
   at SqlSugar.CodeFirstProvider.InitTables[T]()
   at MapStudio.Api.Data.Context.DatabaseInitializer.InitializeAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Data\Context\DatabaseInitializer.cs:line 25
2025-09-12 11:01:48.618 +08:00 [INF] 开始初始化数据库...
2025-09-12 11:01:54.320 +08:00 [ERR] 数据库初始化失败
SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="".
English Message : Connection open error . Unable to connect to any of the specified MySQL hostsDbType="MySql";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.DbMaintenanceProvider.IsAnySystemTablePermissions()
   at SqlSugar.CodeFirstProvider.InitTables(Type entityType)
   at SqlSugar.CodeFirstProvider.InitTables[T]()
   at MapStudio.Api.Data.Context.DatabaseInitializer.InitializeAsync() in D:\路网通项目SVN\1_后台专题类模块\map-studio\backend\Data\Context\DatabaseInitializer.cs:line 25
2025-09-12 11:02:49.514 +08:00 [INF] 开始初始化数据库...
2025-09-12 11:02:56.676 +08:00 [INF] 数据库表创建完成
2025-09-12 11:02:57.174 +08:00 [INF] 数据库初始化完成
2025-09-12 11:02:57.201 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-12 11:02:57.450 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-12 11:02:57.451 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-12 11:02:57.508 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-12 11:02:57.509 +08:00 [INF] Hosting environment: Development
2025-09-12 11:02:57.509 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-12 11:03:42.120 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-12 11:03:42.195 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:03:42.202 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 84.5384ms
2025-09-12 11:03:42.207 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-12 11:03:42.229 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:03:42.235 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-12 11:03:42.262 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-12 11:03:42.395 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 11:03:42.417 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 147.72ms
2025-09-12 11:03:42.419 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-12 11:03:42.620 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 412.5658ms
2025-09-12 11:03:45.777 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:03:45.777 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:03:45.777 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:03:45.782 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:03:45.788 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:03:45.791 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:03:45.804 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:03:45.835 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 57.154ms
2025-09-12 11:03:45.804 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 26.8032ms
2025-09-12 11:03:45.794 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 16.5549ms
2025-09-12 11:03:45.834 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:03:45.845 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:03:45.861 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:03:45.862 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:03:45.863 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:03:45.874 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:03:45.874 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:03:49.388 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:03:50.739 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:03:51.381 +08:00 [INF] 成功获取运行状况统计数据：总日志数=955, 状态码种类数=5
2025-09-12 11:03:51.382 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:03:51.389 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 5497.8007ms
2025-09-12 11:03:51.393 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:03:51.559 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 5725.1983ms
2025-09-12 11:03:51.561 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:03:51.565 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:03:51.566 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:03:51.567 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:03:52.891 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:03:53.402 +08:00 [INF] 成功获取运行状况统计数据：总日志数=956, 状态码种类数=5
2025-09-12 11:03:53.404 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:03:53.406 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 1835.0547ms
2025-09-12 11:03:53.407 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:03:53.561 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 1999.2522ms
2025-09-12 11:03:53.653 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=956, 平均月操作量=159.33333333333334, 增长率=null%
2025-09-12 11:03:53.656 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:03:53.668 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 7786.6624ms
2025-09-12 11:03:53.670 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:03:53.808 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 8003.5067ms
2025-09-12 11:04:16.415 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:04:16.415 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:04:16.415 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:04:16.418 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:04:16.420 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:04:16.424 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 9.1331ms
2025-09-12 11:04:16.435 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 19.6729ms
2025-09-12 11:04:16.423 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:04:16.434 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:04:16.441 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:04:16.456 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 40.7433ms
2025-09-12 11:04:16.459 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:04:16.464 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:04:16.475 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:04:16.476 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:04:16.477 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:04:16.479 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:04:16.672 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:04:16.673 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:04:17.015 +08:00 [INF] 成功获取运行状况统计数据：总日志数=958, 状态码种类数=5
2025-09-12 11:04:17.016 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:04:17.018 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 533.3785ms
2025-09-12 11:04:17.019 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:04:17.247 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 812.869ms
2025-09-12 11:04:17.249 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:04:17.252 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:04:17.253 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:04:17.254 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:04:17.412 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:04:17.810 +08:00 [INF] 成功获取运行状况统计数据：总日志数=959, 状态码种类数=5
2025-09-12 11:04:17.811 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:04:17.813 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 554.5981ms
2025-09-12 11:04:17.814 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:04:17.947 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 698.1492ms
2025-09-12 11:04:18.592 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=960, 平均月操作量=160, 增长率=null%
2025-09-12 11:04:18.594 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:04:18.597 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 2110.252ms
2025-09-12 11:04:18.598 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:04:18.743 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 2301.5575ms
2025-09-12 11:04:58.871 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:04:58.871 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:04:58.877 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:04:58.886 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:04:58.890 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:04:58.892 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:04:58.894 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 23.0148ms
2025-09-12 11:04:58.896 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 25.075ms
2025-09-12 11:04:58.896 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:04:58.903 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 26.9101ms
2025-09-12 11:04:58.904 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:04:58.912 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:04:58.916 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:04:58.917 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:04:58.917 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:04:58.918 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:04:58.921 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:04:58.996 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:04:58.996 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:04:59.207 +08:00 [INF] 成功获取运行状况统计数据：总日志数=961, 状态码种类数=5
2025-09-12 11:04:59.208 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:04:59.210 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 285.6534ms
2025-09-12 11:04:59.212 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:04:59.382 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 485.6467ms
2025-09-12 11:04:59.383 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:04:59.387 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:04:59.388 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:04:59.389 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:04:59.497 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:04:59.745 +08:00 [INF] 成功获取运行状况统计数据：总日志数=962, 状态码种类数=5
2025-09-12 11:04:59.747 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:04:59.749 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 354.7145ms
2025-09-12 11:04:59.751 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:04:59.920 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 536.6541ms
2025-09-12 11:05:00.539 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=963, 平均月操作量=160.5, 增长率=null%
2025-09-12 11:05:00.540 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:05:00.542 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1614.8741ms
2025-09-12 11:05:00.544 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:05:00.723 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1818.7515ms
2025-09-12 11:05:34.709 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:05:34.715 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:05:34.715 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:05:34.716 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:05:34.720 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:05:34.728 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 18.6962ms
2025-09-12 11:05:34.735 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 21.3602ms
2025-09-12 11:05:34.724 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:05:34.739 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:05:34.742 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:05:34.754 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 39.292ms
2025-09-12 11:05:34.756 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:05:34.758 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:05:34.764 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:05:34.765 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:05:34.766 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:05:34.767 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:05:41.567 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:05:43.171 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:05:43.667 +08:00 [INF] 成功获取运行状况统计数据：总日志数=964, 状态码种类数=5
2025-09-12 11:06:37.232 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:06:37.270 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:06:37.270 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:06:37.270 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:06:37.447 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:37.553 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:37.308 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:37.571 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 499 null null 300.8447ms
2025-09-12 11:06:37.679 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 62900.7032ms
2025-09-12 11:06:37.587 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 499 null null 316.7649ms
2025-09-12 11:06:37.655 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 499 null null 383.7592ms
2025-09-12 11:06:37.704 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:06:37.770 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:06:37.770 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:06:37.770 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:06:37.770 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:06:37.770 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:06:37.770 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:06:37.770 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:06:37.772 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:37.773 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:37.775 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:37.776 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:37.777 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:37.779 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:37.782 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:37.783 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 12.7094ms
2025-09-12 11:06:37.783 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 13.3128ms
2025-09-12 11:06:37.784 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 13.927ms
2025-09-12 11:06:37.785 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 14.4667ms
2025-09-12 11:06:37.785 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 15.0004ms
2025-09-12 11:06:37.786 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 15.7016ms
2025-09-12 11:06:37.787 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 16.3992ms
2025-09-12 11:06:37.789 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:06:37.793 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:06:37.795 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-12 11:06:37.797 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-12 11:06:37.799 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-12 11:06:37.803 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - null null
2025-09-12 11:06:37.807 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:37.808 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:06:37.808 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:37.810 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:37.811 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:37.814 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:37.816 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:37.816 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 27.6067ms
2025-09-12 11:06:37.818 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:37.818 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 25.8344ms
2025-09-12 11:06:37.819 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 24.2103ms
2025-09-12 11:06:37.820 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 22.3586ms
2025-09-12 11:06:37.820 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 21.1458ms
2025-09-12 11:06:37.821 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/Metadata/service-types - 204 null null 17.6147ms
2025-09-12 11:06:37.826 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:06:37.829 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:06:37.829 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-12 11:06:37.829 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-12 11:06:37.830 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-12 11:06:37.840 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:06:37.841 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:37.844 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:37.845 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 499 null application/json; charset=utf-8 63104.1735ms
2025-09-12 11:06:37.847 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:37.848 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:37.850 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:06:37.851 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-12 11:06:37.857 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-12 11:06:37.857 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-12 11:06:37.858 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:06:37.860 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-12 11:06:37.865 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-12 11:06:37.865 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-12 11:06:39.588 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:06:39.588 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/Metadata/service-types - null null
2025-09-12 11:06:39.591 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:39.591 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-12 11:06:39.592 +08:00 [INF] Route matched with {action = "GetServiceTypes", controller = "Metadata"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.ServiceTypeOption]]]] GetServiceTypes() on controller MapStudio.Api.Controllers.MetadataController (MapStudio.Api).
2025-09-12 11:06:39.773 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 11:06:41.617 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:06:41.656 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-12 11:06:41.814 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 3946.1249ms
2025-09-12 11:06:41.814 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-12 11:06:41.815 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:41.817 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-12 11:06:41.819 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:41.820 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-12 11:06:41.821 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-12 11:06:41.822 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-12 11:06:41.824 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-12 11:06:43.813 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:06:43.816 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:43.817 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:06:43.818 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:06:44.812 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:06:44.814 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:44.815 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:06:44.816 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:06:45.815 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.ServiceTypeOption, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 11:06:45.817 +08:00 [INF] Executed action MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api) in 6222.0293ms
2025-09-12 11:06:45.819 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.MetadataController.GetServiceTypes (MapStudio.Api)'
2025-09-12 11:06:45.892 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 11:06:45.942 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 200 null application/json; charset=utf-8 6353.768ms
2025-09-12 11:06:45.980 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/Metadata/service-types - 499 null application/json; charset=utf-8 8111.4099ms
2025-09-12 11:06:46.018 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 11:06:48.583 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 10712.3013ms
2025-09-12 11:06:50.536 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:06:51.192 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:06:51.206 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 11:06:51.208 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 9379.0187ms
2025-09-12 11:06:51.209 +08:00 [INF] 成功获取运行状况统计数据：总日志数=965, 状态码种类数=5
2025-09-12 11:06:51.215 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-12 11:06:51.293 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 11:06:53.390 +08:00 [INF] 成功获取运行状况统计数据：总日志数=967, 状态码种类数=5
2025-09-12 11:06:53.408 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 11751.3572ms
2025-09-12 11:06:54.587 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:06:56.414 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=965, 平均月操作量=160.83333333333334, 增长率=null%
2025-09-12 11:06:54.532 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:06:56.499 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 14668.07ms
2025-09-12 11:06:57.676 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:57.842 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-12 11:06:57.889 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 3289.9074ms
2025-09-12 11:06:57.672 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:06:57.696 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:06:57.938 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 20072.4887ms
2025-09-12 11:06:53.382 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 15509.6614ms
2025-09-12 11:06:58.259 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 14437.7441ms
2025-09-12 11:06:58.181 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:06:58.357 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:06:58.259 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-12 11:06:51.209 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-12 11:06:54.587 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:06:54.534 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:06:54.534 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-12 11:06:54.532 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - null null
2025-09-12 11:06:57.890 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:06:58.132 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 499 null application/json; charset=utf-8 16318.2828ms
2025-09-12 11:06:58.386 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 83616.8276ms
2025-09-12 11:06:58.393 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:58.394 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:58.396 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:58.397 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:58.398 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:58.403 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:06:58.405 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:06:58.408 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/phones - null null
2025-09-12 11:06:58.408 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - null null
2025-09-12 11:06:58.404 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 3817.3163ms
2025-09-12 11:06:58.405 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 3872.721ms
2025-09-12 11:06:58.406 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/sms-template - 204 null null 3874.0024ms
2025-09-12 11:06:58.407 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/SendMsg/phones - 204 null null 3874.7083ms
2025-09-12 11:06:58.408 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:06:58.417 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:58.418 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:58.420 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:06:58.432 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:06:58.432 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:06:58.433 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-12 11:06:58.434 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-12 11:06:58.438 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:06:58.438 +08:00 [INF] Route matched with {action = "GetSmsPhones", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto]]]] GetSmsPhones() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-12 11:06:58.439 +08:00 [INF] Route matched with {action = "GetSmsTemplate", controller = "SendMsg"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[System.Collections.Generic.List`1[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto]]]] GetSmsTemplate() on controller MapStudio.Api.Controllers.SendMsgController (MapStudio.Api).
2025-09-12 11:06:59.359 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 499 null application/json; charset=utf-8 21529.9367ms
2025-09-12 11:07:02.725 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:07:02.726 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 499 null application/json; charset=utf-8 24894.6384ms
2025-09-12 11:07:02.725 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 499 null application/json; charset=utf-8 24895.5513ms
2025-09-12 11:07:03.430 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 499 null application/json; charset=utf-8 88690.485ms
2025-09-12 11:07:03.430 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 499 null application/json; charset=utf-8 19616.6083ms
2025-09-12 11:07:03.430 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:07:03.511 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsTemplateSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 11:07:03.512 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[System.Collections.Generic.List`1[[MapStudio.Api.Models.DTOs.SmsPhoneSettingDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-09-12 11:07:03.514 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api) in 5066.6024ms
2025-09-12 11:07:03.516 +08:00 [INF] Executed action MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api) in 5070.4389ms
2025-09-12 11:07:03.517 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsTemplate (MapStudio.Api)'
2025-09-12 11:07:03.518 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.SendMsgController.GetSmsPhones (MapStudio.Api)'
2025-09-12 11:07:03.700 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/phones - 200 null application/json; charset=utf-8 5291.624ms
2025-09-12 11:07:03.771 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/SendMsg/sms-template - 200 null application/json; charset=utf-8 5363.2785ms
2025-09-12 11:07:03.885 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=969, 平均月操作量=161.5, 增长率=null%
2025-09-12 11:07:03.931 +08:00 [INF] 成功获取运行状况统计数据：总日志数=974, 状态码种类数=5
2025-09-12 11:07:12.794 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:07:12.794 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:07:12.796 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:07:12.796 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:07:13.280 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:07:12.796 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:07:13.192 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 14754.7615ms
2025-09-12 11:07:13.311 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 35461.3946ms
2025-09-12 11:07:13.312 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:07:13.313 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 516.6858ms
2025-09-12 11:07:13.315 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:07:13.316 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:07:13.317 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:07:13.318 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:07:13.319 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 522.3522ms
2025-09-12 11:07:13.322 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:07:13.323 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 526.6286ms
2025-09-12 11:07:13.324 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:07:13.330 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:07:13.333 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:07:13.334 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:07:13.337 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:07:13.340 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:07:14.436 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:07:14.436 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 499 null application/json; charset=utf-8 36627.5907ms
2025-09-12 11:07:14.436 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 499 null application/json; charset=utf-8 16545.3977ms
2025-09-12 11:07:15.307 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:07:15.399 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=975, 平均月操作量=162.5, 增长率=null%
2025-09-12 11:07:16.676 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:07:16.908 +08:00 [INF] 成功获取运行状况统计数据：总日志数=978, 状态码种类数=5
2025-09-12 11:07:16.959 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 32140.2943ms
2025-09-12 11:07:16.959 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:07:16.961 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:07:16.963 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 3623.4003ms
2025-09-12 11:07:16.964 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:07:17.117 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 499 null application/json; charset=utf-8 32305.3839ms
2025-09-12 11:07:17.148 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 3833.2095ms
2025-09-12 11:07:17.149 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:07:17.152 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:07:17.152 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:07:17.153 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:07:18.243 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:07:18.500 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=978, 平均月操作量=163, 增长率=null%
2025-09-12 11:07:19.167 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:07:19.264 +08:00 [INF] 成功获取运行状况统计数据：总日志数=980, 状态码种类数=5
2025-09-12 11:07:19.351 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:07:19.397 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 20953.9533ms
2025-09-12 11:07:19.399 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 2243.7763ms
2025-09-12 11:07:19.401 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:07:19.402 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:07:19.587 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 499 null application/json; charset=utf-8 21181.7212ms
2025-09-12 11:07:19.631 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 2482.1475ms
2025-09-12 11:07:20.103 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=980, 平均月操作量=163.33333333333334, 增长率=null%
2025-09-12 11:07:20.645 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:07:20.893 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 7550.9897ms
2025-09-12 11:07:20.896 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:07:21.089 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 499 null application/json; charset=utf-8 7764.8887ms
2025-09-12 11:07:27.103 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:07:27.103 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:07:27.103 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:07:27.106 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:07:27.109 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:07:27.111 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:07:27.112 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 8.8258ms
2025-09-12 11:07:27.114 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:07:27.116 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 12.7925ms
2025-09-12 11:07:27.119 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 15.3794ms
2025-09-12 11:07:27.120 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:07:27.127 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:07:27.140 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:07:27.141 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:07:27.142 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:07:27.145 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:07:27.146 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:07:30.716 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:07:30.716 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:07:31.099 +08:00 [INF] 成功获取运行状况统计数据：总日志数=983, 状态码种类数=5
2025-09-12 11:07:31.100 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:07:31.101 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 3952.3034ms
2025-09-12 11:07:31.103 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:07:31.215 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 4100.9711ms
2025-09-12 11:07:31.216 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:07:31.221 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:07:31.221 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:07:31.222 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:07:31.293 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:07:31.533 +08:00 [INF] 成功获取运行状况统计数据：总日志数=984, 状态码种类数=5
2025-09-12 11:07:31.534 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:07:31.536 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 309.4325ms
2025-09-12 11:07:31.537 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:07:31.647 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 430.8212ms
2025-09-12 11:07:32.092 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=984, 平均月操作量=164, 增长率=null%
2025-09-12 11:07:32.094 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:07:32.095 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 4943.0254ms
2025-09-12 11:07:32.097 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:07:32.448 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 5328.0937ms
2025-09-12 11:23:53.102 +08:00 [INF] 开始初始化数据库...
2025-09-12 11:23:59.079 +08:00 [INF] 数据库表创建完成
2025-09-12 11:23:59.601 +08:00 [INF] 数据库初始化完成
2025-09-12 11:23:59.625 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-12 11:23:59.867 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-12 11:23:59.868 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-12 11:23:59.941 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-12 11:23:59.943 +08:00 [INF] Hosting environment: Development
2025-09-12 11:23:59.944 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-12 11:24:04.585 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:24:04.585 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:24:04.585 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:24:04.654 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:24:04.654 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:24:04.654 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:24:04.659 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 76.8608ms
2025-09-12 11:24:04.659 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 76.7734ms
2025-09-12 11:24:04.659 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 76.8609ms
2025-09-12 11:24:04.663 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:24:04.663 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:24:04.663 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:24:04.683 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:24:04.683 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:24:04.683 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:24:04.688 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:24:04.688 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:24:04.688 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:24:04.707 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:24:04.707 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:24:04.707 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:24:04.828 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:24:05.008 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:24:05.014 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 11:24:05.344 +08:00 [INF] 成功获取服务状态统计数据：总用户数=5, 活跃用户=3, 今日访问量=264, 待审批申请=1
2025-09-12 11:24:05.354 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:24:05.385 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 666.8539ms
2025-09-12 11:24:05.388 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:24:05.469 +08:00 [INF] 查询到操作日志总数: 1029
2025-09-12 11:24:05.476 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1029, 状态码种类数=5
2025-09-12 11:24:05.477 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:24:05.488 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 776.6559ms
2025-09-12 11:24:05.490 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:24:05.558 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 895.056ms
2025-09-12 11:24:05.628 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 965.256ms
2025-09-12 11:24:06.249 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1031, 平均月操作量=171.83333333333334, 增长率=null%
2025-09-12 11:24:06.251 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:24:06.263 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1553.9451ms
2025-09-12 11:24:06.264 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:24:06.416 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1752.705ms
2025-09-12 11:27:21.602 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:27:21.607 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:27:21.607 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:27:21.613 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:27:21.616 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:27:21.619 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 16.5234ms
2025-09-12 11:27:21.645 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 38.4225ms
2025-09-12 11:27:21.618 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:27:21.645 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:27:21.655 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:27:21.660 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 52.8012ms
2025-09-12 11:27:21.662 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:27:21.662 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:27:21.668 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:27:21.672 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:27:21.673 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:27:21.674 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:27:21.675 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:27:21.677 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:27:21.680 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:27:21.684 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:27:21.758 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 11:27:21.759 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:27:21.766 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:27:22.086 +08:00 [INF] 成功获取服务状态统计数据：总用户数=5, 活跃用户=3, 今日访问量=268, 待审批申请=1
2025-09-12 11:27:22.089 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:27:22.093 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 410.3556ms
2025-09-12 11:27:22.095 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:27:22.119 +08:00 [INF] 查询到操作日志总数: 1033
2025-09-12 11:27:22.122 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1033, 状态码种类数=5
2025-09-12 11:27:22.122 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:27:22.124 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 433.4675ms
2025-09-12 11:27:22.125 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:27:22.220 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 574.8178ms
2025-09-12 11:27:22.248 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 592.9229ms
2025-09-12 11:27:23.089 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1035, 平均月操作量=172.5, 增长率=null%
2025-09-12 11:27:23.091 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:27:23.094 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1400.2751ms
2025-09-12 11:27:23.095 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:27:23.283 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1620.7695ms
2025-09-12 11:27:40.991 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:27:40.991 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:27:40.991 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:27:40.993 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:27:40.995 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:27:41.004 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:27:41.005 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 14.7487ms
2025-09-12 11:27:41.008 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 17.2864ms
2025-09-12 11:27:41.009 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:27:41.017 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:27:41.011 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:27:41.007 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 15.9111ms
2025-09-12 11:27:41.033 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:27:41.037 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:27:41.039 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:27:41.043 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:27:41.044 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:27:41.047 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:27:41.049 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:27:41.053 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:27:41.054 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:27:41.122 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:27:41.122 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:27:41.130 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 11:27:41.403 +08:00 [INF] 成功获取服务状态统计数据：总用户数=5, 活跃用户=3, 今日访问量=272, 待审批申请=1
2025-09-12 11:27:41.405 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:27:41.407 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 337.7538ms
2025-09-12 11:27:41.408 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:27:41.481 +08:00 [INF] 查询到操作日志总数: 1037
2025-09-12 11:27:41.485 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1037, 状态码种类数=5
2025-09-12 11:27:41.486 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:27:41.488 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 425.8979ms
2025-09-12 11:27:41.489 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:27:41.565 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 553.7017ms
2025-09-12 11:27:41.656 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 639.0097ms
2025-09-12 11:27:42.410 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1039, 平均月操作量=173.16666666666666, 增长率=null%
2025-09-12 11:27:42.411 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:27:42.413 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1354.4467ms
2025-09-12 11:27:42.414 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:27:42.542 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1533.354ms
2025-09-12 11:28:05.988 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:28:05.991 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:28:05.994 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:28:06.004 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:28:06.006 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:28:06.010 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:28:06.011 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 23.455ms
2025-09-12 11:28:06.012 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 20.6324ms
2025-09-12 11:28:06.014 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 20.3788ms
2025-09-12 11:28:06.015 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:28:06.021 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:28:06.024 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:28:06.040 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:28:06.041 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:28:06.042 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:28:06.043 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:28:06.044 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:28:06.044 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:28:06.045 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:28:06.047 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:28:06.053 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:28:06.150 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:28:06.150 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 11:28:06.171 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:28:06.440 +08:00 [INF] 成功获取服务状态统计数据：总用户数=5, 活跃用户=3, 今日访问量=275, 待审批申请=1
2025-09-12 11:28:06.442 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:28:06.444 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 387.0903ms
2025-09-12 11:28:06.445 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:28:06.584 +08:00 [INF] 查询到操作日志总数: 1040
2025-09-12 11:28:06.587 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1040, 状态码种类数=5
2025-09-12 11:28:06.588 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 573.524ms
2025-09-12 11:28:06.588 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:28:06.596 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 530.8147ms
2025-09-12 11:28:06.602 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:28:06.748 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 724.4482ms
2025-09-12 11:28:07.516 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1042, 平均月操作量=173.66666666666666, 增长率=null%
2025-09-12 11:28:07.519 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:28:07.520 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1461.1492ms
2025-09-12 11:28:07.521 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:28:07.682 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1661.0462ms
2025-09-12 11:28:10.160 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:28:10.160 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:28:10.160 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:28:10.165 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:28:10.168 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:28:10.173 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:28:10.174 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:28:10.175 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:28:10.177 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:28:10.178 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:28:10.184 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:28:10.186 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:28:10.252 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 11:28:10.260 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:28:10.268 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:28:10.562 +08:00 [INF] 成功获取服务状态统计数据：总用户数=5, 活跃用户=3, 今日访问量=278, 待审批申请=1
2025-09-12 11:28:10.566 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:28:10.569 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 377.1458ms
2025-09-12 11:28:10.570 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:28:10.581 +08:00 [INF] 查询到操作日志总数: 1043
2025-09-12 11:28:10.584 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1043, 状态码种类数=5
2025-09-12 11:28:10.585 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:28:10.587 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 378.1692ms
2025-09-12 11:28:10.588 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:28:10.681 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 521.1817ms
2025-09-12 11:28:10.698 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 537.6548ms
2025-09-12 11:28:11.531 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1045, 平均月操作量=174.16666666666666, 增长率=null%
2025-09-12 11:28:11.533 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:28:11.535 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1334.5396ms
2025-09-12 11:28:11.537 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:28:11.643 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1482.6875ms
2025-09-12 11:30:00.627 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:30:00.627 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:30:00.643 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:30:00.645 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:00.647 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:00.650 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 23.3676ms
2025-09-12 11:30:00.655 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 27.9898ms
2025-09-12 11:30:00.650 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:00.655 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:30:00.665 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:30:00.674 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 31.2036ms
2025-09-12 11:30:00.676 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:30:00.677 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:00.680 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:00.683 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:00.684 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:30:00.684 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:30:00.685 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:30:00.686 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:30:00.687 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:30:00.689 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:30:00.751 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 11:30:00.752 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:30:00.753 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:30:01.038 +08:00 [INF] 查询到操作日志总数: 1046
2025-09-12 11:30:01.042 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1046, 状态码种类数=5
2025-09-12 11:30:01.046 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:30:01.050 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 353.4491ms
2025-09-12 11:30:01.052 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:30:01.066 +08:00 [INF] 成功获取服务状态统计数据：总用户数=5, 活跃用户=3, 今日访问量=281, 待审批申请=1
2025-09-12 11:30:01.067 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:30:01.068 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 376.2473ms
2025-09-12 11:30:01.072 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:30:01.194 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 518.03ms
2025-09-12 11:30:01.219 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 563.6177ms
2025-09-12 11:30:02.090 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1048, 平均月操作量=174.66666666666666, 增长率=null%
2025-09-12 11:30:02.092 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:30:02.095 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1400.9767ms
2025-09-12 11:30:02.098 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:30:02.223 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1557.6009ms
2025-09-12 11:30:07.265 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:30:07.265 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:30:07.266 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:30:07.268 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:07.270 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:07.272 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:07.273 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 7.7721ms
2025-09-12 11:30:07.274 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 8.9749ms
2025-09-12 11:30:07.277 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:30:07.277 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:30:07.279 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 12.8728ms
2025-09-12 11:30:07.280 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:30:07.286 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:07.289 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:07.295 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:07.299 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:30:07.300 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:30:07.301 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:30:07.302 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:30:07.303 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:30:07.304 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:30:07.449 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:30:07.449 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 11:30:07.450 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:30:07.751 +08:00 [INF] 查询到操作日志总数: 1049
2025-09-12 11:30:07.752 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1049, 状态码种类数=5
2025-09-12 11:30:07.753 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:30:07.754 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 447.8313ms
2025-09-12 11:30:07.755 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:30:07.788 +08:00 [INF] 成功获取服务状态统计数据：总用户数=5, 活跃用户=3, 今日访问量=284, 待审批申请=1
2025-09-12 11:30:07.789 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:30:07.792 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 476.1673ms
2025-09-12 11:30:07.794 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:30:07.885 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 608.6348ms
2025-09-12 11:30:07.924 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 647.4728ms
2025-09-12 11:30:08.679 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1051, 平均月操作量=175.16666666666666, 增长率=null%
2025-09-12 11:30:08.681 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:30:08.682 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1363.998ms
2025-09-12 11:30:08.684 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:30:08.797 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1516.6681ms
2025-09-12 11:30:17.162 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:30:17.162 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:30:17.162 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:30:17.164 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:17.165 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:17.167 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:17.168 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 5.7423ms
2025-09-12 11:30:17.168 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 6.4173ms
2025-09-12 11:30:17.169 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 7.238ms
2025-09-12 11:30:17.169 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:30:17.170 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:30:17.170 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:30:17.181 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:17.184 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:17.185 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:17.187 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:30:17.187 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:30:17.188 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:30:17.189 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:30:17.189 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:30:17.195 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:30:17.261 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 11:30:17.264 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:30:17.264 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:30:17.573 +08:00 [INF] 成功获取服务状态统计数据：总用户数=5, 活跃用户=3, 今日访问量=287, 待审批申请=1
2025-09-12 11:30:17.577 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:30:17.580 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 382.6494ms
2025-09-12 11:30:17.582 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:30:17.594 +08:00 [INF] 查询到操作日志总数: 1052
2025-09-12 11:30:17.596 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1052, 状态码种类数=5
2025-09-12 11:30:17.597 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:30:17.599 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 397.6799ms
2025-09-12 11:30:17.600 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:30:17.708 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 538.3511ms
2025-09-12 11:30:17.724 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 553.4107ms
2025-09-12 11:30:18.619 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1054, 平均月操作量=175.66666666666666, 增长率=null%
2025-09-12 11:30:18.620 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:30:18.621 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1421.9507ms
2025-09-12 11:30:18.622 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:30:18.727 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1556.7746ms
2025-09-12 11:30:29.910 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:30:29.910 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:30:29.910 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:30:29.911 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:29.913 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:29.914 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:29.915 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 5.2533ms
2025-09-12 11:30:29.916 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 6.0678ms
2025-09-12 11:30:29.916 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 6.794ms
2025-09-12 11:30:29.917 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:30:29.917 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:30:29.918 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:30:29.927 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:29.928 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:29.932 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:29.932 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:30:29.933 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:30:29.933 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:30:29.935 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:30:29.936 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:30:29.936 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:30:29.998 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:30:29.999 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 11:30:30.002 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:30:30.238 +08:00 [INF] 查询到操作日志总数: 1055
2025-09-12 11:30:30.240 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1055, 状态码种类数=5
2025-09-12 11:30:30.242 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:30:30.246 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 307.0783ms
2025-09-12 11:30:30.249 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:30:30.290 +08:00 [INF] 成功获取服务状态统计数据：总用户数=5, 活跃用户=3, 今日访问量=290, 待审批申请=1
2025-09-12 11:30:30.292 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:30:30.295 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 351.4409ms
2025-09-12 11:30:30.297 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:30:30.396 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 478.967ms
2025-09-12 11:30:30.439 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 521.5958ms
2025-09-12 11:30:31.220 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1057, 平均月操作量=176.16666666666666, 增长率=null%
2025-09-12 11:30:31.221 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:30:31.223 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1277.3113ms
2025-09-12 11:30:31.225 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:30:31.360 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1442.3058ms
2025-09-12 11:30:37.123 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:30:37.130 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:30:37.131 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:30:37.135 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:37.139 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:37.137 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:37.144 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 21.1887ms
2025-09-12 11:30:37.145 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 14.0675ms
2025-09-12 11:30:37.147 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:30:37.147 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 17.4261ms
2025-09-12 11:30:37.147 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:30:37.149 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:30:37.158 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:37.167 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:37.169 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:37.170 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:30:37.170 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:30:37.171 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:30:37.172 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:30:37.173 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:30:37.178 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:30:37.305 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 11:30:37.306 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:30:37.307 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:30:37.569 +08:00 [INF] 查询到操作日志总数: 1058
2025-09-12 11:30:37.570 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1058, 状态码种类数=5
2025-09-12 11:30:37.571 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:30:37.573 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 384.2644ms
2025-09-12 11:30:37.580 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:30:37.590 +08:00 [INF] 成功获取服务状态统计数据：总用户数=5, 活跃用户=3, 今日访问量=293, 待审批申请=1
2025-09-12 11:30:37.593 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:30:37.596 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 414.7523ms
2025-09-12 11:30:37.597 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:30:37.713 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 564.0647ms
2025-09-12 11:30:37.730 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 583.3824ms
2025-09-12 11:30:38.469 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1060, 平均月操作量=176.66666666666666, 增长率=null%
2025-09-12 11:30:38.470 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:30:38.472 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1288.4138ms
2025-09-12 11:30:38.473 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:30:38.583 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1435.5443ms
2025-09-12 11:30:53.388 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:30:53.388 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:30:53.388 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:30:53.390 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:53.393 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:53.395 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:53.396 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 7.8813ms
2025-09-12 11:30:53.397 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 8.756ms
2025-09-12 11:30:53.397 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:30:53.398 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:30:53.399 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 11.4228ms
2025-09-12 11:30:53.400 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:30:53.405 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:53.407 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:53.414 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:30:53.414 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:30:53.415 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:30:53.416 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:30:53.417 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:30:53.418 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:30:53.421 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:30:53.495 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:30:53.527 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:30:53.527 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 11:30:53.750 +08:00 [INF] 查询到操作日志总数: 1061
2025-09-12 11:30:53.751 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1061, 状态码种类数=5
2025-09-12 11:30:53.752 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:30:53.754 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 329.8214ms
2025-09-12 11:30:53.755 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:30:53.818 +08:00 [INF] 成功获取服务状态统计数据：总用户数=5, 活跃用户=3, 今日访问量=296, 待审批申请=1
2025-09-12 11:30:53.820 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:30:53.821 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 389.4679ms
2025-09-12 11:30:53.822 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:30:53.877 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 479.713ms
2025-09-12 11:30:53.931 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 530.1792ms
2025-09-12 11:30:54.789 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1063, 平均月操作量=177.16666666666666, 增长率=null%
2025-09-12 11:30:54.791 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:30:54.793 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1365.2575ms
2025-09-12 11:30:54.794 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:30:54.952 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1553.5954ms
2025-09-12 11:31:22.631 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:31:22.632 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:31:22.637 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:31:22.638 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:31:22.640 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:31:22.645 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 13.6344ms
2025-09-12 11:31:22.650 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 17.9599ms
2025-09-12 11:31:22.644 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:31:22.655 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:31:22.655 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:31:22.667 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 30.8393ms
2025-09-12 11:31:22.669 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:31:22.669 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:31:22.673 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:31:22.678 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:31:22.679 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:31:22.679 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:31:22.681 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:31:22.682 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:31:22.683 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:31:22.684 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:31:22.744 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:31:22.746 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 11:31:22.749 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:31:23.002 +08:00 [INF] 查询到操作日志总数: 1064
2025-09-12 11:31:23.004 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1064, 状态码种类数=5
2025-09-12 11:31:23.004 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:31:23.006 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 312.154ms
2025-09-12 11:31:23.008 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:31:23.040 +08:00 [INF] 成功获取服务状态统计数据：总用户数=5, 活跃用户=3, 今日访问量=299, 待审批申请=1
2025-09-12 11:31:23.043 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:31:23.046 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 358.0767ms
2025-09-12 11:31:23.047 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:31:23.152 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 483.5306ms
2025-09-12 11:31:23.176 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 520.9896ms
2025-09-12 11:31:23.940 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1066, 平均月操作量=177.66666666666666, 增长率=null%
2025-09-12 11:31:23.941 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:31:23.943 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1256.995ms
2025-09-12 11:31:23.944 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:31:24.065 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1410.1721ms
2025-09-12 11:31:45.264 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:31:45.264 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:31:45.264 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:31:45.273 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:31:45.276 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:31:45.281 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:31:45.282 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 17.6632ms
2025-09-12 11:31:45.283 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 18.7668ms
2025-09-12 11:31:45.294 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 29.502ms
2025-09-12 11:31:45.284 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:31:45.290 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:31:45.313 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:31:45.326 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:31:45.328 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:31:45.330 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:31:45.331 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:31:45.332 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:31:45.333 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:31:45.336 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:31:45.337 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:31:45.338 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:31:45.432 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 11:31:45.474 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:31:45.475 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:31:45.699 +08:00 [INF] 查询到操作日志总数: 1067
2025-09-12 11:31:45.701 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1067, 状态码种类数=5
2025-09-12 11:31:45.702 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:31:45.703 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 350.2625ms
2025-09-12 11:31:45.705 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:31:45.715 +08:00 [INF] 成功获取服务状态统计数据：总用户数=5, 活跃用户=3, 今日访问量=302, 待审批申请=1
2025-09-12 11:31:45.716 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:31:45.719 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 376.8643ms
2025-09-12 11:31:45.725 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:31:45.839 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 525.6299ms
2025-09-12 11:31:45.862 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 578.406ms
2025-09-12 11:31:46.724 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1069, 平均月操作量=178.16666666666666, 增长率=null%
2025-09-12 11:31:46.726 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:31:46.728 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1378.2338ms
2025-09-12 11:31:46.729 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:31:46.834 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1543.5205ms
2025-09-12 11:32:24.863 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:32:24.867 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:32:24.870 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:32:24.871 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:32:24.875 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:32:24.880 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 17.1048ms
2025-09-12 11:32:24.889 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 24.1767ms
2025-09-12 11:32:24.878 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:32:24.907 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:32:24.907 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:32:24.915 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 44.9269ms
2025-09-12 11:32:24.918 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:32:24.924 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:32:24.926 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:32:24.930 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:32:24.935 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:32:24.943 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:32:24.944 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:32:24.945 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:32:24.946 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:32:24.947 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:32:25.009 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:32:25.016 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 11:32:25.023 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:32:25.302 +08:00 [INF] 查询到操作日志总数: 1070
2025-09-12 11:32:25.304 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1070, 状态码种类数=5
2025-09-12 11:32:25.305 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:32:25.306 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 345.4327ms
2025-09-12 11:32:25.307 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:32:25.343 +08:00 [INF] 成功获取服务状态统计数据：总用户数=5, 活跃用户=3, 今日访问量=305, 待审批申请=1
2025-09-12 11:32:25.344 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:32:25.346 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 388.0352ms
2025-09-12 11:32:25.347 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:32:25.438 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 520.0462ms
2025-09-12 11:32:25.471 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 563.9499ms
2025-09-12 11:32:26.405 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1072, 平均月操作量=178.66666666666666, 增长率=null%
2025-09-12 11:32:28.120 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:32:30.214 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 5264.8206ms
2025-09-12 11:32:31.734 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:32:32.531 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 7623.8863ms
2025-09-12 11:33:02.550 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:33:02.551 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:33:02.556 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:33:02.563 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:33:02.565 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:33:02.567 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:33:02.567 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 16.9924ms
2025-09-12 11:33:02.570 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 19.1648ms
2025-09-12 11:33:02.575 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 19.3284ms
2025-09-12 11:33:02.569 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:33:02.575 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:33:02.580 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:33:02.587 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:33:02.590 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:33:02.592 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:33:02.593 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:33:02.593 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:33:02.594 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:33:02.595 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:33:02.595 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:33:02.596 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:33:02.672 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 11:33:02.674 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:33:02.683 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:33:02.975 +08:00 [INF] 查询到操作日志总数: 1073
2025-09-12 11:33:02.987 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1073, 状态码种类数=5
2025-09-12 11:33:02.988 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:33:02.991 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 381.8735ms
2025-09-12 11:33:02.992 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:33:03.017 +08:00 [INF] 成功获取服务状态统计数据：总用户数=5, 活跃用户=3, 今日访问量=308, 待审批申请=1
2025-09-12 11:33:03.018 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:33:03.020 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 419.3889ms
2025-09-12 11:33:03.022 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:33:03.156 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 580.7066ms
2025-09-12 11:33:03.194 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 624.9283ms
2025-09-12 11:33:04.144 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1075, 平均月操作量=179.16666666666666, 增长率=null%
2025-09-12 11:33:04.145 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:33:04.146 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1534.1379ms
2025-09-12 11:33:04.148 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:33:04.262 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1682.6382ms
2025-09-12 11:34:14.687 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:34:14.687 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:34:14.694 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:34:14.702 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:34:14.708 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:34:14.710 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 23.7173ms
2025-09-12 11:34:14.719 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 31.304ms
2025-09-12 11:34:14.710 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:34:14.717 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:34:14.727 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:34:14.743 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 49.8314ms
2025-09-12 11:34:14.745 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:34:14.746 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:34:14.748 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:34:14.755 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:34:14.764 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:34:14.764 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:34:14.765 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:34:14.766 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:34:14.768 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:34:14.769 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:34:14.876 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 11:34:14.876 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:34:14.895 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:34:15.183 +08:00 [INF] 查询到操作日志总数: 1085
2025-09-12 11:34:15.186 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1085, 状态码种类数=5
2025-09-12 11:34:15.187 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:34:15.190 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 408.2969ms
2025-09-12 11:34:15.191 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:34:15.297 +08:00 [INF] 成功获取服务状态统计数据：总用户数=6, 活跃用户=3, 今日访问量=320, 待审批申请=2
2025-09-12 11:34:15.299 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:34:15.300 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 522.534ms
2025-09-12 11:34:15.302 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:34:15.378 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 651.1701ms
2025-09-12 11:34:15.427 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 709.9011ms
2025-09-12 11:34:16.255 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1087, 平均月操作量=181.16666666666666, 增长率=null%
2025-09-12 11:34:16.258 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:34:16.259 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1474.6507ms
2025-09-12 11:34:16.260 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:34:16.379 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1633.7717ms
2025-09-12 11:44:00.805 +08:00 [INF] 开始初始化数据库...
2025-09-12 11:44:06.896 +08:00 [INF] 数据库表创建完成
2025-09-12 11:44:07.335 +08:00 [INF] 数据库初始化完成
2025-09-12 11:44:07.357 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-12 11:44:07.578 +08:00 [INF] Now listening on: https://localhost:7007
2025-09-12 11:44:07.578 +08:00 [INF] Now listening on: http://localhost:5172
2025-09-12 11:44:07.636 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-12 11:44:07.638 +08:00 [INF] Hosting environment: Development
2025-09-12 11:44:07.640 +08:00 [INF] Content root path: D:\路网通项目SVN\1_后台专题类模块\map-studio\backend
2025-09-12 11:44:49.256 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:44:49.256 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:44:49.256 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:44:49.325 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:44:49.325 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:44:49.325 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:44:49.330 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 78.148ms
2025-09-12 11:44:49.330 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 78.1475ms
2025-09-12 11:44:49.330 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 78.1472ms
2025-09-12 11:44:49.335 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 11:44:49.335 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 11:44:49.335 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 11:44:49.353 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:44:49.353 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:44:49.353 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:44:49.357 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:44:49.357 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:44:49.357 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:44:49.374 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:44:49.374 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:44:49.374 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:44:49.491 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 11:44:49.649 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 11:44:49.666 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 11:44:49.973 +08:00 [INF] 成功获取服务状态统计数据：总用户数=6, 活跃用户=3, 今日访问量=341, 待审批申请=2
2025-09-12 11:44:49.984 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:44:50.003 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 622.7707ms
2025-09-12 11:44:50.005 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 11:44:50.030 +08:00 [INF] 查询到操作日志总数: 1106
2025-09-12 11:44:50.038 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1106, 状态码种类数=5
2025-09-12 11:44:50.040 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:44:50.045 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 665.7685ms
2025-09-12 11:44:50.046 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 11:44:50.156 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 820.3247ms
2025-09-12 11:44:50.186 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 850.8693ms
2025-09-12 11:44:50.856 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1108, 平均月操作量=184.66666666666666, 增长率=null%
2025-09-12 11:44:50.858 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:44:50.871 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1493.6767ms
2025-09-12 11:44:50.873 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 11:44:50.994 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1658.4032ms
2025-09-12 11:44:59.555 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/logs?page=1&pageSize=20 - null null
2025-09-12 11:44:59.557 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:44:59.558 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/logs?page=1&pageSize=20 - 204 null null 3.6602ms
2025-09-12 11:44:59.560 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/logs?page=1&pageSize=20 - null null
2025-09-12 11:44:59.562 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:44:59.563 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetOperationLogs (MapStudio.Api)'
2025-09-12 11:44:59.574 +08:00 [INF] Route matched with {action = "GetOperationLogs", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.OperationLogListResponse]]] GetOperationLogs(Int32, Int32, System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:44:59.803 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.OperationLogListResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:44:59.809 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetOperationLogs (MapStudio.Api) in 233.25ms
2025-09-12 11:44:59.810 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetOperationLogs (MapStudio.Api)'
2025-09-12 11:44:59.957 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/logs?page=1&pageSize=20 - 200 null application/json; charset=utf-8 396.7684ms
2025-09-12 11:46:52.625 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 11:46:52.631 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:46:52.632 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 6.9722ms
2025-09-12 11:46:52.634 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 11:46:52.636 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:46:52.636 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 11:46:52.643 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:46:52.704 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:46:52.711 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 66.2409ms
2025-09-12 11:46:52.712 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 11:46:52.833 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 198.617ms
2025-09-12 11:47:56.404 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 11:47:56.406 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:47:56.407 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 3.3465ms
2025-09-12 11:47:56.412 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 11:47:56.415 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:47:56.416 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 11:47:56.417 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:47:56.475 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:47:56.477 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 57.8164ms
2025-09-12 11:47:56.478 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 11:47:56.593 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 181.3219ms
2025-09-12 11:50:52.257 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 11:50:52.288 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:50:52.290 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 33.9405ms
2025-09-12 11:50:52.291 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 11:50:52.295 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:50:52.297 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 11:50:52.298 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:50:52.386 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:50:52.387 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 86.4876ms
2025-09-12 11:50:52.388 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 11:50:52.498 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 207.2699ms
2025-09-12 11:51:37.051 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 11:51:37.053 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:51:37.054 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 2.9931ms
2025-09-12 11:51:37.055 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 11:51:37.058 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:51:37.058 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 11:51:37.059 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:51:37.154 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:51:37.156 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 95.3395ms
2025-09-12 11:51:37.157 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 11:51:37.300 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 244.6684ms
2025-09-12 11:52:17.834 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 11:52:17.843 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:52:17.844 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 9.78ms
2025-09-12 11:52:17.845 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 11:52:17.849 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:52:17.849 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 11:52:17.850 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:52:17.911 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:52:17.912 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 59.4247ms
2025-09-12 11:52:17.912 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 11:52:18.024 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 178.3371ms
2025-09-12 11:52:27.382 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?selectedTenant=TENANT-2025001&viewMode=bar - null null
2025-09-12 11:52:27.384 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:52:27.384 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?selectedTenant=TENANT-2025001&viewMode=bar - 204 null null 2.7982ms
2025-09-12 11:52:27.386 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?selectedTenant=TENANT-2025001&viewMode=bar - null null
2025-09-12 11:52:27.396 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:52:27.396 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 11:52:27.398 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:52:27.458 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:52:27.459 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 59.1979ms
2025-09-12 11:52:27.459 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 11:52:27.584 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?selectedTenant=TENANT-2025001&viewMode=bar - 200 null application/json; charset=utf-8 198.7079ms
2025-09-12 11:52:58.633 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 11:52:58.639 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:52:58.640 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 7.4357ms
2025-09-12 11:52:58.641 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 11:52:58.645 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:52:58.645 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 11:52:58.646 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:52:59.886 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:52:59.890 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 1243.041ms
2025-09-12 11:52:59.892 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 11:53:00.053 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 1411.514ms
2025-09-12 11:53:23.722 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 11:53:23.725 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:53:23.726 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 4.1743ms
2025-09-12 11:53:23.728 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 11:53:23.730 +08:00 [INF] CORS policy execution successful.
2025-09-12 11:53:23.732 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 11:53:23.733 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 11:53:23.793 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 11:53:23.795 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 60.5114ms
2025-09-12 11:53:23.796 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 11:53:23.930 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 201.6567ms
2025-09-12 12:14:27.472 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 12:14:27.472 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 12:14:27.473 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 12:14:27.503 +08:00 [INF] CORS policy execution successful.
2025-09-12 12:14:27.503 +08:00 [INF] CORS policy execution successful.
2025-09-12 12:14:27.507 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-stats - 204 null null 35.5564ms
2025-09-12 12:14:27.511 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/service-status-code-stats - 204 null null 39.0469ms
2025-09-12 12:14:27.505 +08:00 [INF] CORS policy execution successful.
2025-09-12 12:14:27.513 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - null null
2025-09-12 12:14:27.517 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - null null
2025-09-12 12:14:27.521 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/six-month-usage-stats - 204 null null 48.7306ms
2025-09-12 12:14:27.525 +08:00 [INF] CORS policy execution successful.
2025-09-12 12:14:27.526 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - null null
2025-09-12 12:14:27.528 +08:00 [INF] CORS policy execution successful.
2025-09-12 12:14:27.531 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 12:14:27.532 +08:00 [INF] CORS policy execution successful.
2025-09-12 12:14:27.532 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 12:14:27.533 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 12:14:27.533 +08:00 [INF] Route matched with {action = "GetServiceStatusStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto]]] GetServiceStatusStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 12:14:27.535 +08:00 [INF] Route matched with {action = "GetServiceStatusCodeStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto]]] GetServiceStatusCodeStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 12:14:27.536 +08:00 [INF] Route matched with {action = "GetSixMonthUsageStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto]]] GetSixMonthUsageStats() on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 12:14:27.808 +08:00 [INF] 开始获取近6个月使用率统计数据
2025-09-12 12:14:27.811 +08:00 [INF] 开始获取服务状态统计数据
2025-09-12 12:14:27.820 +08:00 [INF] 开始获取运行状况统计数据
2025-09-12 12:14:28.071 +08:00 [INF] 成功获取服务状态统计数据：总用户数=5, 活跃用户=5, 今日访问量=417, 待审批申请=1
2025-09-12 12:14:28.074 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 12:14:28.077 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api) in 540.2926ms
2025-09-12 12:14:28.078 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusStats (MapStudio.Api)'
2025-09-12 12:14:28.259 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-stats - 200 null application/json; charset=utf-8 746.6509ms
2025-09-12 12:14:28.271 +08:00 [INF] 查询到操作日志总数: 1182
2025-09-12 12:14:28.275 +08:00 [INF] 成功获取运行状况统计数据：总日志数=1182, 状态码种类数=5
2025-09-12 12:14:28.276 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.ServiceStatusStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 12:14:28.278 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api) in 739.9982ms
2025-09-12 12:14:28.279 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetServiceStatusCodeStats (MapStudio.Api)'
2025-09-12 12:14:28.395 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/service-status-code-stats - 200 null application/json; charset=utf-8 878.1757ms
2025-09-12 12:14:29.188 +08:00 [INF] 成功获取近6个月使用率统计数据：总操作量=1184, 平均月操作量=197.33333333333334, 增长率=null%
2025-09-12 12:14:29.191 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.SixMonthUsageStatsResponseDto, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 12:14:29.193 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api) in 1651.642ms
2025-09-12 12:14:29.194 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetSixMonthUsageStats (MapStudio.Api)'
2025-09-12 12:14:29.362 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/six-month-usage-stats - 200 null application/json; charset=utf-8 1836.5508ms
2025-09-12 13:56:05.190 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 13:56:05.206 +08:00 [INF] CORS policy execution successful.
2025-09-12 13:56:05.210 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 20.6762ms
2025-09-12 13:56:05.236 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 13:56:05.243 +08:00 [INF] CORS policy execution successful.
2025-09-12 13:56:05.246 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 13:56:05.252 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 13:56:05.570 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 13:56:05.576 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 322.2755ms
2025-09-12 13:56:05.577 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 13:56:05.844 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 607.6764ms
2025-09-12 13:56:32.708 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 13:56:32.715 +08:00 [INF] CORS policy execution successful.
2025-09-12 13:56:32.716 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 7.3454ms
2025-09-12 13:56:32.717 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 13:56:32.721 +08:00 [INF] CORS policy execution successful.
2025-09-12 13:56:32.721 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 13:56:32.722 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 13:56:32.785 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 13:56:32.788 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 62.8027ms
2025-09-12 13:56:32.788 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 13:56:32.933 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 216.119ms
2025-09-12 13:59:26.861 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 13:59:26.864 +08:00 [INF] CORS policy execution successful.
2025-09-12 13:59:26.866 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 6.5264ms
2025-09-12 13:59:26.880 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 13:59:26.886 +08:00 [INF] CORS policy execution successful.
2025-09-12 13:59:26.887 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 13:59:26.888 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 13:59:26.954 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 13:59:26.956 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 64.9279ms
2025-09-12 13:59:26.957 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 13:59:27.105 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 224.68ms
2025-09-12 14:01:38.892 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 14:01:38.901 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:01:38.904 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 11.9853ms
2025-09-12 14:01:38.906 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 14:01:38.910 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:01:38.911 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 14:01:38.912 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 14:01:38.983 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 14:01:38.985 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 71.3349ms
2025-09-12 14:01:38.987 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 14:01:39.098 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 192.5078ms
2025-09-12 14:01:44.849 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 14:01:44.864 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:01:44.866 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 16.8336ms
2025-09-12 14:01:44.884 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 14:01:44.902 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:01:44.904 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 14:01:44.907 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 14:01:44.967 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 14:01:44.968 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 59.0864ms
2025-09-12 14:01:44.970 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 14:01:45.086 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 201.9433ms
2025-09-12 14:17:20.725 +08:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 14:17:20.737 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:17:20.737 +08:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7007/api/operation/daily-stats?viewMode=bar - 204 null null 12.477ms
2025-09-12 14:17:20.739 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - null null
2025-09-12 14:17:20.741 +08:00 [INF] CORS policy execution successful.
2025-09-12 14:17:20.742 +08:00 [INF] Executing endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 14:17:20.743 +08:00 [INF] Route matched with {action = "GetDailyStats", controller = "Operation"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[MapStudio.Api.Models.ViewModels.ApiResponse`1[MapStudio.Api.Models.DTOs.DailyStatsResponse]]] GetDailyStats(System.String, System.String, System.String, System.String) on controller MapStudio.Api.Controllers.OperationController (MapStudio.Api).
2025-09-12 14:17:21.007 +08:00 [INF] Executing OkObjectResult, writing value of type 'MapStudio.Api.Models.ViewModels.ApiResponse`1[[MapStudio.Api.Models.DTOs.DailyStatsResponse, MapStudio.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-09-12 14:17:21.009 +08:00 [INF] Executed action MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api) in 265.2132ms
2025-09-12 14:17:21.010 +08:00 [INF] Executed endpoint 'MapStudio.Api.Controllers.OperationController.GetDailyStats (MapStudio.Api)'
2025-09-12 14:17:21.141 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7007/api/operation/daily-stats?viewMode=bar - 200 null application/json; charset=utf-8 401.7037ms
