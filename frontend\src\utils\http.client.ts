/**
 * HTTP客户端封装
 * 支持请求拦截、响应处理、错误管理和Mock数据切换
 */

import axios, { AxiosError } from 'axios';
import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  InternalAxiosRequestConfig
} from 'axios';
import { apiConfig } from '../config/api.config';
import { mockDataManager } from '../config/mock.data';
import { requiresAuth, getEndpointDescription } from '../config/endpoints';
import type { ApiResponse, ApiError } from '../services/types';

// 注意：ApiResponse 和 ApiError 类型现在从 ../services/types 导入，避免重复定义

// 请求重试配置
interface RetryConfig {
  attempts: number;
  delay: number;
  maxDelay: number;
}

export class HttpClient {
  private instance: AxiosInstance;
  private requestId = 0;
  private retryConfig: RetryConfig = {
    attempts: apiConfig.retries,
    delay: 1000,
    maxDelay: 10000
  };

  constructor() {
    this.instance = axios.create({
      baseURL: apiConfig.baseURL,
      timeout: apiConfig.timeout,
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Version': '1.0.0',
        'X-Client-Platform': 'web'
      }
    });

    this.setupInterceptors();
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        const requestId = ++this.requestId;
        config.metadata = { requestId, startTime: Date.now() };

        // 如果开启Mock模式，标记请求
        if (apiConfig.useMock) {
          config.headers['X-Mock-Mode'] = 'true';
          console.log(`🎭 [${requestId}] Mock请求:`, config.method?.toUpperCase(), config.url);
        } else {
          console.log(`🌐 [${requestId}] API请求:`, config.method?.toUpperCase(), config.url);
        }

        // 添加认证token
        if (requiresAuth(config.url || '')) {
          const token = this.getAuthToken();
          if (token) {
            config.headers.Authorization = `Bearer ${token}`;
          }
        }

        // 添加请求ID用于追踪
        config.headers['X-Request-ID'] = requestId.toString();

        return config;
      },
      (error: AxiosError) => {
        console.error('❌ 请求拦截器错误:', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        const { requestId, startTime } = response.config.metadata || {};
        const duration = Date.now() - (startTime || Date.now());

        console.log(`✅ [${requestId}] 响应成功 (${duration}ms):`, response.config.url);

        return response;
      },
      async (error: AxiosError) => {
        const { requestId, startTime } = error.config?.metadata || {};
        const duration = Date.now() - (startTime || Date.now());

        console.error(`❌ [${requestId}] 响应失败 (${duration}ms):`, error.config?.url, error.message);

        // 处理不同类型的错误
        return this.handleResponseError(error);
      }
    );
  }

  /**
   * 处理响应错误
   */
  private async handleResponseError(error: AxiosError): Promise<never> {
    const { response, config } = error;

    // 如果是401错误，清除认证信息并跳转登录
    if (response?.status === 401) {
      this.clearAuthToken();
      this.redirectToLogin();
      throw new Error('认证失效，请重新登录');
    }

    // 如果是网络错误且配置了重试，尝试重试
    if (!response && config && this.shouldRetry(error)) {
      return await this.retryRequest(config) as never;
    }

    // 处理其他错误
    const apiError: ApiError = {
      success: false,
      message: this.getErrorMessage(error),
      code: response?.status?.toString(),
      details: response?.data,
      timestamp: new Date().toISOString()
    };

    throw apiError;
  }

  /**
   * 判断是否应该重试
   */
  private shouldRetry(error: AxiosError): boolean {
    const { config } = error;
    const retryCount = (config as any).__retryCount || 0;

    // 超过重试次数
    if (retryCount >= this.retryConfig.attempts) {
      return false;
    }

    // 只重试网络错误或5xx错误
    const status = error.response?.status;
    return !status || status >= 500;
  }

  /**
   * 重试请求
   */
  private async retryRequest(config: any): Promise<any> {
    const retryCount = (config.__retryCount || 0) + 1;
    config.__retryCount = retryCount;

    const delay = Math.min(
      this.retryConfig.delay * Math.pow(2, retryCount - 1),
      this.retryConfig.maxDelay
    );

    console.log(`🔄 第${retryCount}次重试 (${delay}ms后):`, config.url);

    await new Promise(resolve => setTimeout(resolve, delay));
    return this.instance.request(config);
  }

  /**
   * 获取错误消息
   */
  private getErrorMessage(error: AxiosError): string {
    if (error.response?.data && typeof error.response.data === 'object') {
      const data = error.response.data as any;
      return data.message || data.error || '请求失败';
    }

    if (error.message) {
      return error.message;
    }

    return '网络错误，请检查连接';
  }

  /**
   * GET请求
   */
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    if (apiConfig.useMock) {
      return this.getMockResponse<T>(url, 'GET');
    }

    const response = await this.instance.get(url, config);
    return this.extractResponseData<T>(response.data);
  }

  /**
   * POST请求
   */
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    if (apiConfig.useMock) {
      return this.getMockResponse<T>(url, 'POST', data);
    }

    const response = await this.instance.post(url, data, config);
    return this.extractResponseData<T>(response.data);
  }

  /**
   * PUT请求
   */
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    if (apiConfig.useMock) {
      return this.getMockResponse<T>(url, 'PUT', data);
    }

    const response = await this.instance.put(url, data, config);
    return this.extractResponseData<T>(response.data);
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    if (apiConfig.useMock) {
      return this.getMockResponse<T>(url, 'DELETE');
    }

    const response = await this.instance.delete(url, config);
    return this.extractResponseData<T>(response.data);
  }

  /**
   * 文件上传
   */
  async upload<T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);

    if (apiConfig.useMock) {
      // Mock文件上传，模拟进度
      if (onProgress) {
        for (let i = 0; i <= 100; i += 10) {
          await new Promise(resolve => setTimeout(resolve, 100));
          onProgress(i);
        }
      }
      return this.getMockResponse<T>(url, 'POST', { fileName: file.name, fileSize: file.size });
    }

    const config: AxiosRequestConfig = {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      }
    };

    const response = await this.instance.post<T>(url, formData, config);
    return response.data;
  }

  /**
   * 提取响应数据，支持后端API的统一响应格式
   */
  private extractResponseData<T>(responseData: any): T {
    // 如果是后端的统一响应格式（包含success和data字段）
    if (responseData && typeof responseData === 'object' && 'success' in responseData && 'data' in responseData) {
      if (!responseData.success) {
        throw new Error(responseData.message || '请求失败');
      }
      return responseData.data as T;
    }

    // 如果不是统一格式，直接返回数据
    return responseData as T;
  }

  /**
   * 获取Mock响应
   */
  private async getMockResponse<T>(url: string, method: string, data?: any): Promise<T> {
    const mockResponse = mockDataManager.getMockResponse(url, method, data);

    // 模拟网络延迟
    if (mockResponse.delay) {
      await new Promise(resolve => setTimeout(resolve, mockResponse.delay));
    }

    // 如果Mock返回错误状态，抛出异常
    if (mockResponse.status >= 400) {
      const error: ApiError = {
        success: false,
        message: mockResponse.data.message || mockResponse.data.error || 'Mock请求失败',
        code: mockResponse.status.toString(),
        details: mockResponse.data,
        timestamp: new Date().toISOString()
      };
      throw error;
    }

    // 对Mock数据也使用相同的数据提取逻辑
    return this.extractResponseData<T>(mockResponse.data);
  }

  /**
   * 获取认证Token
   */
  private getAuthToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  /**
   * 清除认证Token
   */
  private clearAuthToken(): void {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_info');
  }

  /**
   * 跳转到登录页面
   */
  private redirectToLogin(): void {
    // 保存当前页面路径，登录后可以回到这里
    const currentPath = window.location.pathname + window.location.search;
    localStorage.setItem('redirect_after_login', currentPath);

    // 跳转到登录页面
    window.location.href = '/login';
  }

  /**
   * 检查网络连接状态
   */
  async checkConnection(): Promise<boolean> {
    try {
      if (apiConfig.useMock) {
        return true; // Mock模式总是可用
      }

      const response = await this.instance.get('/system/health', { timeout: 5000 });
      return response.status === 200;
    } catch (error) {
      console.warn('网络连接检查失败:', error);
      return false;
    }
  }

  /**
   * 取消所有待处理的请求
   */
  cancelAllRequests(): void {
    // 这里可以实现取消请求的逻辑
    console.log('取消所有待处理的请求');
  }

  /**
   * 获取基础URL
   */
  getBaseURL(): string {
    return this.instance.defaults.baseURL || '';
  }
}

// 导出单例实例
export const httpClient = new HttpClient();

// 扩展axios配置以支持metadata
declare module 'axios' {
  interface InternalAxiosRequestConfig {
    metadata?: {
      requestId: number;
      startTime: number;
    };
  }
}
