# 前端页面修改实现计划：ServiceStatus.vue

## 概述
本文档描述了如何修改ServiceStatus.vue组件，使其从后端API获取动态数据而不是使用静态数据。

## 实现步骤

### 1. 导入必要的模块
在ServiceStatus.vue文件的<script setup>部分添加必要的导入：

```typescript
import { ref, computed, onMounted, onUnmounted } from 'vue'
import ChartBase from '@/components/ChartBase.vue'
import { getServiceStatusStatistics } from '@/services/tenantService'
import type { ServiceStatusStatisticsResponse } from '@/services/types'
```

### 2. 定义响应式数据
替换原有的静态数据，定义响应式数据：

```typescript
// 服务状态统计数据
const serviceStatusData = ref<ServiceStatusStatisticsResponse>({
  totalUsers: 0,
  activeUsers: 0,
  todayVisits: 0,
  pendingApplications: 0
})

// 加载状态
const loading = ref(true)
const error = ref<string | null>(null)

// 自动刷新定时器
let refreshInterval: number | null = null
```

### 3. 创建数据加载方法
实现从后端获取数据的方法：

```typescript
/**
 * 加载服务状态统计数据
 */
const loadServiceStatusData = async () => {
  try {
    loading.value = true
    error.value = null
    const response = await getServiceStatusStatistics()
    
    if (response.success) {
      serviceStatusData.value = response.data
    } else {
      error.value = response.message || '获取数据失败'
    }
  } catch (err) {
    error.value = '网络错误，请稍后重试'
    console.error('加载服务状态数据失败:', err)
  } finally {
    loading.value = false
  }
}
```

### 4. 实现自动刷新功能
添加定时刷新数据的功能：

```typescript
/**
 * 开始自动刷新
 */
const startAutoRefresh = () => {
  // 每30秒刷新一次数据
  refreshInterval = window.setInterval(() => {
    loadServiceStatusData()
  }, 30000)
}

/**
 * 停止自动刷新
 */
const stopAutoRefresh = () => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
    refreshInterval = null
  }
}
```

### 5. 更新生命周期钩子
修改onMounted和添加onUnmounted钩子：

```typescript
// 初始化图表数据和加载服务状态数据
onMounted(() => {
  // 模拟数据加载
  setTimeout(() => {
    chartLoading.value = false
  }, 500)
  
  // 加载服务状态数据
  loadServiceStatusData()
  
  // 启动自动刷新
  startAutoRefresh()
})

// 组件卸载时清理资源
onUnmounted(() => {
  stopAutoRefresh()
})
```

### 6. 更新模板中的数据绑定
修改模板中核心指标卡片的数据绑定：

```vue
<!-- 核心指标卡片 -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
  <!-- 总用户数 -->
  <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm font-medium text-gray-500">总用户数</p>
        <h3 class="mt-1 text-2xl font-bold text-gray-900">{{ serviceStatusData.totalUsers }}</h3>
      </div>
      <div class="p-3 bg-blue-100 rounded-lg">
        <i class="fa-solid fa-building text-blue-600 text-xl"></i>
      </div>
    </div>
  </div>

  <!-- 活跃用户 -->
  <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm font-medium text-gray-500">活跃用户</p>
        <h3 class="mt-1 text-2xl font-bold text-gray-900">{{ serviceStatusData.activeUsers }}</h3>
      </div>
      <div class="p-3 bg-purple-100 rounded-lg">
        <i class="fa-solid fa-eye text-purple-600 text-xl"></i>
      </div>
    </div>
  </div>

  <!-- 今日访问量 -->
  <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm font-medium text-gray-500">今日访问量</p>
        <h3 class="mt-1 text-2xl font-bold text-gray-900">{{ serviceStatusData.todayVisits }}</h3>
      </div>
      <div class="p-3 bg-yellow-100 rounded-lg">
        <i class="fa-solid fa-clock text-yellow-600 text-xl"></i>
      </div>
    </div>
  </div>

  <!-- 待审批申请 -->
  <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm font-medium text-gray-500">待审批申请</p>
        <h3 class="mt-1 text-2xl font-bold text-gray-900">{{ serviceStatusData.pendingApplications }}</h3>
      </div>
      <div class="p-3 bg-green-100 rounded-lg">
        <i class="fa-solid fa-shield text-green-600 text-xl"></i>
      </div>
    </div>
  </div>
</div>
```

### 7. 添加加载状态和错误处理
在核心指标卡片区域上方添加加载状态和错误处理：

```vue
<!-- 加载状态 -->
<div v-if="loading" class="flex justify-center items-center h-32">
  <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
  <span class="ml-2 text-gray-500">加载中...</span>
</div>

<!-- 错误状态 -->
<div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 text-center mb-6">
  <div class="text-red-600">{{ error }}</div>
  <button @click="loadServiceStatusData" class="mt-2 px-4 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200">
    重新加载
  </button>
</div>
```

### 8. 添加数字格式化功能（可选）
为了更好地显示大数字，可以添加数字格式化功能：

```typescript
/**
 * 格式化数字显示
 * @param num 要格式化的数字
 */
const formatNumber = (num: number): string => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}
```

然后在模板中使用：
```vue
<h3 class="mt-1 text-2xl font-bold text-gray-900">{{ formatNumber(serviceStatusData.totalUsers) }}</h3>
```

### 9. 样式优化
确保在不同屏幕尺寸下都能良好显示，并保持与现有设计风格一致。

## 测试计划

### 组件测试
1. 验证组件能正确显示从API获取的数据
2. 验证加载状态的正确显示
3. 验证错误状态的正确显示
4. 验证重新加载功能
5. 验证自动刷新功能

### 端到端测试
1. 验证页面能正确加载并显示统计数据
2. 验证在不同数据量下的显示效果
3. 验证响应式布局在不同屏幕尺寸下的表现

## 部署注意事项
1. 确保所有导入路径正确
2. 验证API服务方法的可用性
3. 测试在不同网络条件下的表现
4. 确保错误处理机制正常工作