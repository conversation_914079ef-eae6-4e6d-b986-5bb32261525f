/**
 * API服务类型定义
 * 定义前后端交互的数据结构
 */

// =============================================================================
// 通用类型
// =============================================================================

export interface PaginationParams {
  page: number;
  pageSize: number;
}

export interface PaginationResponse<T> {
  items: T[];
  totalCount: number;
  currentPage: number;
  pageSize: number;
  totalPages: number;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  timestamp?: string;
}

// =============================================================================
// 认证相关类型
// =============================================================================

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  token: string;
  user: UserInfo;
  expiresIn: number;
}

export interface UserInfo {
  id: string;
  name: string;
  email: string;
  role: string;
  lastLoginAt?: string;
  permissions?: string[];
}

// =============================================================================
// 租户管理相关类型
// =============================================================================

export interface TenantInfo {
  tenantId: string;
  tenantName: string;
}

export interface AuthApplicationRequest {
  tenantName: string;
  tenantId: string;
  contactPerson: string;
  contactEmail: string;
  contactPhone: string;
  serviceType: string;
  authPeriod: string;
  permissionScope: string;
  description?: string;
  businessLicense?: string; // 文件ID
  organizationCode?: string; // 文件ID
}

export interface AuthApplicationResponse {
  id: string;
  status: string;
  appliedAt: string;
  message: string;
}

export interface AuthApplicationListParams extends PaginationParams {
  searchTerm?: string;
  serviceType?: string;
  authPeriod?: string;
  status?: string;
}

export interface AuthApplicationItem {
  id: string;
  tenantName: string;
  applyTime: string;
  serviceType: string;
  serviceTypeName: string;
  authPeriod: string;
  authPeriodName: string;
  contactPerson: string;
  contactPhone: string;
  contactEmail: string;
  status: 'pending' | 'approved' | 'rejected' | 'expired';
}

export interface AuthApplicationDetail {
  id: string;
  applicationId: string;
  status: string;
  applyTime: string;
  serviceInfo: {
    typeName: string;
    authPeriodName: string;
    permissionScopeName: string;
    description: string;
  };
  tenantInfo: {
    name: string;
    id: string;
    contactPerson: string;
    contactPhone: string;
    contactEmail: string;
  };
  documents: FileInfo[];
  approvalHistory: ApprovalHistoryItem[];
}

export interface ApprovalRequest {
  action: 'approve' | 'reject';
  comments: string;
}

export interface ApprovalResponse {
  id: string;
  status: string;
  approvedAt: string;
  approvalUser: {
    id: string;
    name: string;
  };
  message: string;
}

export interface ApprovalHistoryItem {
  id: string;
  action: string;
  operator: string;
  operatorRole: string;
  time: string;
  comment: string;
  status: string;
}

export interface AuthApplicationProgessItem {
  id: string;
  tenantId: string;
  tenantName: string;
  applyTime: string;
  serviceType: string;
  serviceTypeName: string;
  authPeriod: string;
  authPeriodName: string;
  status: string;
  statusName: string;
  progress: number;
  currentStep: string;
  steps: {Id: string; Name: string;time: string; Status: string;}[];
}

export interface AuthApplicationProgessDetail {
  id: string;
  tenantId: string;
  tenantName: string;
  applyTime: string;
  serviceType: string;
  serviceTypeName: string;
  authPeriod: string;
  authPeriodName: string;
  status: string;
  statusName: string;
  progress: number;
  currentStep: string;
  steps: {Id: string; Name: string;time: string; Status: string;}[];
}

export interface AuthApplicationDestroyItem {
    id: string;
    tenantName: string;
    serviceType: string;
    serviceTypeName: string;
    authStartTime: string;
    authEndTime: string;
    status: string;
    statusName: string;
    contactPerson: string;
    contactPhone: string;
    contactEmail: string;
}
// =============================================================================
// 运营统计相关类型
// =============================================================================

export interface DailyStatsParams {
  selectedTenant?: string;
  selectedServiceType?: string;
  selectedSize?: string;
  viewMode?: 'bar' | 'heatmap';
}

export interface DailyStatsItem {
  id: string;
  name: string;
  serviceType: string;
  serviceName: string;
  size: 'small' | 'medium' | 'large' | 'enterprise';
  dailyOperations: number;
  color: string;
}

export interface DailyStatsResponse {
  dailyStatsData: DailyStatsItem[];
  overview: {
    totalTenants: number;
    averageDailyOperations: number;
    maxDailyOperations: number;
    maxTenant: {
      name: string;
      serviceName: string;
    };
    totalOperations: number;
  };
}

/**
 * 日统计汇总数据传输对象
 * 用于daily-stats页面的统计功能，与后端DailyStatsDTO对应
 */
export interface DailyStatsSummary {
  /**
   * 租户总数：统计ms_auth_applications表的数据量
   */
  totalTenants: number;
  
  /**
   * 平均日操作次数：按天汇聚统计操作数，然后求平均值
   */
  averageDailyOperations: number;
  
  /**
   * 最大日操作数：按天汇聚统计，找出最大值
   */
  maxDailyOperations: number;
  
  /**
   * 操作总量：统计ms_operation_logs表当天的总数据量
   */
  totalOperations: number;
}

export interface ServiceStatusStats {
  totalUsers: number;
  activeUsers: number;
  todayVisits: number;
  pendingApplications: number;
}

export interface StatusCodeStats {
  statusCode: number;
  statusName: string;
  count: number;
  percentage: number;
}

export interface ServiceStatusStatsResponse {
  totalLogs: number;
  statusCodeStats: StatusCodeStats[];
  lastUpdated: string;
}

export interface MonthlyStatsParams {
  selectedTenant?: string;
  comparisonType?: string;
}

export interface MonthlyStatsResponse {
  overview: {
    totalOperations: number;
    monthlyAvg: number;
    maxMonthOperations: number;
    maxMonthName: string;
    averageGrowth: number;
  };
  monthlyData: {
    month: string;
    operations: number;
    prevYearOperations?: number;
    yoyGrowth?: number;
  }[];
}

export interface MonthlyUsageData {
  month: string;
  operations: number;
  activeTenants?: number;
}

export interface SixMonthUsageStatsResponse {
  monthlyData: MonthlyUsageData[];
  totalOperations: number;
  averageOperations: number;
  growthRate?: number;
}

export interface OperationLogParams extends PaginationParams {
  tenantName?: string;
  actionType?: string;
  userName?: string;
  operationDate?: string;
}

export interface OperationLogItem {
  id: string;
  time: string;
  tenant: string;
  user: string;
  type: 'create' | 'update' | 'delete' | 'query';
  object: string;
  detail: string;
}

export interface ActionTypeRankParams {
  selectedOperationType: string;
  tenantSize?: string;
  industry?: string;
  timeRange?: string;
}

export interface UserUsageParams {
  selectedTenant?: string;
  timeRange?: string;
  timeGranularity?: string;
}

// =============================================================================
// 文件管理相关类型
// =============================================================================

export interface FileUploadResponse {
  fileId: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  uploadTime: string;
  downloadUrl: string;
}

export interface FileInfo {
  id: string;
  name: string;
  typeName: string;
  size: string;
  uploadTime: string;
  url: string;
}

// =============================================================================
// 基础数据相关类型
// =============================================================================

export interface ServiceTypeOption {
  id: string;
  name: string;
  description: string;
}

export interface PermissionScopeOption {
  id: string;
  name: string;
  description: string;
  permissions: string;
}

export interface AuthPeriodOption {
  id: string;
  name: string;
  months: number;
}

export interface TenantSizeOption {
  id: string;
  name: string;
  description: string;
}

export interface IndustryOption {
  id: string;
  name: string;
  category: string;
}

// =============================================================================
// 告警设置相关类型
// =============================================================================

export interface AlarmSettingsResponse {
  emailTemplate: string;
  emailList: AlarmEmailItem[];
  isActive: boolean;
  lastUpdated: string;
}

export interface AlarmEmailItem {
  id: string;
  email: string;
  enabled: boolean;
  levels: string[];
  addedAt: string;
}

export interface AlarmTemplateUpdateRequest {
  emailTemplate: string;
}

export interface AlarmEmailRequest {
  email: string;
  levels: string[];
}

export interface AlarmEmailUpdateRequest {
  enabled: boolean;
  levels: string[];
}

// =============================================================================
// 短信告警相关类型
// =============================================================================

export interface SmsLevelOption {
  id: string;
  name: string;
  description: string;
}

export interface SmsTemplateVariable {
  key: string;
  description: string;
}

export interface SmsPhoneItem {
  id: string;
  phone: string;
  enabled: boolean;
  levels: string[];
  addedAt: string;
}

export interface AlarmSmsResponse {
  smsTemplate: string;
  phoneList: SmsPhoneItem[];
  isActive: boolean;
  lastUpdated: string;
}

export interface SmsTemplateUpdateRequest {
  smsTemplate: string;
}

export interface SmsPhoneRequest {
  phone: string;
  levels: string[];
}

export interface SmsPhoneUpdateRequest {
  status: boolean;
  levels: string[];
}

// =============================================================================
// 短信模板相关类型
// =============================================================================

export interface SmsTemplateSettingDto {
  tempId: number;
  templateName: string;
  createdAt: string;
  updatedAt: string;
}

export interface SaveSmsTemplateRequestDto {
  tempId?: number;
  templateName: string;
}

export interface SmsTemplateSettingResponse {
  success: boolean;
  message: string;
  data?: SmsTemplateSettingDto;
  errorCode?: string;
}

export interface SmsTemplateSettingListResponse {
  success: boolean;
  message: string;
  data: SmsTemplateSettingDto[];
  errorCode?: string;
}

// =============================================================================
// 错误相关类型
// =============================================================================

export interface ApiError {
  success: false;
  message: string;
  code?: string;
  details?: any;
  timestamp: string;
}