using SqlSugar;

namespace MapStudio.Api.Models.Entities
{
    /// <summary>
    /// 租户实体，对应数据库表 ms_tenants
    /// </summary>
    [SugarTable("ms_tenants")]
    public class Tenant
    {
        /// <summary>
        /// 租户ID (主键)
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, Length = 36)]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 租户名称
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = false)]
        public string TenantName { get; set; } = string.Empty;

        /// <summary>
        /// 租户标识符
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        public string TenantId { get; set; } = string.Empty;

        /// <summary>
        /// 联系人姓名
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        public string ContactPerson { get; set; } = string.Empty;

        /// <summary>
        /// 联系人邮箱
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = false)]
        public string ContactEmail { get; set; } = string.Empty;

        /// <summary>
        /// 联系人电话
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false)]
        public string ContactPhone { get; set; } = string.Empty;

        /// <summary>
        /// 租户状态 (Active, Inactive, Pending, Destroyed)
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false)]
        public string Status { get; set; } = "Pending";

        /// <summary>
        /// 地址
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string? Address { get; set; }

        /// <summary>
        /// 行业
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        public string? Industry { get; set; }

        /// <summary>
        /// 企业规模
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = true)]
        public string? Scale { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime UpdatedAt { get; set; }
    }
}