/* 导入变量 */
@import './variables.css';

/* 导入 Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 统一的组件样式类 */
@layer components {
  /* 按钮样式 */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply btn bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }

  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }

  .btn-outline {
    @apply btn border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500;
  }

  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  /* 输入框样式 */
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm;
  }

  .input-error {
    @apply border-red-300 focus:ring-red-500 focus:border-red-500;
  }

  /* 卡片样式 */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }

  /* 表格样式 */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }

  .table-header {
    @apply bg-gray-50;
  }

  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }

  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }

  .table-row {
    @apply hover:bg-gray-50;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }

  /* 标签样式 */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply badge bg-blue-100 text-blue-800;
  }

  .badge-success {
    @apply badge bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800;
  }

  .badge-danger {
    @apply badge bg-red-100 text-red-800;
  }

  .badge-gray {
    @apply badge bg-gray-100 text-gray-800;
  }

  /* 导航样式 */
  .nav-item {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200;
  }

  .nav-item-active {
    @apply nav-item text-blue-600 bg-blue-50;
  }

  .nav-item-inactive {
    @apply nav-item text-gray-600 hover:text-blue-600 hover:bg-blue-50;
  }

  /* 模态框样式 */
  .modal-overlay {
    @apply fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity z-40;
  }

  .modal-container {
    @apply fixed inset-0 z-50 overflow-y-auto;
  }

  .modal-content {
    @apply flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0;
  }

  .modal-panel {
    @apply relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg;
  }

  /* 下拉菜单样式 */
  .dropdown {
    @apply origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 py-1 z-10;
  }

  .dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-150;
  }

  /* 加载状态样式 */
  .loading-spinner {
    @apply animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600;
  }

  .loading-overlay {
    @apply absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center;
  }

  /* 分页样式 */
  .pagination {
    @apply flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6;
  }

  .pagination-info {
    @apply text-sm text-gray-700;
  }

  .pagination-nav {
    @apply flex flex-1 justify-between sm:hidden;
  }

  .pagination-desktop {
    @apply hidden sm:flex sm:flex-1 sm:items-center sm:justify-between;
  }

  /* 面包屑样式 */
  .breadcrumb {
    @apply flex items-center space-x-2 text-sm text-gray-500;
  }

  .breadcrumb-item {
    @apply hover:text-gray-700;
  }

  .breadcrumb-separator {
    @apply text-gray-400;
  }

  /* 警告框样式 */
  .alert {
    @apply p-4 rounded-md;
  }

  .alert-info {
    @apply alert bg-blue-50 border border-blue-200 text-blue-800;
  }

  .alert-success {
    @apply alert bg-green-50 border border-green-200 text-green-800;
  }

  .alert-warning {
    @apply alert bg-yellow-50 border border-yellow-200 text-yellow-800;
  }

  .alert-error {
    @apply alert bg-red-50 border border-red-200 text-red-800;
  }

  /* 图表容器样式 */
  .chart-container {
    @apply w-full h-64 sm:h-80 lg:h-96;
  }

  .chart-wrapper {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }

  .chart-title {
    @apply text-lg font-semibold text-gray-900 mb-4;
  }

  .chart-subtitle {
    @apply text-sm text-gray-600 mb-6;
  }
}

/* 统一的动画效果 */
@layer utilities {
  .fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .fade-out {
    animation: fadeOut 0.3s ease-in-out;
  }

  .slide-in-right {
    animation: slideInRight 0.3s ease-in-out;
  }

  .slide-out-right {
    animation: slideOutRight 0.3s ease-in-out;
  }

  .bounce-in {
    animation: bounceIn 0.5s ease-in-out;
  }

  .scale-in {
    animation: scaleIn 0.2s ease-in-out;
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slideInRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

@keyframes slideOutRight {
  from { transform: translateX(0); }
  to { transform: translateX(100%); }
}

@keyframes bounceIn {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* 响应式工具类 */
@layer utilities {
  .container-fluid {
    @apply w-full px-4 sm:px-6 lg:px-8;
  }

  .section-padding {
    @apply py-8 sm:py-12 lg:py-16;
  }

  .text-responsive {
    @apply text-sm sm:text-base lg:text-lg;
  }

  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6;
  }

  .flex-responsive {
    @apply flex flex-col sm:flex-row items-start sm:items-center gap-4;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }

  .print-break {
    page-break-after: always;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }

  .card {
    box-shadow: none;
    border: 1px solid #ddd;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .btn-primary {
    @apply bg-blue-800 border-2 border-blue-900;
  }

  .input {
    @apply border-2 border-gray-600;
  }

  .card {
    @apply border-2 border-gray-400;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}