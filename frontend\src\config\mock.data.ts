/**
 * Mock数据管理器
 * 提供模拟API响应数据，支持前端开发和测试
 */

import { API_ENDPOINTS } from './endpoints';

export interface MockResponse {
  data: any;
  status: number;
  headers?: Record<string, string>;
  delay?: number;
}

class MockDataManager {
  private mockData = new Map<string, MockResponse>();

  constructor() {
    this.initializeMockData();
  }

  /**
   * 初始化Mock数据
   */
  private initializeMockData() {
    this.setupAuthMockData();
    this.setupTenantMockData();
    this.setupOperationMockData();
    this.setupFileMockData();
    this.setupMetadataMockData();
  }

  /**
   * 认证Mock数据
   */
  private setupAuthMockData() {
    // 登录成功
    this.mockData.set('POST:/auth/login', {
      data: {
        success: true,
        token: 'mock_jwt_token_12345',
        user: {
          id: 'user-001',
          name: '系统管理员',
          email: '<EMAIL>',
          role: 'admin'
        },
        expiresIn: 3600
      },
      status: 200,
      delay: 800
    });

    // 获取用户信息
    this.mockData.set('GET:/auth/profile', {
      data: {
        id: 'user-001',
        name: '系统管理员',
        email: '<EMAIL>',
        role: 'admin',
        lastLoginAt: '2025-08-25T10:30:00Z',
        permissions: ['read', 'write', 'admin']
      },
      status: 200,
      delay: 300
    });
  }

  /**
   * 租户管理Mock数据
   */
  private setupTenantMockData() {
    // 提交授权申请
    this.mockData.set('POST:/Tenant/auth-applications', {
      data: {
        success: true,
        data: {
          id: 'TA-20250825',
          status: 'Pending',
          appliedAt: new Date().toISOString(),
          message: '租户授权申请提交成功！您的申请已提交至管理员审批，请耐心等待。'
        },
        message: '申请提交成功'
      },
      status: 201,
      delay: 1000
    });

    // 获取申请列表
    this.mockData.set('GET:/Tenant/auth-applications', {
      data: {
        success: true,
        data: {
          items: [
            {
              id: 'TA-20250825',
              tenantName: '智慧城市科技有限公司',
              applyTime: '2025-08-25 09:23:45',
              serviceType: 'enterprise',
              serviceTypeName: '企业级地图服务',
              authPeriod: '1year',
              authPeriodName: '1年',
              contactPerson: '张三',
              contactPhone: '13800138000',
              contactEmail: '<EMAIL>',
              status: 'pending'
            },
            {
              id: 'TA-20250824',
              tenantName: '绿色能源开发有限公司',
              applyTime: '2025-08-24 14:15:30',
              serviceType: 'advanced',
              serviceTypeName: '高级地图服务',
              authPeriod: '6months',
              authPeriodName: '6个月',
              contactPerson: '李四',
              contactPhone: '13900139000',
              contactEmail: '<EMAIL>',
              status: 'approved'
            },
            {
              id: 'TA-20250823',
              tenantName: '物流配送科技公司',
              applyTime: '2025-08-23 11:45:20',
              serviceType: 'basic',
              serviceTypeName: '基础地图服务',
              authPeriod: '3months',
              authPeriodName: '3个月',
              contactPerson: '王五',
              contactPhone: '13700137000',
              contactEmail: '<EMAIL>',
              status: 'rejected'
            }
          ],
          totalCount: 25,
          currentPage: 1,
          pageSize: 10,
          totalPages: 3
        },
        message: '获取申请列表成功'
      },
      status: 200,
      delay: 500
    });

    // 获取申请详情
    this.mockData.set('GET:/Tenant/auth-applications/TA-20250825', {
      data: {
        success: true,
        data: {
          applicationId: 'TA-20250825',
          status: 'pending',
          applyTime: '2025-08-25 09:23:45',
          serviceInfo: {
            typeName: '企业级地图服务',
            authPeriodName: '1年',
            permissionScopeName: '管理员权限',
            description: '申请企业级地图服务，用于智慧城市项目建设。需要完整的地图编辑、分析和管理功能。'
          },
          tenantInfo: {
            name: '智慧城市科技有限公司',
            id: 'SMART_CITY_TECH',
            contactPerson: '张三',
            contactPhone: '13800138000',
            contactEmail: '<EMAIL>'
          },
          documents: [
            {
              id: 'doc-001',
              name: '企业营业执照.pdf',
              typeName: '企业营业执照',
              size: '2.3 MB',
              uploadTime: '2025-08-25 09:15:30',
              url: '/api/files/download/doc-001'
            },
            {
              id: 'doc-002',
              name: '组织机构代码证.pdf',
              typeName: '组织机构代码证',
              size: '1.8 MB',
              uploadTime: '2025-08-25 09:16:45',
              url: '/api/files/download/doc-002'
            }
          ],
          approvalHistory: [
            {
              id: 'history-001',
              action: '申请提交',
              operator: '张三',
              operatorRole: '申请人',
              time: '2025-08-25 09:23:45',
              comment: '提交租户授权申请',
              status: 'submitted'
            }
          ]
        },
        message: '获取申请详情成功'
      },
      status: 200,
      delay: 400
    });

    // 审批申请
    this.mockData.set('PUT:/Tenant/auth-applications/TA-20250825/approve', {
      data: {
        success: true,
        data: {
          id: 'TA-20250825',
          status: 'Approved',
          approvedAt: new Date().toISOString(),
          approvalUser: {
            id: 'approver-001',
            name: '李审核员'
          },
          message: '申请单 TA-20250825 已成功审批通过。'
        },
        message: '审批完成'
      },
      status: 200,
      delay: 800
    });
  }

  /**
   * 运营统计Mock数据
   */
  private setupOperationMockData() {
    // 日度统计数据
    this.mockData.set('GET:/operation/daily-stats', {
      data: {
        success: true,
        data: {
          dailyStatsData: [
            {
              id: 'TENANT-2025001',
              name: '智慧城市科技有限公司',
              serviceType: 'enterprise',
              serviceName: '企业级地图服务',
              size: 'large',
              dailyOperations: 485,
              color: '#165DFF'
            },
            {
              id: 'TENANT-2025002',
              name: '绿色能源开发有限公司',
              serviceType: 'advanced',
              serviceName: '高级地图服务',
              size: 'medium',
              dailyOperations: 328,
              color: '#14C9C9'
            },
            {
              id: 'TENANT-2025003',
              name: '物流配送科技公司',
              serviceType: 'basic',
              serviceName: '基础地图服务',
              size: 'small',
              dailyOperations: 156,
              color: '#F7BA1E'
            },
            {
              id: 'TENANT-2025004',
              name: '数字农业科技有限公司',
              serviceType: 'advanced',
              serviceName: '高级地图服务',
              size: 'medium',
              dailyOperations: 372,
              color: '#722ED1'
            },
            {
              id: 'TENANT-2025005',
              name: '智慧医疗系统集成商',
              serviceType: 'enterprise',
              serviceName: '企业级地图服务',
              size: 'large',
              dailyOperations: 421,
              color: '#FF7D00'
            }
          ],
          overview: {
            totalTenants: 15,
            averageDailyOperations: 285,
            maxDailyOperations: 485,
            maxTenant: {
              name: '智慧城市科技有限公司',
              serviceName: '企业级地图服务'
            },
            totalOperations: 4275
          }
        },
        message: '获取日度统计成功'
      },
      status: 200,
      delay: 600
    });

    // 月度统计数据
    this.mockData.set('GET:/operation/monthly-stats', {
      data: {
        success: true,
        data: {
          monthlyData: [
            {
              month: '2025-01',
              operations: 12580,
              prevMonthOperations: 11240,
              prevYearOperations: 10320,
              momGrowth: 11.9,
              yoyGrowth: 21.9
            },
            {
              month: '2025-02',
              operations: 13240,
              prevMonthOperations: 12580,
              prevYearOperations: 10890,
              momGrowth: 5.2,
              yoyGrowth: 21.6
            },
            {
              month: '2025-03',
              operations: 14120,
              prevMonthOperations: 13240,
              prevYearOperations: 11430,
              momGrowth: 6.6,
              yoyGrowth: 23.5
            },
            {
              month: '2025-04',
              operations: 13890,
              prevMonthOperations: 14120,
              prevYearOperations: 11780,
              momGrowth: -1.6,
              yoyGrowth: 17.9
            },
            {
              month: '2025-05',
              operations: 15260,
              prevMonthOperations: 13890,
              prevYearOperations: 12340,
              momGrowth: 9.9,
              yoyGrowth: 23.7
            },
            {
              month: '2025-06',
              operations: 16180,
              prevMonthOperations: 15260,
              prevYearOperations: 13120,
              momGrowth: 6.0,
              yoyGrowth: 23.3
            },
            {
              month: '2025-07',
              operations: 15940,
              prevMonthOperations: 16180,
              prevYearOperations: 13580,
              momGrowth: -1.5,
              yoyGrowth: 17.4
            },
            {
              month: '2025-08',
              operations: 17320,
              prevMonthOperations: 15940,
              prevYearOperations: 14260,
              momGrowth: 8.7,
              yoyGrowth: 21.4
            }
          ],
          summary: {
            totalOperations: 118530,
            monthlyAverage: 14816,
            maxMonthOperations: 17320,
            maxMonthName: '8',
            averageGrowth: 12.3
          }
        },
        message: '获取月度统计成功'
      },
      status: 200,
      delay: 500
    });

    // 访问统计数据
    this.mockData.set('GET:/operation/access-stats', {
      data: {
        success: true,
        data: {
          overview: {
            totalVisits: 45680,
            totalOperations: 128456,
            avgResponseTime: 245
          },
          trendData: [
            { date: '2025-08-19', visits: 5680, operations: 12450, responseTime: 238 },
            { date: '2025-08-20', visits: 6120, operations: 13280, responseTime: 242 },
            { date: '2025-08-21', visits: 5890, operations: 12890, responseTime: 251 },
            { date: '2025-08-22', visits: 6380, operations: 14120, responseTime: 239 },
            { date: '2025-08-23', visits: 6950, operations: 15340, responseTime: 244 },
            { date: '2025-08-24', visits: 7140, operations: 16120, responseTime: 248 },
            { date: '2025-08-25', visits: 7520, operations: 17256, responseTime: 243 }
          ],
          serviceTypeDistribution: [
            { name: '基础地图服务', value: 35, color: '#165DFF' },
            { name: '高级地图服务', value: 28, color: '#14C9C9' },
            { name: '企业级地图服务', value: 25, color: '#722ED1' },
            { name: '定制化地图服务', value: 12, color: '#FF7D00' }
          ],
          deviceDistribution: [
            { name: '桌面端', value: 65, color: '#165DFF' },
            { name: '移动端', value: 28, color: '#14C9C9' },
            { name: '平板端', value: 7, color: '#722ED1' }
          ],
          regionDistribution: [
            { name: '华北', value: 32, color: '#165DFF' },
            { name: '华东', value: 28, color: '#14C9C9' },
            { name: '华南', value: 22, color: '#722ED1' },
            { name: '西南', value: 12, color: '#FF7D00' },
            { name: '其他', value: 6, color: '#F7BA1E' }
          ]
        },
        message: '获取访问统计成功'
      },
      status: 200,
      delay: 400
    });

    // 操作类型排行
    this.mockData.set('GET:/operation/action-type-rank', {
      data: {
        success: true,
        data: {
          rankings: [
            {
              operationType: 'query',
              operationName: '查询操作',
              count: 35680,
              percentage: 42.5,
              growth: 15.2
            },
            {
              operationType: 'create',
              operationName: '创建操作',
              count: 18240,
              percentage: 21.7,
              growth: 8.3
            },
            {
              operationType: 'update',
              operationName: '更新操作',
              count: 15890,
              percentage: 18.9,
              growth: 12.7
            },
            {
              operationType: 'delete',
              operationName: '删除操作',
              count: 8960,
              percentage: 10.7,
              growth: -3.2
            },
            {
              operationType: 'export',
              operationName: '导出操作',
              count: 5230,
              percentage: 6.2,
              growth: 24.8
            }
          ],
          totalOperations: 84000
        },
        message: '获取操作类型排行成功'
      },
      status: 200,
      delay: 350
    });

    // 租户操作排行
    this.mockData.set('GET:/operation/tenant-action-rank', {
      data: {
        success: true,
        data: {
          rankings: [
            {
              tenantId: 'TENANT-2025001',
              tenantName: '智慧城市科技有限公司',
              totalOperations: 15680,
              dailyAverage: 523,
              serviceType: 'enterprise',
              growth: 18.5
            },
            {
              tenantId: 'TENANT-2025005',
              tenantName: '智慧医疗系统集成商',
              totalOperations: 12640,
              dailyAverage: 421,
              serviceType: 'enterprise',
              growth: 12.3
            },
            {
              tenantId: 'TENANT-2025004',
              tenantName: '数字农业科技有限公司',
              totalOperations: 11160,
              dailyAverage: 372,
              serviceType: 'advanced',
              growth: 15.7
            },
            {
              tenantId: 'TENANT-2025002',
              tenantName: '绿色能源开发有限公司',
              totalOperations: 9840,
              dailyAverage: 328,
              serviceType: 'advanced',
              growth: 9.8
            },
            {
              tenantId: 'TENANT-2025003',
              tenantName: '物流配送科技公司',
              totalOperations: 4680,
              dailyAverage: 156,
              serviceType: 'basic',
              growth: 5.2
            }
          ]
        },
        message: '获取租户操作排行成功'
      },
      status: 200,
      delay: 380
    });

    // 用户使用情况
    this.mockData.set('GET:/operation/user-usage', {
      data: {
        success: true,
        data: {
          userStats: [
            {
              tenantName: '智慧城市科技有限公司',
              activeUsers: 156,
              totalUsers: 200,
              usageRate: 78.0,
              avgSessionTime: 45,
              topOperations: ['地图查询', '图层编辑', '数据导出']
            },
            {
              tenantName: '智慧医疗系统集成商',
              activeUsers: 123,
              totalUsers: 150,
              usageRate: 82.0,
              avgSessionTime: 52,
              topOperations: ['位置查询', '路径规划', '数据分析']
            },
            {
              tenantName: '数字农业科技有限公司',
              activeUsers: 98,
              totalUsers: 120,
              usageRate: 81.7,
              avgSessionTime: 38,
              topOperations: ['区域分析', '监测数据', '报表生成']
            }
          ],
          summary: {
            totalActiveUsers: 456,
            totalUsers: 580,
            overallUsageRate: 78.6,
            avgDailyActiveUsers: 342
          }
        },
        message: '获取用户使用情况成功'
      },
      status: 200,
      delay: 420
    });

    // 操作日志
    this.mockData.set('GET:/operation/logs', {
      data: {
        success: true,
        data: {
          items: [
            {
              id: 'LOG001',
              time: '2025-08-25 14:30:25',
              tenant: '智慧城市科技',
              user: '张三',
              type: 'create',
              object: '地图图层',
              detail: '创建了新的地图图层: 北京交通图层'
            },
            {
              id: 'LOG002',
              time: '2025-08-25 14:28:15',
              tenant: '绿色能源开发',
              user: '李四',
              type: 'update',
              object: '数据源',
              detail: '更新了风力发电站数据源配置'
            },
            {
              id: 'LOG003',
              time: '2025-08-25 14:25:08',
              tenant: '物流配送科技',
              user: '王五',
              type: 'query',
              object: '配送路线',
              detail: '查询了北京市配送路线优化方案'
            },
            {
              id: 'LOG004',
              time: '2025-08-25 14:20:33',
              tenant: '数字农业科技',
              user: '赵六',
              type: 'export',
              object: '农田数据',
              detail: '导出了华北地区农田监测数据'
            },
            {
              id: 'LOG005',
              time: '2025-08-25 14:15:42',
              tenant: '智慧医疗系统',
              user: '孙七',
              type: 'delete',
              object: '过期配置',
              detail: '删除了过期的医院位置配置'
            }
          ],
          totalCount: 128,
          currentPage: 1,
          pageSize: 20,
          totalPages: 7
        },
        message: '获取操作日志成功'
      },
      status: 200,
      delay: 400
    });

    // 告警设置
    this.mockData.set('GET:/operation/alarm-settings', {
        data: {
            success: true,
            data: {
                emailTemplate: '尊敬的管理员，\n\n系统检测到以下告警信息：\n告警级别：{level}\n告警时间：{time}\n告警内容：{content}\n\n请及时处理。\n\n系统自动发送，请勿回复。',
                emailList: [
                    {
                        id: 'email-001',
                        email: '<EMAIL>',
                        enabled: true,
                        levels: ['high', 'medium'],
                        createdAt: '2025-08-20T10:30:00Z'
                    },
                    {
                        id: 'email-002',
                        email: '<EMAIL>',
                        enabled: true,
                        levels: ['high', 'medium', 'low'],
                        createdAt: '2025-08-22T14:20:00Z'
                    },
                    {
                        id: 'email-003',
                        email: '<EMAIL>',
                        enabled: false,
                        levels: ['high'],
                        createdAt: '2025-08-24T09:15:00Z'
                    }
                ]
            },
            message: '获取告警设置成功'
        },
        status: 200,
        delay: 300
    });

    // 更新告警邮件模板
    this.mockData.set('PUT:/operation/alarm-settings/template', {
      data: {
        success: true,
        data: {
          message: '邮件模板更新成功'
        },
        message: '邮件模板更新成功'
      },
      status: 200,
      delay: 500
    });

    // 添加告警邮箱
    this.mockData.set('POST:/operation/alarm-settings/emails', {
      data: {
        success: true,
        data: {
          id: 'email-' + Date.now(),
          message: '告警邮箱添加成功'
        },
        message: '告警邮箱添加成功'
      },
      status: 201,
      delay: 600
    });

    // 服务状态统计数据
    this.mockData.set('GET:/operation/service-status-stats', {
      data: {
        totalUsers: 1250,
        activeUsers: 856,
        todayVisits: 3420,
        pendingApplications: 12
      },
      status: 200,
      delay: 500
    });

    // 服务状态码统计数据
    this.mockData.set('GET:/operation/service-status-code-stats', {
      data: {
        success: true,
        data: {
          statusCodeStats: [
            {
              statusCode: 200,
              statusName: "正常运行",
              count: 450,
              percentage: 50.0
            },
            {
              statusCode: 404,
              statusName: "资源不存在",
              count: 120,
              percentage: 13.3
            },
            {
              statusCode: 405,
              statusName: "跨域错误",
              count: 80,
              percentage: 8.9
            },
            {
              statusCode: 400,
              statusName: "参数错误",
              count: 60,
              percentage: 6.7
            },
            {
              statusCode: 500,
              statusName: "未知异常",
              count: 90,
              percentage: 10.0
            }
          ],
          totalLogs: 900,
          lastUpdated: new Date().toISOString()
        },
        message: '获取服务状态码统计成功'
      },
      status: 200,
      delay: 500
    });

    // 近6个月使用率统计数据
    this.mockData.set('GET:/operation/six-month-usage-stats', {
      data: {
        success: true,
        data: {
          monthlyData: [
            {
              month: '2025-04',
              operations: 1200,
              activeTenants: 15
            },
            {
              month: '2025-05',
              operations: 1500,
              activeTenants: 18
            },
            {
              month: '2025-06',
              operations: 1800,
              activeTenants: 20
            },
            {
              month: '2025-07',
              operations: 2100,
              activeTenants: 22
            },
            {
              month: '2025-08',
              operations: 2400,
              activeTenants: 25
            },
            {
              month: '2025-09',
              operations: 2700,
              activeTenants: 28
            }
          ],
          totalOperations: 11700,
          averageOperations: 1950,
          growthRate: 12.5
        },
        message: '获取近6个月使用率统计成功'
      },
      status: 200,
      delay: 500
    });
  }

  /**
   * 文件管理Mock数据
   */
  private setupFileMockData() {
    // 文件上传
    this.mockData.set('POST:/files/upload', {
      data: {
        fileId: 'file-' + Date.now(),
        fileName: '企业营业执照.pdf',
        fileSize: 2457600,
        fileType: 'businessLicense',
        uploadTime: new Date().toISOString(),
        downloadUrl: '/api/files/download/file-' + Date.now()
      },
      status: 201,
      delay: 2000 // 模拟文件上传延时
    });
  }

  /**
   * 基础数据Mock数据
   */
  private setupMetadataMockData() {
    // 服务类型
    this.mockData.set('GET:/metadata/service-types', {
      data: [
        {
          id: 'basic',
          name: '基础地图服务',
          description: '提供基础地图展示、查询功能'
        },
        {
          id: 'advanced',
          name: '高级地图服务',
          description: '提供高级分析、编辑功能'
        },
        {
          id: 'enterprise',
          name: '企业级地图服务',
          description: '提供企业级功能和优先支持'
        },
        {
          id: 'custom',
          name: '定制化地图服务',
          description: '提供定制化开发和集成服务'
        }
      ],
      status: 200,
      delay: 200
    });
  }

  /**
   * 获取Mock响应数据
   * @param url API路径
   * @param method HTTP方法
   * @param data 请求数据（可选）
   */
  getMockResponse(url: string, method: string, data?: any): MockResponse {
    const key = `${method.toUpperCase()}:${url}`;
    const mockResponse = this.mockData.get(key);

    if (mockResponse) {
      console.log(`🎭 返回Mock数据: ${method} ${url}`);
      return mockResponse;
    }

    // 如果没有找到对应的Mock数据，返回默认响应
    console.warn(`⚠️ 未找到Mock数据: ${method} ${url}`);
    return {
      data: {
        error: '未找到Mock数据',
        message: `No mock data found for ${method} ${url}`,
        timestamp: new Date().toISOString()
      },
      status: 404,
      delay: 300
    };
  }

  /**
   * 添加或更新Mock数据
   * @param method HTTP方法
   * @param url API路径
   * @param response Mock响应
   */
  setMockData(method: string, url: string, response: MockResponse) {
    const key = `${method.toUpperCase()}:${url}`;
    this.mockData.set(key, response);
    console.log(`📝 已设置Mock数据: ${key}`);
  }

  /**
   * 清除所有Mock数据
   */
  clearAllMockData() {
    this.mockData.clear();
    console.log('🧹 已清除所有Mock数据');
  }

  /**
   * 获取所有Mock数据键
   */
  getAllMockKeys(): string[] {
    return Array.from(this.mockData.keys());
  }
}

// 导出单例实例
export const mockDataManager = new MockDataManager();
