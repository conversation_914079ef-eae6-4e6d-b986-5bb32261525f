/**
 * API配置管理
 * 支持开发/生产环境配置，以及Mock数据与真实API的一键切换
 */

export interface ApiConfig {
  baseURL: string;
  timeout: number;
  useMock: boolean;
  retries: number;
}

// 开发环境配置
const development: ApiConfig = {
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:5172/api',
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000,
  useMock: import.meta.env.VITE_ENABLE_MOCK === 'true' || false, // 设为true使用mock数据，false使用真实API
  retries: 3
};

// 生产环境配置
const production: ApiConfig = {
  baseURL: import.meta.env.VITE_API_BASE_URL || 'https://api.mapstudio.com/api',
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 15000,
  useMock: import.meta.env.VITE_ENABLE_MOCK === 'true' || false,
  retries: 3
};

// 测试环境配置
const testing: ApiConfig = {
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:5172/api',
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000,
  useMock: import.meta.env.VITE_ENABLE_MOCK === 'true' || true, // 测试环境默认使用Mock数据
  retries: 1
};

// 根据环境变量选择配置
const getEnvironmentConfig = (): ApiConfig => {
  const mode = import.meta.env.MODE;

  switch (mode) {
    case 'production':
      return production;
    case 'test':
      return testing;
    default:
      return development;
  }
};

// 导出当前配置
export const apiConfig: ApiConfig = getEnvironmentConfig();

// 从本地存储恢复Mock模式设置
const savedMockMode = localStorage.getItem('api_mock_mode');
if (savedMockMode !== null) {
  (apiConfig as any).useMock = savedMockMode === 'true';
}

/**
 * 一键切换Mock模式
 * @param useMock true=使用Mock数据，false=使用真实API
 */
export const toggleMockMode = (useMock: boolean) => {
  (apiConfig as any).useMock = useMock;
  localStorage.setItem('api_mock_mode', useMock.toString());

  console.log(`🔄 API模式已切换为: ${useMock ? 'Mock数据' : '真实API'}`);
  console.log(`📡 当前API地址: ${apiConfig.baseURL}`);

  // 通知用户重新加载页面以应用更改
  if (confirm('API模式已切换，是否重新加载页面以应用更改？')) {
    window.location.reload();
  }
};

/**
 * 获取当前Mock模式状态
 */
export const getCurrentMockMode = (): boolean => {
  return apiConfig.useMock;
};

/**
 * 检查API连接状态
 */
export const checkApiConnection = async (): Promise<boolean> => {
  if (apiConfig.useMock) {
    return true; // Mock模式总是可用
  }

  try {
    // 使用AbortController实现超时控制（遵循API超时控制规范）
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    const response = await fetch(`${apiConfig.baseURL}/health`, {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Version': '1.0.0'
      }
    });

    clearTimeout(timeoutId);
    return response.ok;
  } catch (error) {
    console.error('API连接检查失败:', error);
    return false;
  }
};

/**
 * 环境信息
 */
export const getEnvironmentInfo = () => {
  return {
    mode: import.meta.env.MODE,
    baseURL: apiConfig.baseURL,
    useMock: apiConfig.useMock,
    timeout: apiConfig.timeout,
    retries: apiConfig.retries,
    isDevelopment: import.meta.env.DEV,
    isProduction: import.meta.env.PROD
  };
};
