@echo off
REM Map Studio 后端测试环境启动脚本 (Windows版本)
REM 用于启动开发和测试环境的后端服务

setlocal enabledelayedexpansion

echo 🚀 启动 Map Studio 后端测试环境...

REM 检查参数
set ENVIRONMENT=%1
if "%ENVIRONMENT%"=="" set ENVIRONMENT=Testing

set PORT=%2
if "%PORT%"=="" set PORT=5000

echo ℹ️  环境: %ENVIRONMENT%
echo ℹ️  端口: %PORT%

REM 检查 .NET 是否安装
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ❌ .NET SDK 未安装或未在PATH中
    exit /b 1
)

for /f "tokens=*" %%i in ('dotnet --version') do set DOTNET_VERSION=%%i
echo ✅ .NET SDK 已安装: %DOTNET_VERSION%

REM 检查项目文件
if not exist "MapStudio.Api.csproj" (
    echo ❌ MapStudio.Api.csproj 文件未找到
    echo ℹ️  请确保在后端项目根目录运行此脚本
    exit /b 1
)

REM 检查数据库连接
echo ℹ️  检查数据库连接...

REM 检查MySQL是否运行
mysql -h 192.168.2.119 -P 3307 -u dtauser -pdtauser -e "SELECT 1" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  MySQL 数据库连接失败，请确保MySQL服务已启动
    echo ℹ️  或使用Docker启动MySQL:
    echo ℹ️  docker run --name mapstudio-mysql -e MYSQL_ROOT_PASSWORD=test_password -p 3306:3306 -d mysql:8.0
) else (
    echo ✅ MySQL 数据库连接正常
)

REM 创建必要的目录
echo ℹ️  创建必要的目录...

if not exist "logs" mkdir logs
if not exist "test_uploads" mkdir test_uploads
if not exist "temp" mkdir temp

echo ✅ 目录创建完成

REM 恢复NuGet包
echo ℹ️  恢复NuGet包...

dotnet restore
if errorlevel 1 (
    echo ❌ NuGet包恢复失败
    exit /b 1
)

echo ✅ NuGet包恢复完成

REM 构建项目
echo ℹ️  构建项目...

dotnet build --configuration Debug
if errorlevel 1 (
    echo ❌ 项目构建失败
    exit /b 1
)

echo ✅ 项目构建完成

REM 设置环境变量
set ASPNETCORE_ENVIRONMENT=%ENVIRONMENT%
set ASPNETCORE_URLS=http://localhost:%PORT%

REM 启动应用
echo ℹ️  启动应用...
echo ℹ️  访问地址: http://localhost:%PORT%
echo ℹ️  API文档: http://localhost:%PORT%/swagger
echo ℹ️  按 Ctrl+C 停止服务

dotnet run --environment %ENVIRONMENT% --urls "http://localhost:%PORT%"

echo ℹ️  服务已停止

pause