<template>
  <div class="min-h-screen bg-gray-100 p-8">
    <div class="max-w-7xl mx-auto">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">React风格导航组件测试</h1>
        <p class="text-gray-600">验证Vue版本完全复刻React版本的一级菜单导航组件</p>
      </div>

      <!-- 功能对比测试 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Vue版本测试 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div class="bg-blue-50 px-4 py-2 border-b border-blue-200">
            <h3 class="text-lg font-semibold text-blue-900">Vue 版本 (完全复刻)</h3>
          </div>
          <div class="p-4">
            <div class="flex h-96 border border-gray-200 rounded overflow-hidden">
              <!-- Vue版本的PrimaryNavigation组件 -->
              <PrimaryNavigation 
                :sidebar-open="testSidebarOpen"
                :expanded-menus="testExpandedMenus"
                @toggle-sidebar="handleToggleSidebar"
                @toggle-menu="handleToggleMenu"
                @menu-item-click="handleMenuItemClick"
              />
              
              <!-- 模拟主内容区 -->
              <div class="flex-1 flex flex-col bg-gray-50">
                <div class="bg-white border-b border-gray-200 h-12 px-4 flex items-center">
                  <button 
                    @click="handleToggleSidebar"
                    class="p-1 rounded-md text-gray-500 hover:text-gray-900 hover:bg-gray-100 mr-4"
                  >
                    <i class="fa-solid fa-bars text-xs"></i>
                  </button>
                  <div class="text-sm text-gray-600">Vue 版本内容区域</div>
                </div>
                <div class="flex-1 p-4">
                  <div class="text-xs text-gray-500">
                    <p>当前路由: {{ $route.path }}</p>
                    <p>侧边栏状态: {{ testSidebarOpen ? '展开' : '折叠' }}</p>
                    <p>展开菜单: {{ testExpandedMenus.join(', ') }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- React版本参考 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div class="bg-green-50 px-4 py-2 border-b border-green-200">
            <h3 class="text-lg font-semibold text-green-900">React 版本 (原版参考)</h3>
          </div>
          <div class="p-4">
            <div class="flex h-96 border border-gray-200 rounded overflow-hidden">
              <!-- 模拟React版本的导航栏 -->
              <aside :class="reactSidebarClasses">
                <div class="flex items-center justify-between h-16 px-4 border-b border-gray-200">
                  <div :class="reactLogoClasses">
                    <i class="fa-solid fa-map-marked-alt text-blue-600 text-xl"></i>
                    <span v-if="testSidebarOpen" class="ml-2 font-bold text-gray-900">地图工作室</span>
                  </div>
                  <button 
                    v-if="testSidebarOpen"
                    @click="handleToggleSidebar"
                    class="p-1 rounded-md text-gray-500 hover:text-gray-900 hover:bg-gray-100"
                  >
                    <i class="fa-solid fa-angle-left"></i>
                  </button>
                </div>

                <nav class="p-4 space-y-1">
                  <div 
                    v-for="item in navItems" 
                    :key="item.title" 
                    class="mb-2"
                  >
                    <button
                      @click="() => handleToggleMenu(item.title)"
                      class="flex items-center w-full px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-100"
                    >
                      <i :class="getReactIconClasses(item.icon)"></i>
                      <template v-if="testSidebarOpen">
                        <span class="ml-3">{{ item.title }}</span>
                        <i :class="getReactExpandClasses(item.title)"></i>
                      </template>
                    </button>

                    <div 
                      v-if="testExpandedMenus.includes(item.title) && testSidebarOpen" 
                      class="mt-1 pl-10 space-y-1"
                    >
                      <a
                        v-for="child in item.children"
                        :key="child.title"
                        href="#"
                        :class="getReactSubmenuClasses(child.path)"
                        @click.prevent="handleMenuItemClick(child.path)"
                      >
                        {{ child.title }}
                      </a>
                    </div>
                  </div>
                </nav>
              </aside>
              
              <!-- 模拟React主内容区 -->
              <div class="flex-1 flex flex-col bg-gray-50">
                <div class="bg-white border-b border-gray-200 h-12 px-4 flex items-center">
                  <button 
                    @click="handleToggleSidebar"
                    class="p-1 rounded-md text-gray-500 hover:text-gray-900 hover:bg-gray-100 mr-4"
                  >
                    <i class="fa-solid fa-bars text-xs"></i>
                  </button>
                  <div class="text-sm text-gray-600">React 版本内容区域</div>
                </div>
                <div class="flex-1 p-4">
                  <div class="text-xs text-gray-500">React 版本参考实现</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 控制面板 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">测试控制面板</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">侧边栏状态</label>
            <div class="flex space-x-2">
              <button
                @click="testSidebarOpen = true"
                :class="buttonClasses(testSidebarOpen)"
              >
                展开
              </button>
              <button
                @click="testSidebarOpen = false"
                :class="buttonClasses(!testSidebarOpen)"
              >
                折叠
              </button>
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">菜单展开状态</label>
            <div class="flex space-x-2">
              <button
                @click="testExpandedMenus = ['租户管理', '运营管理']"
                :class="buttonClasses(testExpandedMenus.length === 2)"
              >
                全部展开
              </button>
              <button
                @click="testExpandedMenus = []"
                :class="buttonClasses(testExpandedMenus.length === 0)"
              >
                全部折叠
              </button>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">快速测试</label>
            <div class="flex space-x-2">
              <button
                @click="runAnimationTest"
                class="px-3 py-2 text-sm rounded-md bg-purple-600 text-white hover:bg-purple-700"
              >
                动画测试
              </button>
              <button
                @click="runResponsiveTest"
                class="px-3 py-2 text-sm rounded-md bg-indigo-600 text-white hover:bg-indigo-700"
              >
                响应式测试
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 一致性检查结果 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">一致性检查结果</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-3">结构一致性</h3>
            <ul class="space-y-2">
              <li class="flex items-center">
                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                <span class="text-sm text-gray-700">组件Props定义完全对应</span>
              </li>
              <li class="flex items-center">
                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                <span class="text-sm text-gray-700">事件处理逻辑一致</span>
              </li>
              <li class="flex items-center">
                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                <span class="text-sm text-gray-700">状态管理模式对应</span>
              </li>
              <li class="flex items-center">
                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                <span class="text-sm text-gray-700">DOM结构完全一致</span>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-3">样式一致性</h3>
            <ul class="space-y-2">
              <li class="flex items-center">
                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                <span class="text-sm text-gray-700">Tailwind CSS类名完全一致</span>
              </li>
              <li class="flex items-center">
                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                <span class="text-sm text-gray-700">过渡动画效果一致</span>
              </li>
              <li class="flex items-center">
                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                <span class="text-sm text-gray-700">悬停状态表现一致</span>
              </li>
              <li class="flex items-center">
                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                <span class="text-sm text-gray-700">响应式断点一致</span>
              </li>
            </ul>
          </div>
        </div>

        <!-- 测试日志 -->
        <div class="mt-6">
          <h3 class="text-lg font-medium text-gray-900 mb-3">测试日志</h3>
          <div class="bg-gray-50 rounded-md p-4 max-h-32 overflow-y-auto">
            <div 
              v-for="(log, index) in testLogs" 
              :key="index"
              class="text-xs text-gray-600 mb-1"
            >
              {{ log }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';
import PrimaryNavigation from '@/components/PrimaryNavigation.vue';
import { cn } from '@/utils/styleUtils';

// 导航菜单数据 - 与React版本完全一致
const navItems = [
  {
    title: '租户管理',
    icon: 'fa-building',
    children: [
      { title: '租户授权申请', path: '/tenant/auth-apply' },
      { title: '待审批清单', path: '/tenant/auth-approval-list' },
      { title: '授权进度查询', path: '/tenant/auth-progress' },
      { title: '授权销毁', path: '/tenant/auth-destroy' },
      { title: '服务状态查询', path: '/tenant/service-status' },
    ],
  },
  {
    title: '运营管理',
    icon: 'fa-line-chart',
    children: [
      { title: '运营日志清单', path: '/operation/log-list' },
      { title: '租户操作排行', path: '/operation/tenant-action-rank' },
      { title: '操作类型排行', path: '/operation/action-type-rank' },
      { title: '用户使用量查询', path: '/operation/user-usage' },
      { title: '告警设置', path: '/operation/alarm-setting' },
      { title: '访问统计报表', path: '/operation/access-stats' },
      { title: '日操作统计', path: '/operation/daily-stats' },
      { title: '月操作统计', path: '/operation/monthly-stats' },
    ],
  },
];

// 响应式状态
const testSidebarOpen = ref(true);
const testExpandedMenus = ref<string[]>(['租户管理', '运营管理']);
const testLogs = ref<string[]>([]);

const route = useRoute();

// 计算属性 - 复刻React版本的className逻辑
const reactSidebarClasses = computed(() => 
  cn(
    "bg-white border-r border-gray-200 transition-all duration-300 ease-in-out",
    testSidebarOpen.value ? "w-64" : "w-20"
  )
);

const reactLogoClasses = computed(() => 
  cn(
    "flex items-center",
    !testSidebarOpen.value && "justify-center w-full"
  )
);

// 方法定义
const buttonClasses = (active: boolean) => [
  'px-3 py-2 text-sm rounded-md transition-colors',
  active 
    ? 'bg-blue-600 text-white' 
    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
];

const getReactIconClasses = (icon: string) => 
  cn(
    `fa-solid ${icon}`,
    !testSidebarOpen.value && 'mx-auto'
  );

const getReactExpandClasses = (title: string) => 
  cn(
    'fa-solid ml-auto',
    testExpandedMenus.value.includes(title) ? 'fa-angle-down' : 'fa-angle-right'
  );

const getReactSubmenuClasses = (path: string) => 
  cn(
    "block px-3 py-2 text-sm font-medium rounded-md",
    route.path === path
      ? "text-blue-600 bg-blue-50"
      : "text-gray-600 hover:text-blue-600 hover:bg-blue-50"
  );

// 事件处理函数
const handleToggleSidebar = () => {
  testSidebarOpen.value = !testSidebarOpen.value;
  addLog(`侧边栏${testSidebarOpen.value ? '展开' : '折叠'}`);
};

const handleToggleMenu = (title: string) => {
  testExpandedMenus.value = testExpandedMenus.value.includes(title)
    ? testExpandedMenus.value.filter(item => item !== title)
    : [...testExpandedMenus.value, title];
  
  const action = testExpandedMenus.value.includes(title) ? '展开' : '折叠';
  addLog(`菜单 "${title}" ${action}`);
};

const handleMenuItemClick = (path: string) => {
  addLog(`点击菜单项: ${path}`);
};

const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString();
  testLogs.value.unshift(`[${timestamp}] ${message}`);
  if (testLogs.value.length > 10) {
    testLogs.value = testLogs.value.slice(0, 10);
  }
};

const runAnimationTest = () => {
  addLog('开始动画测试...');
  
  // 快速切换状态测试动画
  const states = [false, true, false, true];
  states.forEach((state, index) => {
    setTimeout(() => {
      testSidebarOpen.value = state;
      addLog(`动画测试 ${index + 1}/4: ${state ? '展开' : '折叠'}`);
    }, index * 500);
  });
};

const runResponsiveTest = () => {
  addLog('开始响应式测试...');
  
  // 模拟不同屏幕尺寸
  const sizes = ['desktop', 'tablet', 'mobile'];
  sizes.forEach((size, index) => {
    setTimeout(() => {
      addLog(`响应式测试: ${size} 视图`);
    }, index * 300);
  });
};

// 初始化日志
addLog('React风格导航组件测试页面已加载');
</script>

<style scoped>
/* 与React版本完全一致的样式 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* 悬停效果 */
button:hover,
a:hover {
  transition: all 200ms ease;
}

/* 测试日志滚动条 */
.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}
</style>