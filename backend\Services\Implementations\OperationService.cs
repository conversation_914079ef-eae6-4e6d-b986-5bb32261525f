using SqlSugar;
using MapStudio.Api.Services.Interfaces;
using MapStudio.Api.Models.DTOs;
using MapStudio.Api.Models.Entities;

namespace MapStudio.Api.Services.Implementations;

public class OperationService : IOperationService
{
    private readonly ISqlSugarClient _db;
    private readonly ILogger<OperationService> _logger;

    public OperationService(ISqlSugarClient db, ILogger<OperationService> logger)
    {
        _db = db;
        _logger = logger;
    }

    public async Task<DailyStatsResponse> GetDailyStatsAsync(string? selectedTenant, string? selectedServiceType, string? selectedSize, string? viewMode)
    {
        try
        {
            // 模拟数据 - 实际实现中应从真实数据计算
            var dailyStatsData = new List<DailyStatsDto>
            {
                new DailyStatsDto
                {
                    Id = "TENANT-2025001",
                    Name = "智慧城市科技有限公司",
                    ServiceType = "enterprise",
                    ServiceName = "企业级地图服务",
                    Size = "medium",
                    DailyOperations = 485,
                    Color = "#165DFF"
                }
            };

            var overview = new DailyStatsOverviewDto
            {
                TotalTenants = 15,
                AverageDailyOperations = 285,
                MaxDailyOperations = 485,
                MaxTenant = new TenantSummaryDto
                {
                    Name = "智慧城市科技有限公司",
                    ServiceName = "企业级地图服务"
                },
                TotalOperations = 4275
            };

            return new DailyStatsResponse
            {
                DailyStatsData = dailyStatsData,
                Overview = overview
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取日统计失败");
            throw;
        }
    }

    public async Task<MonthlyStatsResponse> GetMonthlyStatsAsync(string? selectedTenant, string? comparisonType)
    {
        // 简化实现 - 返回模拟数据
        return new MonthlyStatsResponse
        {
            Overview = new MonthlyStatsOverviewDto
            {
                TotalOperations = 1850000,
                MonthlyAvg = 154167,
                MaxMonthOperations = 185000,
                MaxMonthName = "11",
                AverageGrowth = 12.8
            },
            MonthlyData = new List<MonthlyDataDto>()
        };
    }

    public async Task<AccessStatsResponse> GetAccessStatsAsync(string? timeRange, string? chartType)
    {
        // 简化实现
        return new AccessStatsResponse
        {
            AccessData = new List<AccessStatsDto>(),
            Overview = new AccessStatsOverviewDto()
        };
    }

    public async Task<ActionTypeRankResponse> GetActionTypeRankAsync(string selectedOperationType, string? tenantSize, string? industry, string? timeRange)
    {
        // 简化实现
        return new ActionTypeRankResponse
        {
            RankingData = new List<ActionTypeRankDto>(),
            Overview = new ActionTypeOverviewDto()
        };
    }

    public async Task<TenantActionRankResponse> GetTenantActionRankAsync()
    {
        // 简化实现
        return new TenantActionRankResponse
        {
            RankingData = new List<TenantActionRankDto>()
        };
    }

    public async Task<UserUsageResponse> GetUserUsageAsync(string? selectedTenant, string? timeRange, string? timeGranularity)
    {
        // 简化实现
        return new UserUsageResponse
        {
            UsageData = new List<UserUsageDto>(),
            Overview = new UserUsageOverviewDto()
        };
    }

    public async Task<OperationLogListResponse> GetOperationLogsAsync(int page, int pageSize, string? tenantName, string? actionType, string? userName, string? operationDate)
    {
        try
        {
            var query = _db.Queryable<OperationLog>();

            if (!string.IsNullOrEmpty(tenantName))
            {
                query = query.Where(log => log.TenantId.Contains(tenantName));
            }

            if (!string.IsNullOrEmpty(actionType))
            {
                query = query.Where(log => log.ActionType.Contains(actionType));
            }

            var totalCount = await query.CountAsync();
            var logs = await query
                .OrderBy(log => log.OperatedAt, OrderByType.Desc)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var items = logs.Select(log => new OperationLogDto
            {
                Id = log.Id,
                Time = log.OperatedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                Tenant = log.TenantId,
                User = log.UserId,
                Type = log.ActionType,
                Object = log.ActionObject,
                Detail = log.Description
            }).ToList();

            return new OperationLogListResponse
            {
                Items = items,
                TotalCount = totalCount,
                CurrentPage = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取操作日志失败");
            throw;
        }
    }

    public async Task<AlarmSettingsResponse> GetAlarmSettingsAsync()
    {
        try
        {
            var setting = await _db.Queryable<AlarmSetting>()
                .OrderBy(s => s.UpdatedAt, OrderByType.Desc)
                .FirstAsync();

            if (setting == null)
            {
                setting = new AlarmSetting
                {
                    Id = Guid.NewGuid().ToString(),
                    EmailTemplate = "Map Studio系统告警通知：{{AlarmMessage}}",
                    EmailList = "[]",
                    IsActive = true,
                    UpdatedAt = DateTime.Now
                };
            }

            return new AlarmSettingsResponse
            {
                Id = setting.Id,
                EmailTemplate = setting.EmailTemplate,
                EmailList = new List<AlarmEmailDto>(),
                IsActive = setting.IsActive,
                UpdatedAt = setting.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取告警设置失败");
            throw;
        }
    }

    public async Task<AlarmTemplateUpdateResponse> UpdateAlarmTemplateAsync(string emailTemplate)
    {
        // 简化实现
        return new AlarmTemplateUpdateResponse
        {
            Success = true,
            Message = "模板更新成功",
            UpdatedAt = DateTime.Now
        };
    }

    public async Task<AlarmEmailResponse> AddAlarmEmailAsync(string email, string[] levels)
    {
        // 简化实现
        return new AlarmEmailResponse
        {
            Id = Guid.NewGuid().ToString(),
            Email = email,
            Enabled = true,
            Levels = levels.ToList(),
            Message = "邮箱添加成功"
        };
    }

    public async Task<AlarmEmailUpdateResponse> UpdateAlarmEmailAsync(string id, bool enabled, string[] levels)
    {
        // 简化实现
        return new AlarmEmailUpdateResponse
        {
            Success = true,
            Message = "邮箱更新成功"
        };
    }

    public async Task DeleteAlarmEmailAsync(string id)
    {
        // 简化实现
        await Task.CompletedTask;
    }

    public async Task LogOperationAsync(string tenantId, string? userId, string actionType, string? description)
    {
        try
        {
            var log = new OperationLog
            {
                Id = Guid.NewGuid().ToString(),
                TenantId = tenantId,
                UserId = userId,
                ActionType = actionType,
                Description = description,
                OperatedAt = DateTime.Now
            };

            await _db.Insertable(log).ExecuteCommandAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录操作日志失败");
            // 不抛出异常，避免影响主业务
        }
    }

    public async Task<OperationLog> GetOperationLogByIdAsync(string id)
    {
        try
        {
            var query = _db.Queryable<OperationLog>().Where(it => it.Id == id);
            return await query.FirstAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取操作日志失败");
            throw;
        }
    }

    public async Task<ServiceStatusStatsDto> GetServiceStatusStatsAsync()
    {
        try
        {
            _logger.LogInformation("开始获取服务状态统计数据");

            // 1. 统计总用户数：查询ms_auth_applications表的数据量
            var totalUsers = await _db.Queryable<AuthApplication>().CountAsync();

            // 2. 统计活跃用户：查询ms_operation_logs表的数据，按TenantId字段去重，统计数据量
            var activeUsers = await _db.Queryable<OperationLog>()
                .GroupBy(log => log.TenantId)
                .Select(log => log.TenantId)
                .CountAsync();

            // 3. 统计今日访问量：查询ms_operation_logs表的数据，查询当天的总数据量
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);
            var todayVisits = await _db.Queryable<OperationLog>()
                .Where(log => log.OperatedAt >= today && log.OperatedAt < tomorrow)
                .CountAsync();

            // 4. 统计待审批申请：查询ms_auth_applications表的数据，按照status=Pending过滤，统计总数据量
            var pendingApplications = await _db.Queryable<AuthApplication>()
                .Where(app => app.Status == "Pending")
                .CountAsync();

            var result = new ServiceStatusStatsDto
            {
                TotalUsers = totalUsers,
                ActiveUsers = activeUsers,
                TodayVisits = todayVisits,
                PendingApplications = pendingApplications
            };

            _logger.LogInformation("成功获取服务状态统计数据：总用户数={TotalUsers}, 活跃用户={ActiveUsers}, 今日访问量={TodayVisits}, 待审批申请={PendingApplications}",
                totalUsers, activeUsers, todayVisits, pendingApplications);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取服务状态统计数据失败");
            throw;
        }
    }

    public async Task<ServiceStatusStatsResponseDto> GetServiceStatusCodeStatsAsync()
    {
        try
        {
            _logger.LogInformation("开始获取运行状况统计数据");

            // 1. 查询ms_operation_logs表的所有数据
            var allLogs = await _db.Queryable<OperationLog>().ToListAsync();
            var totalLogs = allLogs.Count;

            _logger.LogInformation("查询到操作日志总数: {TotalLogs}", totalLogs);

            if (totalLogs == 0)
            {
                return new ServiceStatusStatsResponseDto
                {
                    TotalLogs = 0,
                    StatusCodeStats = new List<StatusCodeStatsDto>(),
                    LastUpdated = DateTime.Now
                };
            }

            // 2. 定义状态码映射
            var statusCodeMapping = new Dictionary<int, string>
            {
                { 200, "正常运行" },
                { 201, "创建成功" },
                { 204, "无内容" },
                { 400, "请求错误" },
                { 401, "未授权" },
                { 403, "禁止访问" },
                { 404, "资源不存在" },
                { 405, "方法不允许" },
                { 500, "服务器内部错误" },
                { 502, "网关错误" },
                { 503, "服务不可用" },
                { 504, "网关超时" }
            };

            // 3. 使用正则表达式从Description字段中提取状态码数字
            var statusCodeCounts = new Dictionary<int, int>();
            var regex = new System.Text.RegularExpressions.Regex(@"\b(200|201|204|400|401|403|404|405|500|502|503|504)\b");

            foreach (var log in allLogs)
            {
                if (!string.IsNullOrEmpty(log.Description))
                {
                    var matches = regex.Matches(log.Description);
                    foreach (System.Text.RegularExpressions.Match match in matches)
                    {
                        if (int.TryParse(match.Value, out int statusCode))
                        {
                            if (statusCodeCounts.ContainsKey(statusCode))
                            {
                                statusCodeCounts[statusCode]++;
                            }
                            else
                            {
                                statusCodeCounts[statusCode] = 1;
                            }
                        }
                    }
                }
            }

            // 4. 统计各个状态码的出现次数和百分比
            var statusCodeStats = new List<StatusCodeStatsDto>();
            foreach (var kvp in statusCodeCounts.OrderBy(x => x.Key))
            {
                var statusCode = kvp.Key;
                var count = kvp.Value;
                var percentage = (double)count / totalLogs * 100;

                // 获取状态名称，如果不在映射中则使用默认描述
                var statusName = statusCodeMapping.TryGetValue(statusCode, out var name)
                    ? name
                    : $"状态码 {statusCode}";

                statusCodeStats.Add(new StatusCodeStatsDto
                {
                    StatusCode = statusCode,
                    StatusName = statusName,
                    Count = count,
                    Percentage = Math.Round(percentage, 2)
                });
            }

            // 5. 构建响应对象
            var result = new ServiceStatusStatsResponseDto
            {
                TotalLogs = totalLogs,
                StatusCodeStats = statusCodeStats,
                LastUpdated = DateTime.Now
            };

            _logger.LogInformation("成功获取运行状况统计数据：总日志数={TotalLogs}, 状态码种类数={StatusCodeCount}",
                totalLogs, statusCodeStats.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取运行状况统计数据失败");
            throw;
        }
    }

    public async Task<SixMonthUsageStatsResponseDto> GetSixMonthUsageStatsAsync()
    {
        try
        {
            _logger.LogInformation("开始获取近6个月使用率统计数据");

            // 1. 计算当前日期往前推5个月的月份范围（包括本月，共6个月）
            var currentDate = DateTime.Now;
            var monthlyData = new List<MonthlyUsageDataDto>();
            var totalOperations = 0;

            // 生成近6个月的月份列表
            for (int i = 5; i >= 0; i--)
            {
                var targetDate = currentDate.AddMonths(-i);
                var year = targetDate.Year;
                var month = targetDate.Month;
                
                // 计算该月的开始和结束时间
                var monthStart = new DateTime(year, month, 1);
                var monthEnd = monthStart.AddMonths(1).AddDays(-1);
                
                // 查询该月的操作总量
                var operations = await _db.Queryable<OperationLog>()
                    .Where(log => log.OperatedAt >= monthStart && log.OperatedAt <= monthEnd)
                    .CountAsync();
                
                totalOperations += operations;
                
                // 查询该月的活跃租户数（按TenantId去重）
                var activeTenants = await _db.Queryable<OperationLog>()
                    .Where(log => log.OperatedAt >= monthStart && log.OperatedAt <= monthEnd)
                    .GroupBy(log => log.TenantId)
                    .Select(log => log.TenantId)
                    .CountAsync();
                
                // 添加到月度数据列表
                monthlyData.Add(new MonthlyUsageDataDto
                {
                    Month = $"{year}-{month.ToString("D2")}",
                    Operations = operations,
                    ActiveTenants = activeTenants
                });
            }

            // 2. 计算近6个月的平均月操作量
            var averageOperations = monthlyData.Count > 0 ? (double)totalOperations / monthlyData.Count : 0;

            // 3. 计算相比上一个6个月期间的增长率
            double? growthRate = null;
            try
            {
                // 计算上一个6个月期间的总操作量
                var prevTotalOperations = 0;
                for (int i = 11; i >= 6; i--)
                {
                    var targetDate = currentDate.AddMonths(-i);
                    var year = targetDate.Year;
                    var month = targetDate.Month;
                    
                    var monthStart = new DateTime(year, month, 1);
                    var monthEnd = monthStart.AddMonths(1).AddDays(-1);
                    
                    var operations = await _db.Queryable<OperationLog>()
                        .Where(log => log.OperatedAt >= monthStart && log.OperatedAt <= monthEnd)
                        .CountAsync();
                    
                    prevTotalOperations += operations;
                }

                // 计算增长率
                if (prevTotalOperations > 0)
                {
                    growthRate = ((double)(totalOperations - prevTotalOperations) / prevTotalOperations) * 100;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "计算增长率时发生错误，将不包含增长率数据");
            }

            // 4. 构建响应对象
            var result = new SixMonthUsageStatsResponseDto
            {
                MonthlyData = monthlyData,
                TotalOperations = totalOperations,
                AverageOperations = Math.Round(averageOperations, 2),
                GrowthRate = growthRate.HasValue ? Math.Round(growthRate.Value, 2) : null
            };

            _logger.LogInformation("成功获取近6个月使用率统计数据：总操作量={TotalOperations}, 平均月操作量={AverageOperations}, 增长率={GrowthRate}%",
                totalOperations, averageOperations, growthRate);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取近6个月使用率统计数据失败");
            throw;
        }
    }

    public async Task<List<TenantDataDto>> GetAllTenantsAsync()
    {
        try
        {
            _logger.LogInformation("开始获取所有租户信息");

            // 执行SQL查询：select TenantId, TenantName from ms_auth_applications
            var tenants = await _db.Queryable<TenantDataDto>().ToListAsync();

            _logger.LogInformation("成功获取所有租户信息，共{Count}个租户", tenants.Count);

            return tenants;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有租户信息失败");
            throw;
        }
    }

    /// <summary>
    /// 获取日统计数据，包括租户总数、平均日操作次数、最大日操作数和操作总量
    /// </summary>
    /// <param name="tenantName">租户名称，可选参数。当为空时表示统计所有租户的数据</param>
    /// <returns>日统计数据传输对象</returns>
    public async Task<DailyStatsDTO> GetDailyStatsAsync(string? tenantName = null)
    {
        try
        {
            _logger.LogInformation("开始获取日统计数据");

            // 1. 租户总数：如果指定了租户名称，则为1；否则统计ms_auth_applications表的数据量
            int totalTenants;
            if (!string.IsNullOrEmpty(tenantName))
            {
                totalTenants = 1;
                _logger.LogInformation("指定租户名称，租户总数设为1");
            }
            else
            {
                totalTenants = await _db.Queryable<AuthApplication>().CountAsync();
                _logger.LogInformation("租户总数统计完成: {TotalTenants}", totalTenants);
            }

            // 2. 平均日操作次数：按天汇聚统计操作数，然后求平均值
            // 使用更简单的方法：先获取所有数据，然后在内存中分组
            _logger.LogInformation("开始统计每日操作数据");
            
            // 获取操作日志，根据租户名称进行过滤
            var query = _db.Queryable<OperationLog>();
            
            if (!string.IsNullOrEmpty(tenantName))
            {
                // 根据需求，通过租户名称赋值UserId字段去过滤
                query = query.Where(log => log.UserId == tenantName);
                _logger.LogInformation("按租户名称过滤，使用UserId字段: {TenantName}", tenantName);
            }
            
            var allOperationLogs = await query.ToListAsync();
            _logger.LogInformation("查询到原始操作日志数据: {Count}条", allOperationLogs.Count);

            // 在内存中按日期分组统计
            var dailyStats = allOperationLogs
                .GroupBy(log => new DateTime(log.OperatedAt.Year, log.OperatedAt.Month, log.OperatedAt.Day))
                .Select(g => new { Date = g.Key, Count = g.Count() })
                .ToList();
            
            _logger.LogInformation("按日期分组后的统计数据: {Count}天", dailyStats.Count);

            // 计算平均日操作次数
            int averageDailyOperations = 0;
            if (dailyStats.Count > 0)
            {
                averageDailyOperations = (int)dailyStats.Average(d => d.Count);
                _logger.LogInformation("平均日操作次数: {AverageDailyOperations}", averageDailyOperations);
            }

            // 3. 最大日操作数：按天汇聚统计，找出最大值
            int maxDailyOperations = 0;
            if (dailyStats.Count > 0)
            {
                maxDailyOperations = dailyStats.Max(d => d.Count);
                _logger.LogInformation("最大日操作数: {MaxDailyOperations}", maxDailyOperations);
            }

            // 4. 操作总量：统计ms_operation_logs表当天的总数据量
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);
            var totalOperationsQuery = _db.Queryable<OperationLog>()
                .Where(log => log.OperatedAt >= today && log.OperatedAt < tomorrow);
                
            if (!string.IsNullOrEmpty(tenantName))
            {
                // 根据需求，通过租户名称赋值UserId字段去过滤
                totalOperationsQuery = totalOperationsQuery.Where(log => log.UserId == tenantName);
                _logger.LogInformation("按租户名称过滤今日操作总量，使用UserId字段: {TenantName}", tenantName);
            }
        public async Task<List<TenantOperationDistributionItem>> GetTenantOperationDistributionAsync(string? tenantName = null)
        {
            try
            {
                var tenants = await GetAllTenantsAsync();
                var tenantMap = tenants.ToDictionary(t => t.TenantId, t => t.TenantName);
                
                var query = _db.Queryable<OperationLog>();
                if (!string.IsNullOrEmpty(tenantName))
                {
                    query = query.Where(log => log.TenantId == tenantName);
                    public async Task<List<TenantOperationDistributionItem>> GetTenantOperationDistributionAsync(string? tenantName = null)
                    {
                        try
                        {
                            var tenants = await GetAllTenantsAsync();
                            var tenantMap = tenants.ToDictionary(t => t.TenantId, t => t.TenantName);
                            
                            var query = _db.Queryable<OperationLog>();
                            if (!string.IsNullOrEmpty(tenantName))
                            {
                                query = query.Where(log => log.TenantId == tenantName);
                            }
                        public async Task<List<TenantOperationDistributionItem>> GetTenantOperationDistributionAsync(string? tenantName = null)
                        {
                            try
                            {
                                var tenants = await GetAllTenantsAsync();
                                var tenantMap = tenants.ToDictionary(t => t.TenantId, t => t.TenantName);
                                
                                var query = _db.Queryable<OperationLog>();
                                if (!string.IsNullOrEmpty(tenantName))
                                {
                                    query = query.Where(log => log.TenantId == tenantName);
                                }
                    
                                var logs = await query.ToListAsync();
                                var distribution = logs.GroupBy(log => log.TenantId)
                                    .Select(g => new TenantOperationDistributionItem
                                    {
                                        TenantName = tenantMap.TryGetValue(g.Key, out var name) ? name : g.Key,
                                        OperationCount = g.Count()
                                    })
                                    .ToList();
                    
                                return distribution;
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "获取租户操作分布统计失败");
                                throw;
                            }
                        }
                
                            var logs = await query.ToListAsync();
                            var distribution = logs.GroupBy(log => log.TenantId)
                                .Select(g => new TenantOperationDistributionItem
                                {
                                    TenantName = tenantMap.TryGetValue(g.Key, out var name) ? name : g.Key,
                                    OperationCount = g.Count()
                                })
                                .ToList();
                
                            return distribution;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "获取租户操作分布统计失败");
                            throw;
                        }
                    }
                }
    
                var logs = await query.ToListAsync();
                var distribution = logs.GroupBy(log => log.TenantId)
                    .Select(g => new TenantOperationDistributionItem
                    {
                        TenantName = tenantMap.TryGetValue(g.Key, out var name) ? name : g.Key,
                        OperationCount = g.Count()
                    })
                    .ToList();
    
                return distribution;
                public async Task<List<TenantOperationDistributionItem>> GetTenantOperationDistributionAsync(string? tenantName = null)
                {
                    try
                    {
                        var tenants = await GetAllTenantsAsync();
                        var tenantMap = tenants.ToDictionary(t => t.TenantId, t => t.TenantName);
                        
                        var query = _db.Queryable<OperationLog>();
                        if (!string.IsNullOrEmpty(tenantName))
                        {
                            query = query.Where(log => log.TenantId == tenantName);
                            public async Task<List<TenantOperationDistributionItem>> GetTenantOperationDistributionAsync(string? tenantName = null)
                            {
                                try
                                {
                                    var tenants = await GetAllTenantsAsync();
                                    var tenantMap = tenants.ToDictionary(t => t.TenantId, t => t.TenantName);
                                    
                                    var query = _db.Queryable<OperationLog>();
                                    if (!string.IsNullOrEmpty(tenantName))
                                    {
                                        query = query.Where(log => log.TenantId == tenantName);
                                    }
                        
                                    var logs = await query.ToListAsync();
                                    var distribution = logs.GroupBy(log => log.TenantId)
                                        .Select(g => new TenantOperationDistributionItem
                                        {
                                            TenantName = tenantMap.TryGetValue(g.Key, out var name) ? name : g.Key,
                                            OperationCount = g.Count()
                                        })
                                        .ToList();
                        
                                    return distribution;
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogError(ex, "获取租户操作分布统计失败");
                                    throw;
                                }
                            }
                        }
            
                        var logs = await query.ToListAsync();
                        var distribution = logs.GroupBy(log => log.TenantId)
                            .Select(g => new TenantOperationDistributionItem
                            {
                                TenantName = tenantMap.TryGetValue(g.Key, out var name) ? name : g.Key,
                                OperationCount = g.Count()
                            })
                            .ToList();
            
                        return distribution;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "获取租户操作分布统计失败");
                        throw;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取租户操作分布统计失败");
                throw;
            }
        }
                
            var totalOperations = await totalOperationsQuery.CountAsync();
            _logger.LogInformation("今日操作总量: {TotalOperations}", totalOperations);

            // 构建响应对象
            var result = new DailyStatsDTO
            {
                TotalTenants = totalTenants,
                AverageDailyOperations = averageDailyOperations,
                MaxDailyOperations = maxDailyOperations,
                TotalOperations = totalOperations
            };

            _logger.LogInformation("成功获取日统计数据：租户总数={TotalTenants}, 平均日操作次数={AverageDailyOperations}, 最大日操作数={MaxDailyOperations}, 操作总量={TotalOperations}",
                totalTenants, averageDailyOperations, maxDailyOperations, totalOperations);

            return result;
            public async Task<List<TenantOperationDistributionItem>> GetTenantOperationDistributionAsync(string? tenantName = null)
            {
                try
                {
                    var tenants = await GetAllTenantsAsync();
                    var tenantMap = tenants.ToDictionary(t => t.TenantId, t => t.TenantName);
                    
                    var query = _db.Queryable<OperationLog>();
                    if (!string.IsNullOrEmpty(tenantName))
                    {
                        query = query.Where(log => log.TenantId == tenantName);
                        public async Task<List<TenantOperationDistributionItem>> GetTenantOperationDistributionAsync(string? tenantName = null)
                        {
                            try
                            {
                                var tenants = await GetAllTenantsAsync();
                                var tenantMap = tenants.ToDictionary(t => t.TenantId, t => t.TenantName);
                                
                                var query = _db.Queryable<OperationLog>();
                                if (!string.IsNullOrEmpty(tenantName))
                                {
                                    query = query.Where(log => log.TenantId == tenantName);
                                    public async Task<List<TenantOperationDistributionItem>> GetTenantOperationDistributionAsync(string? tenantName = null)
                                    {
                                        try
                                        {
                                            var tenants = await GetAllTenantsAsync();
                                            var tenantMap = tenants.ToDictionary(t => t.TenantId, t => t.TenantName);
                                            
                                            var query = _db.Queryable<OperationLog>();
                                            if (!string.IsNullOrEmpty(tenantName))
                                            {
                                                query = query.Where(log => log.TenantId == tenantName);
                                            }
                                
                                            var logs = await query.ToListAsync();
                                            var distribution = logs.GroupBy(log => log.TenantId)
                                                .Select(g => new TenantOperationDistributionItem
                                                {
                                                    TenantName = tenantMap.TryGetValue(g.Key, out var name) ? name : g.Key,
                                                    OperationCount = g.Count()
                                                })
                                                .ToList();
                                
                                            return distribution;
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger.LogError(ex, "获取租户操作分布统计失败");
                                            throw;
                                        }
                                    }
                                }
                    
                                var logs = await query.ToListAsync();
                                var distribution = logs.GroupBy(log => log.TenantId)
                                    .Select(g => new TenantOperationDistributionItem
                                    {
                                        TenantName = tenantMap.TryGetValue(g.Key, out var name) ? name : g.Key,
                                        OperationCount = g.Count()
                                    })
                                    .ToList();
                    
                                return distribution;
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "获取租户操作分布统计失败");
                                throw;
                            }
                        }
                    }
                public async Task<List<TenantOperationDistributionItem>> GetTenantOperationDistributionAsync(string? tenantName = null)
                {
                    try
                    {
                        var tenants = await GetAllTenantsAsync();
                        var tenantMap = tenants.ToDictionary(t => t.TenantId, t => t.TenantName);
                        
                        var query = _db.Queryable<OperationLog>();
                        if (!string.IsNullOrEmpty(tenantName))
                        {
                            query = query.Where(log => log.TenantId == tenantName);
                        }
            
                        var logs = await query.ToListAsync();
                        var distribution = logs.GroupBy(log => log.TenantId)
                            .Select(g => new TenantOperationDistributionItem
                            {
                                TenantName = tenantMap.TryGetValue(g.Key, out var name) ? name : g.Key,
                                OperationCount = g.Count()
                            })
                            .ToList();
            
                        return distribution;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "获取租户操作分布统计失败");
                        throw;
                    }
                }
        
                    var logs = await query.ToListAsync();
                    var distribution = logs.GroupBy(log => log.TenantId)
                        .Select(g => new TenantOperationDistributionItem
                        {
                            TenantName = tenantMap.TryGetValue(g.Key, out var name) ? name : g.Key,
                            OperationCount = g.Count()
                        })
                        .ToList();
        
                    return distribution;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取租户操作分布统计失败");
                    throw;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取日统计数据失败");
            throw;
            public async Task<List<TenantOperationDistributionItem>> GetTenantOperationDistributionAsync(string? tenantName = null)
            {
                try
                {
                    // 获取所有租户信息
                    var tenants = await GetAllTenantsAsync();
                    var tenantMap = tenants.ToDictionary(t => t.TenantId, t => t.TenantName);
        
                    // 查询操作日志
                    var query = _db.Queryable<OperationLog>();
                    if (!string.IsNullOrEmpty(tenantName))
                    {
                        query = query.Where(log => log.TenantId == tenantName);
                    }
        
                    var logs = await query.ToListAsync();
                    var distribution = logs.GroupBy(log => log.TenantId)
                        .Select(g => new TenantOperationDistributionItem
                        {
                            TenantName = tenantMap.TryGetValue(g.Key, out var name) ? name : g.Key,
                            OperationCount = g.Count()
                        })
                        .ToList();
        
                    return distribution;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取租户操作分布统计失败");
                    throw;
                }
            }
        }
        public async Task<List<TenantOperationDistributionItem>> GetTenantOperationDistributionAsync(string? tenantName = null)
        {
            try
            {
                // 获取所有租户信息
                var tenants = await GetAllTenantsAsync();
                var tenantMap = tenants.ToDictionary(t => t.TenantId, t => t.TenantName);
    
                // 查询操作日志
                var query = _db.Queryable<OperationLog>();
                if (!string.IsNullOrEmpty(tenantName))
                {
                    query = query.Where(log => log.TenantId == tenantName);
                }
    
                var logs = await query.ToListAsync();
                var distribution = logs.GroupBy(log => log.TenantId)
                    .Select(g => new TenantOperationDistributionItem
                    {
                        TenantName = tenantMap.TryGetValue(g.Key, out var name) ? name : g.Key,
                        OperationCount = g.Count()
                    })
                    .ToList();
    
                return distribution;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取租户操作分布统计失败");
                throw;
            }
        }
    }
        public async Task<List<TenantOperationDistributionItem>> GetTenantOperationDistributionAsync(string? tenantName = null)
        {
            try
            {
                // 获取所有租户信息
                var tenants = await GetAllTenantsAsync();
                var tenantMap = tenants.ToDictionary(t => t.TenantId, t => t.TenantName);
    
                // 查询操作日志
                var query = _db.Queryable<OperationLog>();
                if (!string.IsNullOrEmpty(tenantName))
                {
                    query = query.Where(log => log.TenantId == tenantName);
                }
    
                var logs = await query.ToListAsync();
                var distribution = logs.GroupBy(log => log.TenantId)
                    .Select(g => new TenantOperationDistributionItem
                    {
                        TenantName = tenantMap.TryGetValue(g.Key, out var name) ? name : g.Key,
                        OperationCount = g.Count()
                    })
                    .ToList();
    
                return distribution;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取租户操作分布统计失败");
                throw;
            }
        }
    }
    public async Task<List<TenantOperationDistributionItem>> GetTenantOperationDistributionAsync(string? tenantName = null)
    {
        try
        {
            // 获取所有租户信息
            var tenants = await GetAllTenantsAsync();
            var tenantMap = tenants.ToDictionary(t => t.TenantId, t => t.TenantName);

            // 查询操作日志
            var query = _db.Queryable<OperationLog>();
            if (!string.IsNullOrEmpty(tenantName))
            {
                query = query.Where(log => log.TenantId == tenantName);
            }

            var logs = await query.ToListAsync();
            var distribution = logs.GroupBy(log => log.TenantId)
                .Select(g => new TenantOperationDistributionItem
                {
                    TenantName = tenantMap.TryGetValue(g.Key, out var name) ? name : g.Key,
                    OperationCount = g.Count()
                })
                .ToList();

            return distribution;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取租户操作分布统计失败");
            throw;
        }
    }
    }
    public async Task<List<TenantOperationDistributionItem>> GetTenantOperationDistributionAsync(string? tenantName = null)
    {
        try
        {
            // 获取所有租户信息
            var tenants = await GetAllTenantsAsync();
            var tenantMap = tenants.ToDictionary(t => t.TenantId, t => t.TenantName);

            // 查询操作日志
            var query = _db.Queryable<OperationLog>();
            if (!string.IsNullOrEmpty(tenantName))
            {
                query = query.Where(log => log.TenantId == tenantName);
            }

            var logs = await query.ToListAsync();
            var distribution = logs.GroupBy(log => log.TenantId)
                .Select(g => new TenantOperationDistributionItem
                {
                    TenantName = tenantMap.TryGetValue(g.Key, out var name) ? name : g.Key,
                    OperationCount = g.Count()
                })
                .ToList();

            return distribution;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取租户操作分布统计失败");
            throw;
        }
    }
}