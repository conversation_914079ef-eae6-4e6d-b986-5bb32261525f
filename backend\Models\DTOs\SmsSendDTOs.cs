using System;
using System.Xml.Serialization;

namespace MapStudio.Api.Models.DTOs
{
    /// <summary>
    /// 发送短信请求DTO
    /// </summary>
    public class SendSmsRequestDto
    {
        /// <summary>
        /// 短信类型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 模块
        /// </summary>
        public string Modular { get; set; }

        /// <summary>
        /// 功能
        /// </summary>
        public string Function { get; set; }

        /// <summary>
        /// 接收人姓名
        /// </summary>
        public string ReceiveMans { get; set; }

        /// <summary>
        /// 接收人手机号
        /// </summary>
        public string ReceivePns { get; set; }

        /// <summary>
        /// 抄送人姓名
        /// </summary>
        public string CcMans { get; set; }

        /// <summary>
        /// 抄送人手机号
        /// </summary>
        public string CcPns { get; set; }

        /// <summary>
        /// 发送人
        /// </summary>
        public string SendMan { get; set; }

        /// <summary>
        /// 短信内容
        /// </summary>
        public string Message { get; set; }
    }

    /// <summary>
    /// 发送告警短信请求DTO
    /// </summary>
    public class SendAlertSmsRequestDto
    {
        /// <summary>
        /// 手机号
        /// </summary>
        public string PhoneNumber { get; set; }

        /// <summary>
        /// 级别
        /// </summary>
        public string Level { get; set; }
    }

    /// <summary>
    /// 发送短信响应DTO
    /// </summary>
    public class SendSmsResponseDto
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 响应消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 响应数据
        /// </summary>
        public object Data { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public string ErrorCode { get; set; }
    }

    /// <summary>
    /// 用于生成华为WebService接口所需的XML格式数据
    /// </summary>
    [XmlRoot("Sms")]
    public class XmlSmsRequest
    {
        /// <summary>
        /// 短信类型
        /// </summary>
        [XmlElement("type")]
        public string Type { get; set; }

        /// <summary>
        /// 模块
        /// </summary>
        [XmlElement("modular")]
        public string Modular { get; set; }

        /// <summary>
        /// 功能
        /// </summary>
        [XmlElement("function")]
        public string Function { get; set; }

        /// <summary>
        /// 接收人姓名
        /// </summary>
        [XmlElement("receiveMans")]
        public string ReceiveMans { get; set; }

        /// <summary>
        /// 接收人手机号
        /// </summary>
        [XmlElement("receivePns")]
        public string ReceivePns { get; set; }

        /// <summary>
        /// 抄送人姓名
        /// </summary>
        [XmlElement("ccMans")]
        public string CcMans { get; set; }

        /// <summary>
        /// 抄送人手机号
        /// </summary>
        [XmlElement("ccPns")]
        public string CcPns { get; set; }

        /// <summary>
        /// 发送人
        /// </summary>
        [XmlElement("sendMan")]
        public string SendMan { get; set; }

        /// <summary>
        /// 短信内容
        /// </summary>
        [XmlElement("message")]
        public string Message { get; set; }
    }
}