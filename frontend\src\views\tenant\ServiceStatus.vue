<template>
  <div class="p-6 space-y-8">
    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <i class="fa-solid fa-circle-exclamation text-red-400"></i>
        </div>
        <div class="ml-3">
          <p class="text-sm text-red-700">
            {{ error }}
          </p>
        </div>
        <div class="ml-auto pl-3">
          <div class="-mx-1.5 -my-1.5">
            <button @click="error = null" class="inline-flex bg-red-50 rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-600">
              <i class="fa-solid fa-xmark"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
    <!-- 页面标题 -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">服务状态监控平台</h1>
        <p class="mt-1 text-gray-500">使用情况</p>
      </div>
      <div class="mt-4 md:mt-0">
        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
          <i class="fa-solid fa-calendar mr-2"></i>
          {{ currentDate }}
        </span>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">总用户数</p>
            <h3 class="mt-1 text-2xl font-bold text-gray-900">
              {{ loading ? '加载中...' : serviceStatusStats.totalUsers }}
            </h3>
          </div>
          <div class="p-3 bg-blue-100 rounded-lg">
            <i class="fa-solid fa-building text-blue-600 text-xl"></i>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">活跃用户</p>
            <h3 class="mt-1 text-2xl font-bold text-gray-900">
              {{ loading ? '加载中...' : serviceStatusStats.activeUsers.toLocaleString() }}
            </h3>
          </div>
          <div class="p-3 bg-purple-100 rounded-lg">
            <i class="fa-solid fa-eye text-purple-600 text-xl"></i>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">今日访问量</p>
            <h3 class="mt-1 text-2xl font-bold text-gray-900">
              {{ loading ? '加载中...' : serviceStatusStats.todayVisits }}
            </h3>
          </div>
          <div class="p-3 bg-yellow-100 rounded-lg">
            <i class="fa-solid fa-clock text-yellow-600 text-xl"></i>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">待审批申请</p>
            <h3 class="mt-1 text-2xl font-bold text-gray-900">
              {{ loading ? '加载中...' : serviceStatusStats.pendingApplications }}
            </h3>
          </div>
          <div class="p-3 bg-green-100 rounded-lg">
            <i class="fa-solid fa-shield text-green-600 text-xl"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div class="lg:col-span-2 bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-lg font-semibold text-gray-900">使用率 (近6个月)</h2>
          <div v-if="usageStatsLoading" class="flex items-center text-sm text-gray-500">
            <i class="fa-solid fa-spinner fa-spin mr-2"></i>
            加载中...
          </div>
        </div>
        
        <!-- 使用率统计错误提示 -->
        <div v-if="usageStatsError" class="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div class="flex">
            <div class="flex-shrink-0">
              <i class="fa-solid fa-circle-exclamation text-red-400"></i>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-700">
                {{ usageStatsError }}
              </p>
            </div>
            <div class="ml-auto pl-3">
              <div class="-mx-1.5 -my-1.5">
                <button @click="usageStatsError = null" class="inline-flex bg-red-50 rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-600">
                  <i class="fa-solid fa-xmark"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <div class="h-80">
          <ChartBase :option="trendChartOption" />
        </div>
      </div>

      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-lg font-semibold text-gray-900">运行状况</h2>
          <div v-if="statusStatsLoading" class="flex items-center text-sm text-gray-500">
            <i class="fa-solid fa-spinner fa-spin mr-2"></i>
            加载中...
          </div>
        </div>
        
        <!-- 运行状况统计错误提示 -->
        <div v-if="statusStatsError" class="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div class="flex">
            <div class="flex-shrink-0">
              <i class="fa-solid fa-circle-exclamation text-red-400"></i>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-700">
                {{ statusStatsError }}
              </p>
            </div>
            <div class="ml-auto pl-3">
              <div class="-mx-1.5 -my-1.5">
                <button @click="statusStatsError = null" class="inline-flex bg-red-50 rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-600">
                  <i class="fa-solid fa-xmark"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <div class="h-64 flex items-center justify-center">
          <ChartBase
            v-if="!statusStatsLoading && tenantStatusData.length > 0"
            :option="pieChartOption"
            :loading="statusStatsLoading"
            @chart-ready="handlePieChartReady"
            @chart-error="handlePieChartError"
          />
          <div v-else-if="statusStatsLoading" class="flex flex-col items-center justify-center text-gray-500">
            <i class="fa-solid fa-spinner fa-spin text-2xl mb-2"></i>
            <p>加载运行状况数据...</p>
          </div>
          <div v-else-if="tenantStatusData.length === 0" class="flex flex-col items-center justify-center text-gray-500">
            <i class="fa-solid fa-chart-pie text-2xl mb-2"></i>
            <p>暂无运行状况数据</p>
          </div>
        </div>
        <div v-if="!statusStatsLoading && tenantStatusData.length > 0" class="grid grid-cols-1 gap-3 mt-4">
          <div v-for="(status, index) in tenantStatusData" :key="index" class="flex items-center">
            <div
              class="w-3 h-3 rounded-full mr-2"
              :style="{ backgroundColor: status.color }"
            ></div>
            <span class="text-sm text-gray-700">{{ status.name }}</span>
            <span class="ml-auto text-sm font-medium text-gray-900">{{ status.value }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速访问卡片 -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100" v-if="false">
      <h2 class="text-lg font-semibold text-gray-900 mb-6">快速访问</h2>
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <router-link
          to="/tenant/auth-apply"
          class="p-5 border border-gray-100 rounded-lg hover:border-blue-200 hover:bg-blue-50 transition-colors duration-200"
        >
          <div class="flex items-start">
            <div class="p-3 bg-blue-100 rounded-lg mr-4">
              <i class="fa-solid fa-file-signature text-blue-600"></i>
            </div>
            <div>
              <h3 class="font-medium text-gray-900">租户授权申请</h3>
              <p class="mt-1 text-sm text-gray-500">提交新的租户授权申请</p>
            </div>
          </div>
        </router-link>

        <router-link
          to="/tenant/auth-approval-list"
          class="p-5 border border-gray-100 rounded-lg hover:border-blue-200 hover:bg-blue-50 transition-colors duration-200"
        >
          <div class="flex items-start">
            <div class="p-3 bg-purple-100 rounded-lg mr-4">
              <i class="fa-solid fa-list-check text-purple-600"></i>
            </div>
            <div>
              <h3 class="font-medium text-gray-900">待审批清单</h3>
              <p class="mt-1 text-sm text-gray-500">查看和处理待审批申请</p>
            </div>
          </div>
        </router-link>

        <router-link
          to="/tenant/service-status"
          class="p-5 border border-gray-100 rounded-lg hover:border-blue-200 hover:bg-blue-50 transition-colors duration-200"
        >
          <div class="flex items-start">
            <div class="p-3 bg-green-100 rounded-lg mr-4">
              <i class="fa-solid fa-heartbeat text-green-600"></i>
            </div>
            <div>
              <h3 class="font-medium text-gray-900">服务状态监控</h3>
              <p class="mt-1 text-sm text-gray-500">查看租户服务运行状态</p>
            </div>
          </div>
        </router-link>

        <router-link
          to="/operation/access-stats"
          class="p-5 border border-gray-100 rounded-lg hover:border-blue-200 hover:bg-blue-50 transition-colors duration-200"
        >
          <div class="flex items-start">
            <div class="p-3 bg-orange-100 rounded-lg mr-4">
              <i class="fa-solid fa-chart-line text-orange-600"></i>
            </div>
            <div>
              <h3 class="font-medium text-gray-900">访问统计报表</h3>
              <p class="mt-1 text-sm text-gray-500">查看平台访问统计数据</p>
            </div>
          </div>
        </router-link>
      </div>
    </div>

    <!-- 最近申请 -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100" v-if="false">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-gray-900">最近授权申请</h2>
        <router-link to="/tenant/auth-approval-list" class="text-sm text-blue-600 hover:text-blue-800">
          查看全部 <i class="fa-solid fa-angle-right ml-1"></i>
        </router-link>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请单号</th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">租户名称</th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请日期</th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="application in recentApplicationsData" :key="application.id" class="hover:bg-gray-50">
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">{{ application.id }}</td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">{{ application.name }}</td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">{{ application.date }}</td>
              <td class="px-3 py-4 whitespace-nowrap">
                <span :class="getStatusClass(application.status)">
                  {{ application.status }}
                </span>
              </td>
              <td class="px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
                <router-link
                  :to="`/tenant/auth-approval/${application.id}`"
                  class="text-blue-600 hover:text-blue-900 mr-3"
                >
                  查看详情
                </router-link>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import ChartBase from '@/components/ChartBase.vue'
import { OperationService } from '@/services/operation.service'
import type { ServiceStatusStats, SixMonthUsageStatsResponse, ServiceStatusStatsResponse, StatusCodeStats } from '@/services/types'

// 使用率数据
const usageStatsData = ref<SixMonthUsageStatsResponse>({
  monthlyData: [],
  totalOperations: 0,
  averageOperations: 0,
  growthRate: 0
})

// 运行状况数据（从API获取）
const tenantStatusData = ref<{ name: string; value: number; color: string; statusCode?: number }[]>([])

// 状态码到中文显示和颜色的映射
const statusCodeMapping = computed(() => ({
  200: { name: '正常运行', color: '#00B42A' },
  404: { name: '资源不存在', color: '#F53F3F' },
  500: { name: '服务器错误', color: '#F53F3F' },
  403: { name: '权限不足', color: '#FF7D00' },
  401: { name: '未授权', color: '#FF7D00' },
  400: { name: '请求错误', color: '#FF7D00' },
  408: { name: '请求超时', color: '#FF7D00' },
  502: { name: '网关错误', color: '#F53F3F' },
  503: { name: '服务不可用', color: '#F53F3F' },
  // 默认映射
  default: { name: '其他状态', color: '#86909C' }
}))

const recentApplicationsData = ref([
  { id: 'TA-20250801', name: '智慧城市科技有限公司', date: '2025-08-01', status: '已通过' },
  { id: 'TA-20250802', name: '未来交通研究院', date: '2025-08-02', status: '待审批' },
  { id: 'TA-20250803', name: '绿色能源集团', date: '2025-08-03', status: '审批中' },
])

// 当前日期
const currentDate = computed(() => {
  return new Date().toLocaleDateString('zh-CN', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric',
    weekday: 'long'
  })
})

// 图表加载状态
const chartLoading = ref(true)
const usageStatsLoading = ref(false)
const usageStatsError = ref<string | null>(null)
const statusStatsLoading = ref(false)
const statusStatsError = ref<string | null>(null)

// 服务状态统计数据
const serviceStatusStats = ref<ServiceStatusStats>({
  totalUsers: 0,
  activeUsers: 0,
  todayVisits: 0,
  pendingApplications: 0
})

// 数据加载状态
const loading = ref(false)
const error = ref<string | null>(null)

// 趋势图表配置
const trendChartOption = computed(() => ({
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: usageStatsData.value.monthlyData.map(item => item.month),
    axisLine: {
      lineStyle: {
        color: '#9ca3af'
      }
    },
    axisLabel: {
      fontSize: 12,
      color: '#9ca3af'
    }
  },
  yAxis: {
    type: 'value',
    axisLine: {
      lineStyle: {
        color: '#9ca3af'
      }
    },
    axisLabel: {
      fontSize: 12,
      color: '#9ca3af'
    },
    splitLine: {
      lineStyle: {
        color: '#f0f0f0',
        type: 'dashed'
      }
    }
  },
  series: [{
    name: '操作量',
    data: usageStatsData.value.monthlyData.map(item => item.operations),
    type: 'line',
    smooth: true,
    lineStyle: {
      color: '#165DFF',
      width: 2
    },
    itemStyle: {
      color: '#165DFF'
    },
    areaStyle: {
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [{
          offset: 0, color: 'rgba(22, 93, 255, 0.1)'
        }, {
          offset: 1, color: 'rgba(22, 93, 255, 0.01)'
        }]
      }
    }
  }],
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderColor: '#e5e7eb',
    borderWidth: 1,
    textStyle: {
      color: '#374151'
    },
    formatter: (params: any) => {
      const dataIndex = params[0].dataIndex
      const monthData = usageStatsData.value.monthlyData[dataIndex]
      return `${monthData.month}<br/>操作量: ${monthData.operations}${monthData.activeTenants !== undefined ? '<br/>活跃租户: ' + monthData.activeTenants : ''}`
    }
  }
}))

// 饼图配置
const pieChartOption = computed(() => {
  console.log('📊 正在生成饼图配置，数据:', tenantStatusData.value)
  
  return {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e5e7eb',
      borderWidth: 1,
      textStyle: {
        color: '#374151'
      },
      formatter: '{b}: {c}% ({d}%)'
    },
    legend: {
      show: false
    },
    series: [{
      name: '运行状况',
      type: 'pie',
      radius: ['60%', '80%'],
      center: ['50%', '50%'],
      data: tenantStatusData.value.map(item => ({
        name: item.name,
        value: item.value,
        itemStyle: {
          color: item.color
        },
        // 确保数据项与图例关联
        legendHoverLink: true,
        selected: true
      })),
      label: {
        show: false
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      // 确保饼图扇区与图例交互
      avoidLabelOverlap: false,
      stillShowZeroSum: true
    }]
  }
})

/**
 * 获取服务状态统计数据
 */
const loadServiceStatusStats = async () => {
  try {
    loading.value = true
    error.value = null
    
    console.log('📊 开始获取服务状态统计数据...')
    const response = await OperationService.getServiceStatusStats()
    
    serviceStatusStats.value = response
    console.log('✅ 服务状态统计数据加载成功:', response)
  } catch (err: any) {
    console.error('❌ 获取服务状态统计数据失败:', err)
    error.value = err.message || '获取服务状态统计数据失败'
    // 重置数据
    serviceStatusStats.value = {
      totalUsers: 0,
      activeUsers: 0,
      todayVisits: 0,
      pendingApplications: 0
    }
  } finally {
    loading.value = false
  }
}

/**
 * 获取运行状况统计数据
 */
const loadServiceStatusCodeStats = async () => {
  try {
    statusStatsLoading.value = true
    statusStatsError.value = null
    
    console.log('📊 开始获取运行状况统计数据...')
    const response = await OperationService.getServiceStatusCodeStats()
    
    console.log('📦 API返回的原始数据:', response)
    
    // 将statusCodeStats数据转换为图表所需的格式
    if (response && response.statusCodeStats && Array.isArray(response.statusCodeStats)) {
      tenantStatusData.value = response.statusCodeStats.map((stat: StatusCodeStats) => {
        console.log('🔄 处理状态码统计数据:', stat)
        
        // 优先使用API返回的statusName，如果没有则使用状态码映射
        const mapping = statusCodeMapping.value[stat.statusCode as keyof typeof statusCodeMapping.value] || statusCodeMapping.value.default
        const name = stat.statusName || mapping.name
        const color = mapping.color
        
        // 确保percentage是数字类型
        const percentage = typeof stat.percentage === 'number' ? stat.percentage : Number(stat.percentage) || 0
        
        console.log(`✅ 转换后数据: name=${name}, value=${percentage}, color=${color}`)
        
        return {
          name: name,
          value: percentage,
          color: color,
          // 添加原始状态码，用于后续处理
          statusCode: stat.statusCode
        }
      })
      
      // 验证转换后的数据
      console.log('✅ 运行状况统计数据加载成功:', tenantStatusData.value)
      console.log('📊 饼图数据验证:', {
        数据长度: tenantStatusData.value.length,
        数据总和: tenantStatusData.value.reduce((sum, item) => sum + item.value, 0),
        数据项: tenantStatusData.value.map(item => `${item.name}: ${item.value}%`)
      })
    } else {
      console.warn('⚠️ 运行状况统计数据格式不正确:', response)
      tenantStatusData.value = []
    }
  } catch (err: any) {
    console.error('❌ 获取运行状况统计数据失败:', err)
    console.error('❌ 错误详情:', {
      message: err.message,
      stack: err.stack,
      response: err.response
    })
    statusStatsError.value = err.message || '获取运行状况统计数据失败'
    // 重置数据
    tenantStatusData.value = []
  } finally {
    statusStatsLoading.value = false
  }
}

/**
 * 获取近6个月使用率统计数据
 */
const loadSixMonthUsageStats = async () => {
  try {
    usageStatsLoading.value = true
    usageStatsError.value = null
    
    console.log('📊 开始获取近6个月使用率统计数据...')
    const response = await OperationService.getSixMonthUsageStats()
    
    usageStatsData.value = response
    console.log('✅ 近6个月使用率统计数据加载成功:', response)
  } catch (err: any) {
    console.error('❌ 获取近6个月使用率统计数据失败:', err)
    usageStatsError.value = err.message || '获取近6个月使用率统计数据失败'
    // 重置数据
    usageStatsData.value = {
      monthlyData: [],
      totalOperations: 0,
      averageOperations: 0,
      growthRate: 0
    }
  } finally {
    usageStatsLoading.value = false
  }
}

// 初始化图表数据
onMounted(() => {
  // 获取服务状态统计数据
  loadServiceStatusStats()
  
  // 获取运行状况统计数据
  loadServiceStatusCodeStats()
  
  // 获取近6个月使用率统计数据
  loadSixMonthUsageStats()
  
  // 模拟图表数据加载
  setTimeout(() => {
    chartLoading.value = false
  }, 500)
})

// 饼图事件处理函数
const handlePieChartReady = (chart: any) => {
  console.log('📊 饼图已准备就绪:', chart)
  console.log('📊 当前饼图配置:', pieChartOption.value)
}

const handlePieChartError = (error: any) => {
  console.error('❌ 饼图加载失败:', error)
  statusStatsError.value = '饼图加载失败，请检查数据格式'
}

// 状态样式
const getStatusClass = (status: string) => {
  const baseClass = 'px-2 py-1 text-xs rounded-full'
  switch (status) {
    case '已通过':
      return `${baseClass} bg-green-100 text-green-800`
    case '待审批':
      return `${baseClass} bg-yellow-100 text-yellow-800`
    case '审批中':
      return `${baseClass} bg-blue-100 text-blue-800`
    default:
      return `${baseClass} bg-gray-100 text-gray-800`
  }
}
</script>