using SqlSugar;
using MapStudio.Api.Models.Entities;
using FileEntity = MapStudio.Api.Models.Entities.File;

namespace MapStudio.Api.Data.Context;

public class DatabaseInitializer
{
    private readonly ISqlSugarClient _db;
    private readonly ILogger<DatabaseInitializer> _logger;

    public DatabaseInitializer(ISqlSugarClient db, ILogger<DatabaseInitializer> logger)
    {
        _db = db;
        _logger = logger;
    }

    public async Task InitializeAsync()
    {
        try
        {
            _logger.LogInformation("开始初始化数据库...");

            // 创建表结构 - 所有表均使用ms_前缀
            _db.CodeFirst.InitTables<User>();         // 创建ms_users表
            _db.CodeFirst.InitTables<AuthApplication>(); // 创建ms_auth_applications表
            _db.CodeFirst.InitTables<OperationLog>(); // 创建ms_operation_logs表
            _db.CodeFirst.InitTables<ServiceType>();  // 创建ms_service_types表
            _db.CodeFirst.InitTables<PermissionScope>(); // 创建ms_permission_scopes表
            _db.CodeFirst.InitTables<FileEntity>();   // 创建ms_files表
            _db.CodeFirst.InitTables<AlarmSetting>(); // 创建ms_alarm_settings表
            _db.CodeFirst.InitTables<Tenant>(); // 创建ms_tenants表

            _logger.LogInformation("数据库表创建完成");

            // 初始化基础数据
            await SeedDataAsync();

            _logger.LogInformation("数据库初始化完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据库初始化失败");
            throw;
        }
    }

    private async Task SeedDataAsync()
    {
        // 检查是否已有数据，避免重复插入
        var existingServiceTypes = await _db.Queryable<ServiceType>().CountAsync();
        if (existingServiceTypes == 0)
        {
            // 初始化服务类型数据
            var serviceTypes = new List<ServiceType>
            {
                new ServiceType { Id = Guid.NewGuid().ToString(), Name = "基础地图服务", Description = "提供基础地图展示、查询功能", IsActive = true },
                new ServiceType { Id = Guid.NewGuid().ToString(), Name = "高级地图服务", Description = "提供高级分析、编辑功能", IsActive = true },
                new ServiceType { Id = Guid.NewGuid().ToString(), Name = "企业级地图服务", Description = "提供企业级功能和优先支持", IsActive = true },
                new ServiceType { Id = Guid.NewGuid().ToString(), Name = "定制化地图服务", Description = "提供定制化开发和集成服务", IsActive = true }
            };

            await _db.Insertable(serviceTypes).ExecuteCommandAsync();
            _logger.LogInformation("服务类型数据初始化完成");
        }

        var existingPermissionScopes = await _db.Queryable<PermissionScope>().CountAsync();
        if (existingPermissionScopes == 0)
        {
            // 初始化权限范围数据
            var permissionScopes = new List<PermissionScope>
            {
                new PermissionScope { Id = Guid.NewGuid().ToString(), Name = "只读权限", Description = "仅可查看和浏览地图数据", Permissions = "read" },
                new PermissionScope { Id = Guid.NewGuid().ToString(), Name = "读写权限", Description = "可查看、编辑和创建地图数据", Permissions = "read,write" },
                new PermissionScope { Id = Guid.NewGuid().ToString(), Name = "管理员权限", Description = "具备完整的管理和配置权限", Permissions = "read,write,admin" }
            };

            await _db.Insertable(permissionScopes).ExecuteCommandAsync();
            _logger.LogInformation("权限范围数据初始化完成");
        }

        var existingUsers = await _db.Queryable<User>().CountAsync();
        if (existingUsers == 0)
        {
            // 初始化默认管理员用户
            var adminUser = new User
            {
                Id = Guid.NewGuid().ToString(),
                Name = "系统管理员",
                Email = "<EMAIL>",
                Role = "admin",
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            await _db.Insertable(adminUser).ExecuteCommandAsync();
            _logger.LogInformation("默认管理员用户创建完成");
        }

        var existingAlarmSettings = await _db.Queryable<AlarmSetting>().CountAsync();
        if (existingAlarmSettings == 0)
        {
            // 初始化默认告警设置
            var defaultAlarmSetting = new AlarmSetting
            {
                Id = Guid.NewGuid().ToString(),
                EmailTemplate = "Map Studio系统告警通知：{{AlarmMessage}}",
                EmailList = "[]",
                IsActive = true,
                UpdatedAt = DateTime.Now
            };

            await _db.Insertable(defaultAlarmSetting).ExecuteCommandAsync();
            _logger.LogInformation("默认告警设置创建完成");
        }
    }
}